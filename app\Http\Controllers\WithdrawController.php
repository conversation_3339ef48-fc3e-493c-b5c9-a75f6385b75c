<?php

namespace App\Http\Controllers;

use App\Constants\LockKey;
use App\Exceptions\DataException;
use App\Models\Currency;
use App\Models\Enums\SysConfigKeyEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\InviteAmountWithdraw;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use App\Jobs\EmailRuleNotice;
use App\Models\Enums\EmailRuleEventEnum;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Constants\QueueKey;
use Throwable;

class WithdrawController extends Controller
{
    /**
     * 邀请提现
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function inviteWithdraw(Request $request): JsonResource
    {
        $validated = $request->validate([
            'amount' => ['required', 'numeric'],
            'currency_code' => ['required', 'string'],
            'payment_account' => ['required', 'array'],
            'email_code' => ['required', 'string'],
            'payment_platform' => ['string', 'required'],
        ]);
        $amount = Arr::get($validated, 'amount');
        $emailCode = Arr::get($validated, 'email_code');

        /**
         * @var $user User|null
         */
        $user = Auth::user();

        // 验证邮箱验证码
        $this->validateEmailCode($user->email, $emailCode);
        //美元提现金额
        $usdAmount = $this->calculateUsdAmount($amount, $validated['currency_code']);

        if ($user->wallet->invite_amount < $usdAmount) {
            throw new DataException("Insufficient user balance.");
        }
        $config = sysConfigService()->get(SysConfigKeyEnum::InviteWithdrawConfig);
        if (Arr::get($config, 'min_amount', 50) > $usdAmount) {
            throw new DataException("Below the minimum withdrawal amount.");
        }
        $payment_platform = Arr::get($validated, 'payment_platform', 'visa');
        $platforms = collect(Arr::get($config, 'platforms'));
        $platform = $platforms->where('name', $payment_platform)->first();
        if (empty($platform)) {
            throw ValidationException::withMessages(['platform' => 'Withdrawal type does not exist']);
        }
        // 无脑的5秒一次
        $lock = Cache::lock(LockKey::UserInviteWithdraw->getKey($user->id), 10);
        if (!$lock) {
            throw new DataException("The operation is too frequent, please try again later");
        }
        try {
            DB::beginTransaction();
            $commissionAmount = 0;
            if (Arr::get($config, 'need_commission')) {
                $commissionRate = (float) Arr::get($platform, 'commission_rate', 0);
                $commissionAmount = round($amount * $commissionRate / 100, 2);
            }
            Arr::forget($validated, 'email_code');

            //美元汇率
            $usdCommissionAmount = $this->calculateUsdAmount($commissionAmount, $validated['currency_code']);
            
            // status
            $inviteAmountWithdraw = new InviteAmountWithdraw([
                ...$validated,
                'payment_platform' => $payment_platform,
                'status' => 0,
                'commission_amount' => $commissionAmount,
                'withdrawable_amount' => $user->wallet->invite_amount - $usdAmount -$usdCommissionAmount,
                'usd_amount' => $usdAmount ,
                'usd_commission_amount'=> $usdCommissionAmount
            ]);
            $inviteAmountWithdraw->user()->associate($user);
            $inviteAmountWithdraw->save();

             // 更新用户首次提现时间
             if (!$user->first_withdraw_date) {
                $user->update([
                    'first_withdraw_date' => now(),
                ]);
            }
            userWalletService()->walletChangeInviteAmount($user->wallet, WalletChangeTypeEnum::Withdraw, $usdAmount+$usdCommissionAmount, "", $inviteAmountWithdraw);

            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            throw new DataException($exception->getMessage());
        } finally {
            $lock->release();
        }

        return JsonResource::make($inviteAmountWithdraw);
    }

    /**
     * 提现规则
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function rule(Request $request)
    {
        $config = sysConfigService()->get(SysConfigKeyEnum::InviteWithdrawConfig);
        $platforms = collect(Arr::get($config, 'platforms'));
        // 获取所有的
        $platform = $platforms->all();
        return JsonResource::make([
            'need_commission' => Arr::get($config, 'need_commission'),
            'min_amount' => (float) Arr::get($config, 'min_amount'),
            'min_amount_agent' => convertPrice((float) Arr::get($config, 'min_amount'), currentCurrency()),
            'platforms' => $platform
        ]);
    }

    /**
     * 提现发送邮件
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function withdrawSendEmail(Request $request)
    {
        $user = Auth::user();
        $email = $user->email;

        // 生成6位数字验证码
        $code = str_pad((string)random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        
        // 存储验证码到缓存，有效期5分钟
        $cacheKey = 'email_code:' . $email;
        Cache::put($cacheKey, $code, now()->addMinutes(5));

        // 发送验证码邮件
        EmailRuleNotice::dispatch(
            EmailRuleEventEnum::Verificationcode,
            user: $user,
            data: ['code' => $code]
        )->onQueue(QueueKey::Default->value);

        return response()->json([
            'message' => 'The verification code has been sent to your email'
        ]);
    }

    /**
     * 提现记录列表
     * @param Request $request
     * @return JsonResource
     */
    public function inviteWithdrawIndex(Request $request): JsonResource
    {
        $user = Auth::user();
        $query = QueryBuilder::for(InviteAmountWithdraw::class)
            ->where('user_id', $user->id)
            ->allowedFilters([
                AllowedFilter::exact('status'),
            ])
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');
        //查询全部
        if ($request->boolean('all', false)) {
            return JsonResource::collection($query->get());
        }
        return JsonResource::collection($query->paginate($this->getPerPage()));
    }

    /**
     * 验证邮箱验证码
     * @param string $email
     * @param string $code
     * @throws ValidationException
     */
    private function validateEmailCode(string $email, string $code): void
    {
        $cacheKey = 'email_code:' . $email;
        $cachedCode = Cache::get($cacheKey);

        if (!$cachedCode) {
            throw ValidationException::withMessages([
                'email_code' => ['Verification code has expired, please resend.']
            ]);
        }

        if ($cachedCode !== $code) {
            throw ValidationException::withMessages([
                'email_code' => ['Verification code is incorrect.']
            ]);
        }

        // 验证成功后删除验证码
        Cache::forget($cacheKey);
    }

    /**
     * 计算美元提现金额
     * @param float $amount
     * @param string $currencyCode
     * @return float
     */
    private function calculateUsdAmount(float $amount, string $currencyCode): float
    {
        if ($currencyCode === 'USD') {
            return $amount;
        }

        // 获取美元货币对象
        $usdCurrency = Currency::where('currency_code', 'USD')->first();
        if (!$usdCurrency) {
            // 如果找不到美元货币配置，返回原金额
            return $amount;
        }

        //提现货币
        $fromCurrency =  Currency::where('currency_code', $currencyCode)->first();

        // 使用 convertPrice 函数进行货币转换
        return (float) convertPrice($amount, $usdCurrency, $fromCurrency);
    }
}
