<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Payment</title>
    <!-- PayPal JS SDK -->


</head>
<body>
<h1>PayPal Payment Page</h1>
<div id="paypal-button-container"></div>

<button type="submit" id="myClick"> submit</button>

</body>
</html>

<script
    src="https://sandbox.paypal.com/sdk/js?client-id=AT8pD_ddJ-5ybsa5VD15UDUqsyWY0HhqGNIvbggvkoN7UJuHRjnY3ZRi1jr6sFtG_HCWTj3PwlpSFzFv&components=buttons"></script>
<script>


    // 初始化 PayPal 按钮
    const button = paypal.Buttons({
        onInit(data, actions) {
            // Disable the buttons
            // Listen for changes to the checkbox
            document.getElementById("myClick").addEventListener("click", function (event) {
                // Enable or disable the button when it is checked or unchecked
                if (event.target.checked) {
                    actions.enable();
                } else {
                    actions.disable();
                }
            });
        },
        // 1. 创建订单：从后端获取 payid
        createOrder: function (data, actions) {
            return "7FP31515PN433242T";
        },

        // 2. 用户批准订单后触发
        onApprove: function (data, actions) {
            alert('Payment successful!');
        },

        // 3. 用户取消支付时触发
        onCancel: function (data) {
            console.log('Payment cancelled:', data);
            alert('Payment was cancelled!');
        },
        // 4. 处理支付过程中的错误
        onError: function (err) {
            console.error('Payment Error:', err);
            alert('An error occurred during the payment process.');
        },
    }).render('#paypal-button-container'); // 渲染按钮到容器中

    document.querySelector('.paypal-button-container').dispatchEvent(new MouseEvent('click')); // 模拟点击生成的PayPal按钮

</script>
