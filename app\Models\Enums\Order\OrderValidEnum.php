<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderValidEnum: int
{
    case Invalid = 0;
    case Effective = 1;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Invalid->value => 'Invalid', // 无效
            self::Effective->value => 'Effective', // 有效
        ];
    }

}
