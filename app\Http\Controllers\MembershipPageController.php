<?php

namespace App\Http\Controllers;

use App\Models\MembershipPageConfig;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\QueryBuilder;

class MembershipPageController extends Controller
{
    /**
     * 获取会员页面配置
     */
    public function getConfigs(): JsonResource
    {
        $config = QueryBuilder::for(MembershipPageConfig::class)
            ->with([
                'pcLoginBeforeImage:id,path,disk,module',
                'pcLoginAfterImage:id,path,disk,module',
                'mobileLoginBeforeImage:id,path,disk,module',
                'mobileLoginAfterImage:id,path,disk,module',
            ])
            ->first();
        return JsonResource::make($config);
    }
}
