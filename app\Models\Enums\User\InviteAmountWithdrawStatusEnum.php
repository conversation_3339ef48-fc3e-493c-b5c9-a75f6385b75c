<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum InviteAmountWithdrawStatusEnum: int
{
    case InReview  = 0;
    case Passed = 1;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::InReview->value => 'In Review',
            self::Passed->value => 'Passed',
        ];
    }

}
