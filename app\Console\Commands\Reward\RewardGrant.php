<?php

namespace App\Console\Commands\Reward;

use App\Models\User\UserInviteReward;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RewardGrant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rewards:grant';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发放订单返现奖励';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        un_limit();
        // 获取需返现的奖励列表
        $rewards = UserInviteReward::query()
            ->where('is_cashback', false)
            ->where('is_effective', true)
            ->where('cashback_at', '<=', now())
            ->with(['rewardUser', 'rewardUser.wallet'])
            ->get();
        
        // 循环发放奖励
        foreach ($rewards as $reward) {
            try {
                UserService()->cashbackReward($reward);
            } catch (\Exception $e) {
                Log::error('User cashback reward error:' . $e->getMessage());
            }
        }
        $this->info('发放订单返现奖励完成');
        return self::SUCCESS;
    }
}
