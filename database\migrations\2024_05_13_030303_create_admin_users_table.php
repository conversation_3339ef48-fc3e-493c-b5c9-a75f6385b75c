<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin_users', function (Blueprint $table) {
            $table->id();
            $table->string('account')->comment('账号');
            $table->string('name')->comment('名称');
            $table->string('password')->comment('密码');
            $table->string('phone')->nullable()->comment('手机号');
            $table->boolean('enable')->default(true)->comment('是否启用');
            $table->dateTime('last_login_date')->nullable()->comment('上次登录时间');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('管理员');
            $table->engine('InnoDB');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_users');
    }
};
