<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_id')->index();
            $table->string('sku', 512)->comment('sku');
            $table->integer('stock')->default(0)->comment('库存');
            $table->integer('min_show_stock')->default(5)->comment('最小显示库存');
            $table->integer('sort')->default(100)->comment('库存');
            $table->decimal('price', 10, 4)->comment('售价');
            $table->decimal('original_price', 10, 4)->comment('原价');
            $table->bigInteger('image_id')->comment('首图');
            $table->bigInteger('color_attribute_value_id')->nullable()->comment('颜色值id');
            $table->bigInteger('size_attribute_value_id')->nullable()->comment('尺寸值id');
            $table->boolean('is_publish')->default(true)->comment('是否上架');
            $table->decimal('weight', 10, 3)->nullable()->comment('重量');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('商品变种');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
