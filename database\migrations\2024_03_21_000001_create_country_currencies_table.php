<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('country_currencies', function (Blueprint $table) {
            $table->id();
            $table->string('country_code', 10)->comment('国家代码');
            $table->string('currency_code', 10)->comment('货币代码');
            $table->integer('image_id')->nullable()->comment('国家logo图片ID');
            $table->boolean('active')->default(true)->comment('是否启用');
            $table->boolean('is_default')->default(false)->comment('是否默认');
            $table->timestamps();
            $table->softDeletes();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('country_currencies');
    }
}; 