<?php

namespace App\Models\Order;

use App\Models\Country;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property bool $is_save
 */
class OrderAddress extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_save' => 'bool',
        'type' => OrderAddressTypeEnum::class,
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
