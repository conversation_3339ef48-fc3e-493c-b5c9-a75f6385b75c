<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_articles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cms_setting_id')->index()->default(0)->comment('所属CMS分类id');
            $table->string('title', 255)->default('')->comment('标题');
            $table->string('desc', 255)->nullable()->comment('博客摘要');
            $table->string('meta_description', 255)->nullable()->comment('meta描述');
            $table->longText('content')->comment('内容');
            $table->dateTime('published_at')->nullable()->comment('发布时间');
            $table->unsignedBigInteger('admin_user_id')->default(0)->comment('发布人');
            $table->boolean('status')->default(false)->comment('发布状态');
            $table->unsignedBigInteger('sort')->default(0)->comment('排序');
            $table->softDeletes();
            $table->timestamps();
            $table->comment('cms文章配置');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_articles');
    }
};
