<?php

namespace Database\Seeders;

use App\Constants\Permissions;
use App\Constants\Roles;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        foreach (Permissions::cases() as $permission) {
            Permission::query()
                ->updateOrCreate([
                    'name' => $permission->value
                ], [
                    'name' => $permission->value,
                    'desc' => $permission->desc(),
                    'guard_name' => 'admin'
                ]);
        }
        foreach (Roles::cases() as $role) {
            /**
             * @var $roleModel Role
             */
            $roleModel = Role::query()
                ->updateOrCreate([
                    'name' => $role->value
                ], [
                    'name' => $role->value,
                    'desc' => $role->desc(),
                    'guard_name' => 'admin'
                ]);
            // 管理员都可以
            if (in_array($roleModel->name, [Roles::SuperAdmin->value, Roles::Admin->value])) {
                $roleModel->givePermissionTo(...Permissions::cases());
            }
        }
    }
}
