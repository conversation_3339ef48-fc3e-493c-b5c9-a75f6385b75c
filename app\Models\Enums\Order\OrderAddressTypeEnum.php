<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderAddressTypeEnum: int
{
    case  Shipping = 1;
    case  Billing = 2;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Shipping->value => 'Shipping',
            self::Billing->value => 'Billing',
        ];
    }

}
