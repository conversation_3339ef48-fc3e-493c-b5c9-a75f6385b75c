<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\MembershipPageConfig;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Spatie\QueryBuilder\QueryBuilder;

class MembershipPageConfigController extends Controller
{
    /**
     * 获取配置
     */
    public function show(): JsonResource
    {
        $config = QueryBuilder::for(MembershipPageConfig::class)
            ->with([
                'pcLoginBeforeImage:id,path,disk,module',
                'pcLoginAfterImage:id,path,disk,module',
                'mobileLoginBeforeImage:id,path,disk,module',
                'mobileLoginAfterImage:id,path,disk,module',
            ])
            ->first();
        return JsonResource::make($config);
    }

    /**
     * 更新配置（必须传4个图片）
     */
    public function update(Request $request): JsonResource
    {
        $validated = $request->validate([
            'pc_login_before_image_id' => 'required|exists:attachments,id',
            'pc_login_after_image_id' => 'required|exists:attachments,id',
            'mobile_login_before_image_id' => 'required|exists:attachments,id',
            'mobile_login_after_image_id' => 'required|exists:attachments,id',
            'is_enabled' => 'boolean',
        ]);

        $validated['admin_user_id'] = Auth::user()->id;

        // 获取或创建配置记录
        $config = MembershipPageConfig::first();
        if ($config) {
            $config->update($validated);
        } else {
            $config = MembershipPageConfig::create($validated);
        }

        // 重新加载关联数据
        $config = MembershipPageConfig::getConfig();

        return JsonResource::make([
            'message' => '会员页面配置更新成功',
            'config' => $config
        ]);
    }
}
