<?php

namespace App\Jobs\Product;

use App\Models\ExternalClient;
use App\Models\Product\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

/**
 * 处理关键词，提供模糊搜索
 */
class ProductSyncErp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Product $product, public bool $update_image = false)
    {
    }


    // 去掉
    public function handle(): void
    {
        // try {
        //     // 加载颜色和尺寸
        //     $productInfo = $this->product->loadMissing([
        //         'brand:id,name',
        //         'image',
        //         'variants',
        //         'variants.colorAttribute',
        //         'variants.sizeAttribute',
        //         'variants.images',
        //         'variants.image'
        //     ]);
        //     $timestamp = time();
        //     $notice = Str::random(5);
        //     $client = ExternalClient::query()->where('name', 'erp')->first();
        //     $url = env("ERP_URL") . "/supershare/api/api_product_sync.php?submitEdit=true";
        //     $sign = md5("{$client->key}{$client->secret}{$timestamp}{$notice}");
        //     $newProduct = $productInfo->toArray();
        //     $newProduct['images'] = [$productInfo->image];
        //     $param = [
        //         'product' => $newProduct,
        //         'key' => $client->key,
        //         'notice' => $notice,
        //         'timestamp' => $timestamp,
        //         'sign' => $sign,
        //         'update_image' => $this->update_image // 手动执行直接替换
        //     ];
        //     $response = Http::withHeaders([
        //             'Content-Type' => 'application/json',
        //         ])
        //         ->timeout(60)
        //         ->post($url, $param);
        //     $result = $response->json();
        //     if (!Arr::get($result, 'flag')) {
        //         throw new \Exception(Arr::get($result, 'msg'));
        //     }
        // } catch (\Throwable $e) {
        //     logger()->channel('request_curl')->error('Sync ERP:' . $e->getMessage());
        // }

    }
}
