<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\CmsSetting;
use App\Models\Attachment;
use App\Models\CmsArticle;
use Illuminate\Support\Facades\Auth;
use App\Models\Enums\CmsSettingSortEnum;
use App\Models\Enums\CurrencyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class CmsSettingController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::CmsSettingsUpdate)->except(['index', 'store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::CmsSettingsIndex, Permissions::CmsSettingsUpdate)->only(['index', 'show']);
    }

    public function store(Request $request, CmsSetting $CmsSetting): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'sort' => ['nullable', 'int'],
            'image_id' => ['required', new Exists(Attachment::class, 'id')],
            'click_image_id' => ['required', new Exists(Attachment::class, 'id')],
            'status' => ['nullable', 'boolean']
        ]);
        try {

            // 如果 status 为 true，则更新 published_at
            if (!empty($validated['status']) && $validated['status'] == true) {
                $validated['published_at'] = now();
            }
            $validated['admin_user_id'] = Auth::user()->id;

            // 保存 CmsSetting
            $CmsSetting->fill($validated)->save();

        } catch (\Throwable $throwable) {
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($CmsSetting);
    }

    public function update(Request $request, CmsSetting $CmsSetting): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'sort' => ['nullable', 'int'],
            'image_id' => ['required', new Exists(Attachment::class, 'id')],
            'click_image_id' => ['required', new Exists(Attachment::class, 'id')],
            'status' => ['nullable', 'boolean']
        ]);
        try {
            // 如果 status 为 true，则更新 published_at
            if (!empty($validated['status']) && $validated['status'] == true) {
                $validated['published_at'] = now();
            }
            $validated['admin_user_id'] = Auth::user()->id;

            $CmsSetting->update($validated);

        } catch (\Throwable $throwable) {
            throw new DataException($throwable->getMessage());
        }
        return JsonResource::make($CmsSetting);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(CmsSetting::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            CmsSetting::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $CmsSetting = QueryBuilder::for(CmsSetting::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('status')
            ])
            ->with(['image:id,path,disk,module', 'clickImage:id,path,disk,module', 'adminUser:id,name'])
            ->allowedSorts('sort')
            ->defaultSort('-sort');

        if ($request->boolean('all', false)) {
            return JsonResource::collection($CmsSetting->get());
        }

        $res = $CmsSetting->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    //获取所有列表
    public function all(Request $request): JsonResource
    {
        $CmsSetting = QueryBuilder::for(CmsSetting::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('status'),
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts('sort')
            ->defaultSort('-sort');
        $res = $CmsSetting->get();

        return JsonResource::collection($res);
    }

    public function show(CmsSetting $CmsSetting): JsonResource
    {
        $CmsSetting->loadMissing([
            'image:id,path,disk,module',
            'clickImage:id,path,disk,module',
            'adminUser:id,name'
        ]);
        return JsonResource::make($CmsSetting);
    }

    public function status(Request $request, CmsSetting $CmsSetting)
    {
        $validated = $request->validate([
            'status' => ['required', 'boolean'],
        ]);
        if (!empty($validated['status']) && $validated['status'] == true) {
            $validated['published_at'] = now();
        }
        $CmsSetting->update($validated);
        return response()->json();
    }

    //排序单独修改
    public function sort(Request $request, CmsSetting $CmsSetting): JsonResource
    {
        $validated = $request->validate([
            'sort' => ['required', 'int'],
        ]);
        $CmsSetting->update($validated);
        return JsonResource::make($CmsSetting);
    }

    //根据id获取分类文章分页
    public function article(Request $request, CmsSetting $CmsSetting): JsonResource
    {
        $CmsArticle = QueryBuilder::for(CmsArticle::class, $request)
            ->where('cms_setting_id', $CmsSetting->id)
            ->allowedFilters([
                AllowedFilter::exact('status'),
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts('sort')
            ->defaultSort('-sort');
        $res = $CmsArticle->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }
    //根据id获取分类所有文章
    public function articleAll(Request $request, CmsSetting $CmsSetting): JsonResource
    {
        $CmsArticle = QueryBuilder::for(CmsArticle::class, $request)
            ->where('cms_setting_id', $CmsSetting->id)
            ->allowedFilters([
                AllowedFilter::exact('status'),
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts('sort')
            ->defaultSort('-sort');
        $res = $CmsArticle->get();
        return JsonResource::collection($res);
    }
}
