<?php

namespace App\Jobs;

use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateUserInviteReward implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 重试间隔时间（秒）
     */
    public int $backoff = 60;
 
    public function __construct(public OrderStatusEnum $status, public Order $order)
    {
        //
    }

    public function handle(): void
    {
        try {
            if ($this->status === OrderStatusEnum::Paid) {
                orderService()->createUserInviteReward($this->order);
            } elseif ($this->status === OrderStatusEnum::Cancel) {
                $this->order->inviteRewards()->update(['is_effective' => false]);
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }
}

