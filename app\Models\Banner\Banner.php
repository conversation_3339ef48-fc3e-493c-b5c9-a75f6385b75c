<?php

namespace App\Models\Banner;

use App\Constants\CacheKey;
use App\Models\Attachment;
use App\Models\Enums\EquipmentTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Banner extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'equipment_type' => EquipmentTypeEnum::class,
        'is_visible' => 'boolean',
        'video_text' => 'json'
    ];

    public function getIsVisibleAttribute($value)
    {
        return (bool) $value;
    }

    protected static function booted()
    {
        parent::booted();
        static::creating(function ($banner) {
            cachePartialDelete(CacheKey::BannerBuilderList->getKey('*'));
        });
        static::saved(function ($banner) {
            cachePartialDelete(CacheKey::BannerBuilderList->getKey('*'));
        });
    }

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }
}
