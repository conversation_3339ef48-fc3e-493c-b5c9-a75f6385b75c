<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum SysConfigKeyEnum: string
{
    case UserServiceAgreement = 'user_service_agreement';
    case CommentRuleSetting = 'comment_rule_setting';
    case CouponConfig = 'coupon_config';
    case ShippingFeeConfig = 'shipping_fee_config';
    case InviteWithdrawConfig = 'invite_withdraw_config';
    case CartModalCheckoutButtonConfig = 'cart_modal_checkout_button_config';
    case CartQuickAddCheckoutButtonConfig = 'cart_quick_add_checkout_button_config';
    case BannerCarouselConfig = 'banner_carousel_config';
    case CommentSortOrderConfig = 'comment_sort_order_config';
    case InvitationRulesConfig = 'invitation_rules_config';
    case QRCodeRedirectLinkConfig = 'qr_code_redirect_link_config';
    case ModalConfig = 'modal_config';
    case TaskPointDelayConfig = 'task_point_delay_config';


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::UserServiceAgreement->value => '用户服务协议',
            self::CommentRuleSetting->value => '评论规则设置',
            self::CouponConfig->value => '优惠券配置',
            self::ShippingFeeConfig->value => '运费配置',
            self::InviteWithdrawConfig->value => '邀请奖励提现规则',
            self::CartModalCheckoutButtonConfig->value => '购物车弹窗立即购买按钮配置',
            self::BannerCarouselConfig->value => '轮播图',
            self::InvitationRulesConfig->value => '邀请规则配置',
            self::QRCodeRedirectLinkConfig => '二维码跳转链接',
            self::ModalConfig->value => '弹框配置',
            self::TaskPointDelayConfig->value => '任务积分延迟发放配置',
        ];
    }

    public function rule(): array
    {
        return match ($this) {
//            self::UserServiceAgreement => [],
            // 评论配置 : 不知道是什么 后面再改
            self::CommentRuleSetting => [
                'order' => 'required|string',
                'sort' => 'required|string',
                'is_image_first' => 'required|boolean',
                'shield_rating' => 'required|integer'
            ],
            // 优惠券配置  自动匹配,可以叠加
            self::CouponConfig => [
                'automatic_matching' => 'boolean',
                'can_superimposed' => 'boolean'
            ],
            // 运费 : 普通,加急
            self::ShippingFeeConfig => [
                'general' => 'integer',
                'urgent' => 'integer'
            ],
            // 邀请奖励提现规则
            self::InviteWithdrawConfig => [
                'enabled' => 'required|bool',
                'min_amount' => 'required|integer',
                'currency_code' => 'required|string',
                'need_commission' => 'required|bool',
                'platforms' => ['array', 'required'],
                'platforms.*.name' => ['required', 'string'],
                'platforms.*.commission_rate' => ['nullable', 'numeric'],
            ],
            // 购物车弹窗立即购买按钮配置
            self::CartQuickAddCheckoutButtonConfig, self::CartModalCheckoutButtonConfig => [
                'checkout_button' => 'boolean',
                'paypal_button' => 'boolean'
            ],
            //banner轮播是否轮播
            self::BannerCarouselConfig => [
                'value' => 'boolean',
            ],
            //评论排序配置
            self::CommentSortOrderConfig => [
                'created_at' => 'required|string',
                'grade' => 'required|string',
                "image" => 'boolean',
                "top" => 'boolean',
            ],
            // 邀请规则配置
            self::InvitationRulesConfig => [
                'exclusive' => 'required|integer',
                'ordinary' => 'required|integer',
                'hours' => 'required|integer',
            ],
            // 二维码配置
            self::QRCodeRedirectLinkConfig => [
                'links' => 'required|array',
                'links.*.url' => 'required|string',
                'links.*.redirect_url' => 'required|string',
            ],
            // 弹框配置
            self::ModalConfig => [
                'ai_modal' => ['array', 'required'],
                'ai_modal.button' => 'boolean',
                'ai_modal.url' => 'required|string',
                'modal1' => ['array', 'required'],
                'modal1.button' => 'boolean',
                'modal1.modal_name' => 'required|string',
                'modal1.desc' => 'required|string',
                'modal1.title' => 'required|string',
                'modal1.image_id' => 'required|integer',
                'modal1.is_auto_show' => 'required|boolean',
                'modal1.delay_time' => 'required|integer',
                'modal1.button_image_id' => 'required|integer',
                'modal2' => ['array', 'required'],
                'modal2.button' => 'boolean',
                'modal2.logo_image_first_id' => 'required|integer',
                'modal2.logo_image_second_id' => 'required|integer',
                'modal2.title' => 'required|string',
                'modal2.image_id' => 'required|integer',
                'modal2.image_app_id' => 'required|integer',
                'modal2.is_auto_show' => 'required|boolean',
                'modal2.delay_time' => 'required|integer',
            ],
            default => [
                'value' => 'required|string',
            ],
        };
    }

}
