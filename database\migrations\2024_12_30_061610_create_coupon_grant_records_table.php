<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_grant_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('coupon_grant_rule_id')->comment('规则id');
            $table->bigInteger('user_id')->comment('发放到用户');
            $table->string('source_type')->comment('来源类别');
            $table->bigInteger('source_id')->comment('来源id');
            $table->bigInteger('user_coupon_id')->comment('发放的用户优惠券id');

            $table->engine('InnoDB');
            $table->comment('优惠券发放记录');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_grant_records');
    }
};
