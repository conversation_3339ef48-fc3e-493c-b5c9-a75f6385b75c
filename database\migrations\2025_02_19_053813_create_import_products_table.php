<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_products', function (Blueprint $table) {
            $table->id();
            $table->string('file')->comment('文件');
            $table->tinyInteger('status')->default(0)->comment('状态:0-待处理,1-处理中,2-处理完成,3-处理失败');
            $table->text('error_log')->nullable()->comment('错误日志');
            $table->bigInteger('admin_user_id')->index()->comment('管理员ID');
            $table->comment('导入商品表');
            $table->engine('InnoDB');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_products');
    }
};
