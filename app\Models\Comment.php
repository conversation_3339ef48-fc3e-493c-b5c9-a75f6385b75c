<?php

namespace App\Models;

use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Jobs\Product\ProductCommentStatic;
use App\Jobs\UpdateCommentHasImages;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Enums\CouponGrantType;
use App\Models\Order\Order;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property Carbon $top_at
 * @property int $product_id
 */
class Comment extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'top_at' => 'datetime',
        'enabled' => 'boolean',
        'status' => CommentStatusEnum::class
    ];

    protected static function booted(): void
    {
        parent::booted();

        static::updated(function (Comment $comment) {
            ProductCommentStatic::dispatch($comment->product_id)->onQueue(QueueKey::Product->value);
            // 审核通过发放奖励
            if ($comment->isDirty(['status']) && $comment->status == CommentStatusEnum::Approved) {
                CouponGrantCheck::dispatch(CouponGrantType::Comment, $comment)->onQueue(QueueKey::Coupon->value);
            }
        });
        static::created(function (Comment $comment) {
            ProductCommentStatic::dispatch($comment->product_id)->onQueue(QueueKey::Comment->value);
            UpdateCommentHasImages::dispatch($comment->id)->onQueue(QueueKey::Comment->value);
        });

    }

    public function isTop(): Attribute
    {
        return Attribute::make(get: function () {
            return !!$this->top_at;
        });
    }

    // 商品
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // 商品变体
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    // 订单
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // 客户
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // 评论图片
    public function images(): BelongsToMany
    {
        return $this->belongsToMany(
            Attachment::class,
            CommentImage::class,
            'comment_id',
            'attachment_id',
            'id',
            'id'
        );
    }
}
