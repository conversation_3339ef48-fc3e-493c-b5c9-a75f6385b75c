<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_articles', function (Blueprint $table) {
            // 先删除原有的 tag_id 字段
            $table->dropColumn('tag_id');
            // 添加新的 JSON 类型字段
            $table->json('tag_ids')->nullable()->comment('标签ID数组');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_articles', function (Blueprint $table) {
            // 删除 JSON 字段
            $table->dropColumn('tag_ids');
            // 恢复原有的 tag_id 字段
            $table->integer('tag_id')->nullable()->comment('标签ID');
        });
    }
}; 