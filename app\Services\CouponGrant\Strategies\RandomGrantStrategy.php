<?php

namespace App\Services\CouponGrant\Strategies;

use App\Models\Coupon\Coupon;
use App\Models\CouponRandomGrantRecord;
use App\Models\User\User;

class RandomGrantStrategy extends RuleStrategy
{

    public function getUser(): ?User
    {
        if ($this->model instanceof CouponRandomGrantRecord) {
            return $this->model->user;
        }
        return null;
    }

    /**
     * 获取邀请奖励的优惠券
     */
    public function getCoupon(): ?Coupon
    {
        if ($this->model instanceof CouponRandomGrantRecord) {
            return $this->model->rewardCoupon;
        }
        return null;
    }

    public function checkCanGrant(): bool
    {
        return $this->model instanceof CouponRandomGrantRecord;
    }

}
