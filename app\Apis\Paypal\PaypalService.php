<?php

namespace App\Apis\Paypal;

use App\Apis\Paypal\PaypalClient;
use App\Models\Order\FastPaypalOrder;
use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use Illuminate\Support\Str;
use App\Models\Cart;
use App\Models\CartItem;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;

class PaypalService
{

    /**
     * 构建支付意向数据 - 快闪订单
     * @param FastPaypalOrder $fastPaypalOrder
     * @return array
     */
    public static function formatIntentsDataFromFastPaypalOrder(FastPaypalOrder $fastPaypalOrder): array
    {
        // 获取购物车信息
        $cart = $fastPaypalOrder->cart;

        //配送信息
        $shipping_info = [];
        if ($cart->checkout_data) {
            $shipping_info = [
                'name' => [
                    'full_name' => $cart->checkout_data['shipping_address']['first_name'] . ' ' . $cart->checkout_data['shipping_address']['last_name']
                ],
                'address' => [
                    'address_line_1' => $cart->checkout_data['shipping_address']['address'],
                    'address_line_2' => $cart->checkout_data['shipping_address']['address1'],
                    'admin_area_2' => $cart->checkout_data['shipping_address']['city'],
                    'admin_area_1' => $cart->checkout_data['shipping_address']['state'],
                    'postal_code' => $cart->checkout_data['shipping_address']['zip'],
                    'country_code' => $cart->checkout_data['shipping_address']['country_code']
                ]
            ];
        }

        //账单信息
        $payer = [];
        if ($cart->checkout_data) {
            $payer = [
                'name' => [
                    'given_name' => $cart->checkout_data['shipping_address']['first_name'],
                    'surname' => $cart->checkout_data['shipping_address']['last_name'],
                ],
                'email_address' => $cart->checkout_data['email'],
                'phone' => [
                    'phone_type' => 'HOME',
                    'phone_number' => [
                        'national_number' => preg_replace("/[^0-9]/", '', $cart->checkout_data['billing_address']['phone'])
                    ]
                ],
                'address' => [
                    'address_line_1' => $cart->checkout_data['billing_address']['address'],
                    'address_line_2' => $cart->checkout_data['billing_address']['address1'],
                    'admin_area_2' => $cart->checkout_data['billing_address']['city'],
                    'admin_area_1' => $cart->checkout_data['billing_address']['state'],
                    'postal_code' => $cart->checkout_data['billing_address']['zip'],
                    'country_code' => $cart->checkout_data['billing_address']['country_code']
                ]
            ];
        }

        // 生成paypal订单
        $discount = 0;
        $price = 0;
        $currencyCode = currentCurrency()->currency_code;
        $items = $cart->items()->with(['productVariant'])->chunkMap(function (CartItem $item) use (&$price, &$discount, $currencyCode) {
            $price += $item->{'price_agent'} * $item->{'num'};
            $discount += $item->discount_agent;
            return [
                "name" => "{$item->productVariant->getName()}({$item->productVariant->sku})",
                "unit_amount" => [
                    "currency_code" => $currencyCode,
                    "value" => (string)round($item->{'price_agent'}, 2),
                ],
                "quantity" => (string)$item->{'num'},
            ];
        })->toArray();

        $purchaseUnits = [
            "name" => Str::limit(implode(',', array_column($items, 'name')), '15'),
            "description" => 'ardoreva.com',
            "type" => "DIGITAL",
            "category" => "PHYSICAL_GOODS",
            "amount" => [
                "currency_code" => $currencyCode,
                "value" => (string)round(($price + $cart->delivery_fee_real_agent - $discount), 2),
                'breakdown' => [
                    'item_total' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($price, 2),
                    ],
                    'shipping' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($cart->delivery_fee_agent, 2),
                    ],
                    'shipping_discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($cart->delivery_fee_discount_agent, 2),
                    ],
                    'discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($discount, 2),
                    ],
                ],
            ],
            'items' => $items,
            'shipping' => $shipping_info ?: null,
            "invoice_id" => $fastPaypalOrder->{'no'}
        ];

        return [
            "intent" => "CAPTURE",
            'payer' => $payer ?: null,
            "application_context" => [
                "brand_name" => 'Ardoreva',
                "return_url" => "https://www.ardoreva.com/payment",
                "cancel_url" => 'https://www.ardoreva.com/payment',
                'shipping_preference' => 'GET_FROM_FILE',
                'user_action' => 'PAY_NOW',
            ],
            "purchase_units" => [$purchaseUnits],
        ];
    }

    /**
     * 构建支付意向数据 - 订单
     * @param Order $order
     * @return array
     */
    public static function formatIntentsData(Order $order)
    {
        //配送信息
        $shipping_info = [
            'name' => [
                'full_name' => $order->shippingAddress->first_name . ' ' . $order->shippingAddress->last_name
            ],
            'address' => [
                'address_line_1' => $order->shippingAddress->address,
                'address_line_2' => $order->shippingAddress->address1,
                'admin_area_2' => $order->shippingAddress->city,
                'admin_area_1' => $order->shippingAddress->state,
                'postal_code' => $order->shippingAddress->zip,
                'country_code' => $order->shippingAddress->country->iso_code
            ]
        ];

        //账单信息
        $payer = [
            'name' => [
                'given_name' => $order->shippingAddress->first_name,
                'surname' => $order->shippingAddress->last_name,
            ],
            'email_address' => $order->email,
            'phone' => [
                'phone_type' => 'HOME',
                'phone_number' => [
                    'national_number' => preg_replace("/[^0-9]/", '', $order->billingAddress->phone)
                ]
            ],
            'address' => [
                'address_line_1' => $order->billingAddress->address,
                'address_line_2' => $order->billingAddress->address1,
                'admin_area_2' => $order->billingAddress->city,
                'admin_area_1' => $order->billingAddress->state,
                'postal_code' => $order->billingAddress->zip,
                'country_code' => $order->billingAddress->country->iso_code
            ]
        ];

        $discount = 0;
        $price = 0;
        $currencyCode = currentCurrency()->currency_code;
        $items = $order->items()->with(['product.product'])->chunkMap(function (OrderItem $item) use ($order, &$price, &$discount, $currencyCode) {
            $price += $item->{'price_agent'} * $item->{'num'};
            $discount += $item->coupon_discount_price_agent;
            return [
                "name" => "{$item->product->getName()}({$item->product->sku})",
                "unit_amount" => [
                    "currency_code" => $currencyCode,
                    "value" => (string)round($item->{'price_agent'}, 2),
                ],
                "quantity" => (string)$item->{'num'},
            ];
        })->toArray();

        $purchaseUnits = [
            "name" => Str::limit(implode(',', array_column($items, 'name')), '15'),
            "description" => 'ardoreva.com',
            "amount" => [
                "currency_code" => $currencyCode,
                "value" => (string)round(($price + $order->shipping_fee_agent - $order->shipping_fee_discount_agent - $discount), 2),
                'breakdown' => [
                    'item_total' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($price, 2),
                        341
                    ],
                    'shipping' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($order->shipping_fee_agent, 2),
                    ],
                    'shipping_discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($order->shipping_fee_discount_agent, 2),
                    ],
                    'discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($discount, 2),
                    ],
                ],
            ],
            'items' => $items,
            'shipping' => $shipping_info ?: null,
            "invoice_id" => $order->{'no'}
        ];

        return [
            "intent" => "CAPTURE",
            'payer' => $payer ?: null,
            "application_context" => [
                "brand_name" => 'Ardoreva',
                "return_url" => "https://www.ardoreva.com/payment",
                "cancel_url" => 'https://www.ardoreva.com/payment',
                'shipping_preference' => 'GET_FROM_FILE',
                'user_action' => 'PAY_NOW',
            ],
            "purchase_units" => [$purchaseUnits],
        ];
    }

    /**
     * 构建支付意向数据 - 购物车
     * @param Cart $cart
     * @return array
     */
    public static function formatIntentsDataFromCart(Cart $cart): array
    {
        //配送信息
        $shipping_info = [];
        if ($cart->checkout_data) {
            $shipping_info = [
                'name' => [
                    'full_name' => $cart->checkout_data['shipping_address']['first_name'] . ' ' . $cart->checkout_data['shipping_address']['last_name']
                ],
                'address' => [
                    'address_line_1' => $cart->checkout_data['shipping_address']['address'],
                    'address_line_2' => $cart->checkout_data['shipping_address']['address1'],
                    'admin_area_2' => $cart->checkout_data['shipping_address']['city'],
                    'admin_area_1' => $cart->checkout_data['shipping_address']['state'],
                    'postal_code' => $cart->checkout_data['shipping_address']['zip'],
                    'country_code' => $cart->checkout_data['shipping_address']['country_code']
                ]
            ];
        }

        //账单信息
        $payer = [];
        if ($cart->checkout_data) {
            $payer = [
                'name' => [
                    'given_name' => $cart->checkout_data['shipping_address']['first_name'],
                    'surname' => $cart->checkout_data['shipping_address']['last_name'],
                ],
                'email_address' => $cart->checkout_data['email'],
                'phone' => [
                    'phone_type' => 'HOME',
                    'phone_number' => [
                        'national_number' => preg_replace("/[^0-9]/", '', $cart->checkout_data['billing_address']['phone'])
                    ]
                ],
                'address' => [
                    'address_line_1' => $cart->checkout_data['billing_address']['address'],
                    'address_line_2' => $cart->checkout_data['billing_address']['address1'],
                    'admin_area_2' => $cart->checkout_data['billing_address']['city'],
                    'admin_area_1' => $cart->checkout_data['billing_address']['state'],
                    'postal_code' => $cart->checkout_data['billing_address']['zip'],
                    'country_code' => $cart->checkout_data['billing_address']['country_code']
                ]
            ];
        }

        $discount = 0;
        $price = 0;
        $currencyCode = currentCurrency()->currency_code;
        $items = $cart->items()->with(['productVariant'])->chunkMap(function (CartItem $item) use (&$price, &$discount, $currencyCode) {
            $price += $item->{'price_agent'} * $item->{'num'};
            $discount += $item->discount_agent;
            return [
                "name" => $item->productVariant->getName(),
                "sku" => $item->productVariant->sku,
                "description" => "{$item->productVariant->getName()}({$item->productVariant->sku})",
                "unit_amount" => [
                    "currency_code" => $currencyCode,
                    "value" => (string)round($item->{'price_agent'}, 2),
                ],
                "quantity" => (string)$item->{'num'},
            ];
        })->toArray();

        $purchaseUnits = [
            "name" => Str::limit(implode(',', array_column($items, 'name')), '15'),
            "description" => 'ardoreva.com',
            "amount" => [
                "currency_code" => $currencyCode,
                "value" => (string)round(($price + $cart->delivery_fee_real_agent - $discount), 2),
                'breakdown' => [
                    'item_total' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($price, 2),
                    ],
                    'shipping' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($cart->delivery_fee_agent, 2),
                    ],
                    'shipping_discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($cart->delivery_fee_discount_agent, 2),
                    ],
                    'discount' => [
                        "currency_code" => $currencyCode,
                        "value" => (string)round($discount, 2),
                    ],
                ],
            ],
            'items' => $items,
            'shipping' => $shipping_info ?: null
        ];

        return [
            "intent" => "CAPTURE",
            'payer' => $payer ?: null,
            "application_context" => [
                "brand_name" => 'Ardoreva',
                "return_url" => "https://www.ardoreva.com/payment",
                "cancel_url" => 'https://www.ardoreva.com/payment',
                'shipping_preference' => 'GET_FROM_FILE',
                'user_action' => 'PAY_NOW',
            ],
            "purchase_units" => [$purchaseUnits],
        ];
    }

    /**
     * 创建支付意向 - 订单
     * @param FastPaypalOrder $fastPaypalOrder
     * @return array|mixed
     */
    public static function paymentIntentsCreateForFastPaypalOrder(FastPaypalOrder $fastPaypalOrder): mixed
    {
        // 获取客户端
        $provider = PaypalClient::getInstance();
        $currencyCode = currentCurrency()->currency_code;
        $provider->setCurrency($currencyCode);
        $provider->setWebHookID('2S673891D6866720J');

        // 发起请求
        $data = self::formatIntentsDataFromFastPaypalOrder($fastPaypalOrder);
        logger()->channel('payment')->warning("payment:paypal:createIntent fastNo:{$fastPaypalOrder->no} request:", $data);
        $response = $provider->createOrder($data);
        logger()->channel('payment')->warning("payment:paypal:createIntent fastNo:{$fastPaypalOrder->no} response:", $response);
        return $response;
    }

    /**
     * 创建支付意向 - 订单
     * @param Order $order
     * @return array|mixed
     */
    public static function paymentIntentsCreate(Order $order): mixed
    {
        // 获取客户端
        $provider = PaypalClient::getInstance();
        $currencyCode = currentCurrency()->currency_code;
        $provider->setCurrency($currencyCode);
        $provider->setWebHookID('2S673891D6866720J');

        // 发起请求
        $data = self::formatIntentsData($order);
        logger()->channel('payment')->warning("payment:paypal:createIntent no:{$order->no} request:", $data);
        $response = $provider->createOrder($data);
        logger()->channel('payment')->warning("payment:paypal:createIntent no:{$order->no} response:", $response);
        return $response;
    }

    /**
     * 创建支付意向 - 购物车
     * @param Cart $cart
     * @return array
     */
    public static function paymentIntentsCreateForCart(Cart $cart): mixed
    {
        // 获取客户端
        $provider = PaypalClient::getInstance();
        $currencyCode = currentCurrency()->currency_code;
        $provider->setCurrency($currencyCode);
        $provider->setWebHookID('2S673891D6866720J');

        // 发起请求
        $data = self::formatIntentsDataFromCart($cart);
        logger()->channel('payment')->warning("payment:paypal:createIntent cartId:{$cart->id} request:", $data);
        $response = $provider->createOrder($data);
        logger()->channel('payment')->warning("payment:paypal:createIntent cartId:{$cart->id} response:", $response);
        return $response;
    }

    /**
     * 查询订单详情
     * @param string $paypalOrderId
     * @return array
     */
    public static function showOrderDetails(String $paypalOrderId)
    {
        $response = PaypalClient::getInstance()->showOrderDetails($paypalOrderId);
        logger()->channel('payment')->warning("payment:paypal:details paypalOrderId:{$paypalOrderId} response:", $response);
        return $response;
    }

    /**
     * 确认支付意向
     * @param string $paypalOrderId
     * @return array
     */
    public static function paymentIntentsConfirm(String $paypalOrderId): mixed
    {
        $response = PaypalClient::getInstance()->capturePaymentOrder($paypalOrderId);
        logger()->channel('payment')->warning("payment:paypal:confirmIntent paypalOrderId:{$paypalOrderId} response:", $response);
        return $response;
    }

    /**
     * 回调验证
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    public static function notify(Request $request)
    {
        $result = PaypalClient::getInstance()
            ->setWebHookID('2S673891D6866720J')
            ->verifyIPN($request);
        logger()->channel('payment')->warning("payment:paypal:notify :", $result);
        return Arr::get($result, 'verification_status') != 'SUCCESS' ? false : true;
    }

}
