<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sharing_rules', function (Blueprint $table) {
            $table->id();
            $table->string('title', 32)->comment('标题');
            $table->string('description', 255)->comment('描述');
            $table->boolean('is_global')->default(false)->comment('是否默认全局');
            $table->boolean('is_publish')->default(false)->comment('是否发布');
            $table->dateTime('effective_start_at')->nullable()->comment('有效期（固定开始时间）');
            $table->dateTime('effective_end_at')->nullable()->comment('有效期（固定过期时间）');
            $table->tinyInteger('reward_type')->default(1)->comment('奖励类型：1-固定金额，2-百分比，3-优惠卷');
            $table->json('rules')->comment('规则');
            $table->json('condition')->comment('限制条件');
            $table->json('email_setting')->comment('邮件设置');
            $table->bigInteger('update_user_id')->comment('操作人ID');

            $table->engine('InnoDB');
            $table->comment('用户分裂规则表');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sharing_rules');
    }
};
