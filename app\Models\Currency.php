<?php

namespace App\Models;

use App\Constants\CacheKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;

class Currency extends MiddleModel
{
    use HasFactory;

    protected static ?self $instance = null;

    protected $fillable = [
        'currency_code',    // 货币代码
        'symbol',          // 货币符号
        'currency_name',   // 货币名称
        'exchange_rate',   // 汇率
        'symbol_position', // 符号位置
        'active',         // 是否启用
        'is_default',     // 是否默认货币
    ];

    protected $casts = [
        'active' => 'boolean',
        'is_default' => 'boolean',
        'exchange_rate' => 'float',
    ];

    public static function booted()
    {
        static::saved(function (self $model) {
            Cache::forget(CacheKey::CurrencyDefault->value);
        });
        static::updated(function (self $model) {
            Cache::forget(CacheKey::CurrencyDefault->value);
        });

    }

    public static function getDefaultCurrency()
    {
        if (!self::$instance) {
            self::$instance = Cache::remember(CacheKey::CurrencyDefault->value, now()->addDay(), function () {
                return self::query()->where('is_default', 1)->first();
            });
        }
        return self::$instance;

    }

    /**
     * 获取默认货币
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->where('status', true)->first();
    }

    /**
     * 获取所有启用的货币
     */
    public static function getActive()
    {
        return static::where('status', true)->orderBy('sort', 'asc')->get();
    }

    /**
     * 获取使用该货币的所有国家
     */
    public function countries()
    {
        return $this->hasMany(CountryCurrency::class, 'currency_code', 'code')
            ->where('active', true);
    }
}
