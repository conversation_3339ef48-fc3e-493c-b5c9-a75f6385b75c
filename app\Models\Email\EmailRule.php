<?php

namespace App\Models\Email;

use App\Models\AdminUser;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\EmailRuleSendTypeEnum;
use App\Models\Enums\EmailRuleTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $delay_second
 * @property EmailTemplate $template
 * @property EmailRuleEventEnum $event
 * @property array $data
 */
class EmailRule extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'enabled' => 'boolean',
        'is_delay' => 'boolean',
        'delay_second' => 'int',
        'data' => 'array',
        'type' => EmailRuleTypeEnum::class,
        'send_type' => EmailRuleSendTypeEnum::class,
        'event' => EmailRuleEventEnum::class,
    ];

    /**
     * @return HasOne
     */
    public function template(): HasOne
    {
        return $this->hasOne(EmailTemplate::class, 'id', 'email_template_id');
    }

    //用户信息
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'admin_user_id');
    }
}
