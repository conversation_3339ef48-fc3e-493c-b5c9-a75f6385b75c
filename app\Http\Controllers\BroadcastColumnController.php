<?php

namespace App\Http\Controllers;

use App\Models\BroadcastColumn;
use App\Models\Enums\Broadcast\BroadcastColumnPositionsEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\QueryBuilder;


class BroadcastColumnController extends Controller
{

    /**
     * 列表
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(BroadcastColumn::class, $request)
            // ->selectRaw('*, IF(image_id IS NOT NULL, 1, 2) as image_type')
            ->where('is_visible',1)
            ->allowedSorts(['id'])
            ->defaultSort('-id')
            ->with('image:id,path,disk,module');
        $broadcasts = $builder->get();

        $left = [];
        $center = [];
        $right = [];

        foreach ($broadcasts as $broadcast) {
            switch (BroadcastColumnPositionsEnum::from($broadcast->content_type)) {
                case BroadcastColumnPositionsEnum::ToTheLeft:
                    $left[$broadcast->title] = $broadcast->url;
                    break;

                case BroadcastColumnPositionsEnum::Centered:
                    $center[] = $broadcast;
                    break;

                case BroadcastColumnPositionsEnum::ToTheRight:
                    $right[] = $broadcast;
                    break;
            }
        }

        $result = [
            'left' => $left,
            'center' => $center,
            'right' => $right,
        ];
        return JsonResource::collection($result);
    }

}
