<?php

namespace App\Models\Order\Payment;

use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use Carbon\Carbon;

interface Payment
{

    public function loadPaymentInfo():void;

    /**
     * 获取支付金额
     * @return float
     */
    public function getPaidAmount(): float;

    /**
     * 获取支付时间
     * @return string|Carbon|null
     */
    public function getPaidAt(): string|Carbon|null;

    /**
     * 获取支付类型
     * @return OrderPaidTypeEnum
     */
    public function getPaidType(): OrderPaidTypeEnum;

    /**
     * 获取支付状态
     * @return OrderPaidStatusEnum
     */
    public function getPaidStatus(): OrderPaidStatusEnum;
}
