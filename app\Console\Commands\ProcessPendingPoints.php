<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\UserPointService;

class ProcessPendingPoints extends Command
{
    protected $signature = 'points:process-pending {--limit=100 : Maximum number of records to process} {--dry-run : Show what would be processed without actually doing it}';
    protected $description = '处理待发放的积分记录';

    public function handle(): int
    {
        $limit = (int) $this->option('limit');
        $dryRun = $this->option('dry-run');

        $this->info("Processing pending points...");
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No actual processing will occur");
        }

        $pointService = new UserPointService();
        
        try {
            $processedCount = $pointService->processDuePoints();
            
            if ($processedCount > 0) {
                $this->info("Successfully processed {$processedCount} pending point records.");
            } else {
                $this->info("No pending points found for processing.");
            }
            
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error processing pending points: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}
