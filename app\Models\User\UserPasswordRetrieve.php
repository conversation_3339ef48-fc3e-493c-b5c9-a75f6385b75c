<?php

namespace App\Models\User;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property  string $key
 * @property  User $user
 * @property  bool $is_used
 */
class UserPasswordRetrieve extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_used' => 'bool',
        'created_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
