<?php

namespace App\Models\Enums\Reward;

use Illuminate\Support\Arr;

enum RewardFansTypeEnum: string
{
    case Exclusive = 'exclusive';
    case Ordinary = 'ordinary';

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Exclusive->value => '专属',
            self::Ordinary->value => '普通',
        ];
    }
}
