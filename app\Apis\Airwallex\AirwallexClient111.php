<?php

namespace App\Apis\Airwallex;

use App\Constants\CacheKey;
use App\Models\Enums\Order\Payment\PaymentMethodTypeEnum;
use App\Models\Order\Payment\PaymentMethod;
use App\Traits\SingletonTrait;
use GuzzleHttp\Middleware;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class AirwallexClient extends PendingRequest
{
    use SingletonTrait;

    /**
     * @var array
     */
    protected array $config;

    private function __construct()
    {
        parent::__construct();
        // 支付方式
        $payment = PaymentMethod::query()->where('type', PaymentMethodTypeEnum::Airwallex->value)->first();
        // 配置
        $this->config = $payment->config;
        if ($this->config['sandbox']) {
            $baseUrl = 'https://api-demo.airwallex.com';
        } else {
            $baseUrl = 'https://api.airwallex.com';
        }
        $this->baseUrl($baseUrl);
        $clientConfig = [
            'verify' => false,
            'timeout' => 30,
        ];
        if ($proxy = config('services.socialite_proxy')) {
            if ($proxy['enabled']) {
                $clientConfig['proxy'] = [
                    'http' => config('services.socialite_proxy.http'),
                    'https' => config('services.socialite_proxy.https'),
                ];
            }
        }
        $this->withOptions($clientConfig);
        $this->withHeader('Content-Type', 'application/json');
        // 添加中间件
        $this->withMiddleware(Middleware::mapRequest(function (RequestInterface $request) {
            if ($request->getUri()->getPath() == '/api/v1/authentication/login') {
                return $request;
            }
            $token = $this->getToken();
            return $request->withHeader('Authorization', "Bearer {$token}");
        }));

        // 添加后置中间件
        // $this->withMiddleware(Middleware::retry(function ($retries, RequestInterface $request, ResponseInterface $response) {
        //     // 401重试
        //     if ($response->getStatusCode() == 401 && $retries <= 2) {
        //         logger()->info('retries:'.$retries);
        //         $this->getToken(true);
        //         return true;
        //     }
        //     if ($retries >= 3) {
        //         logger()->error('AirwallexClientError: get Token error');
        //     }
        //     return false;
        // }, function ($retries) {
        //     return $retries * 1000;
        // }));
    }

    private function getToken($refresh = false): string
    {
        if ($refresh) {
            Cache::forget(CacheKey::PaymentAirwallexAccessToken->value);
        }

        // logger()->info('缓存:'.Cache::get(CacheKey::PaymentAirwallexAccessToken->value));return false;
        // 访问令牌有效期为 30 分钟
        return Cache::remember(CacheKey::PaymentAirwallexAccessToken->value, now()->addMinutes(25), function () {
            $loginResponse = $this->replaceHeaders($this->getHeaders())->post('/api/v1/authentication/login');
            // 返回token
            if (!$loginResponse->successful()) {
                logger()->channel('airwallex')->info($loginResponse->body());
                throw new \RuntimeException('Airwallex failed: ' . $loginResponse->body());
            }
            return $loginResponse->json('token');
        });
    }

    private function getHeaders()
    {
        if ($this->config['sandbox']) {
            return [
                'x-api-key' => Arr::get($this->config, 'sandbox_api_key'),
                'x-client-id' => Arr::get($this->config, 'sandbox_client_id'),
            ];
        } else {
            return [
                'x-api-key' => Arr::get($this->config, 'api_key'),
                'x-client-id' => Arr::get($this->config, 'client_id'),
            ];
        }
    }
}
