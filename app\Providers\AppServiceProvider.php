<?php

namespace App\Providers;

use App\Apis\Bing\BingAddressClient;
use App\Services\CartService;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\ProductService;
use App\Services\SysConfigService;
use App\Services\UserService;
use App\Services\UserWalletService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        $this->app->singleton(UserService::class);
        $this->app->singleton(SysConfigService::class);
        $this->app->singleton(UserWalletService::class);
        $this->app->singleton(ProductService::class);
        $this->app->singleton(OrderService::class);
        $this->app->singleton(PaymentService::class);
        $this->app->singleton(CartService::class);
        $this->app->singleton(BingAddressClient::class);

    }
}
