<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum WalletChangeTypeEnum: string
{
    case  RechargeBackend = 'recharge_backend';
    case  OrderCompleted = 'order_completed';
    case  Consume = 'consume';
    case  Withdraw = 'withdraw';
    case  MembershipOrder = 'membership_order';
    case  MembershipBirthday = 'membership_birthday';
    case  MembershipPromotion = 'membership_promotion';
    case  MembershipRefund = 'membership_refund';
    case  TaskReward = 'task_reward';

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::RechargeBackend->value => 'Backend recharge',
            self::OrderCompleted->value => 'Order completion rewards',
            self::Consume->value => 'Points consumption',
            self::Withdraw->value => 'Withdraw',
            self::MembershipOrder->value => 'Membership order points',
            self::MembershipBirthday->value => 'Membership birthday bonus',
            self::MembershipPromotion->value => 'Membership promotion bonus',
            self::MembershipRefund->value => 'Membership refund deduction',
            self::TaskReward->value => 'Task reward points',
        ];
    }
}
