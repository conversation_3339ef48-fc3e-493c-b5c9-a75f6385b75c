<?php

namespace App\Http\Middleware;

use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\User\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserCheck
{

    public function handle(Request $request, Closure $next)
    {
        /**
         * @var  $user User
         */
        $user = Auth::user();
        if (!$user instanceof User) {
            throw new DataException("User login token is invalid or expired", ErrorCode::Unauthenticated);
        }
        if ($user->{'status'} == UserStatusEnum::StatusDisabled) {
            throw new DataException("The account has been disabled. Please contact the administrator to investigate the reason", ErrorCode::UserStatusDisabled);
        }

        return $next($request);
    }
}
