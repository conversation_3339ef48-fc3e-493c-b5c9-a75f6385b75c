<?php

namespace App\Console\Commands;

use App\Jobs\EmailRuleNotice;
use App\Models\User\UserCoupon;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Email\EmailRule;
use Illuminate\Console\Command;

class SendCouponExpirationReminder extends Command
{
    protected $signature = 'coupon:send-expiration-reminder';
    protected $description = '发送优惠券临期提醒邮件';

    public function handle()
    {
        // 查询优惠券过期提醒规则
        $rule = EmailRule::query()
            ->where('event', EmailRuleEventEnum::CouponExpire->value)
            ->where('enabled', true)
            ->first();

        if (!$rule) {
            $this->info('未找到启用的优惠券过期提醒规则');
            return;
        }

        $rule_detail = $rule->data;
        $send_time = $rule_detail['send_time']; //邮件发送时间，格式：10:00:00
        $advance_minute = $rule_detail['advance_minute']; //优惠券到期前多少分钟发送

        // 判断当前时间是否等于发送时间（只比较小时）
        $currentHour = now()->format('H');
        $sendHour = explode(':', $send_time)[0];
        if ($currentHour != $sendHour) {
            $this->info("当前小时 {$currentHour} 不是发送时间 {$sendHour}，跳过发送");
            return;
        }

        // 获取即将过期的优惠券
        $expiringCoupons = UserCoupon::query()
            ->select('user_coupons.*', 'coupons.name', 'coupons.desc', 'coupons.rules')
            ->join('coupons', 'user_coupons.coupon_id', '=', 'coupons.id')
            ->where('user_coupons.effective_end_at', '>', now())
            ->where('user_coupons.effective_end_at', '<=', now()->addMinutes($advance_minute))
            ->where('user_coupons.used_at', null)
            ->where('user_coupons.is_sendEmail', 0)
            ->with(['user'])
            ->get();

        foreach ($expiringCoupons as $userCoupon) {
            if (!$userCoupon->user) {
                continue;
            }

            // 发送邮件提醒
            EmailRuleNotice::dispatch(
                EmailRuleEventEnum::CouponExpire,
                user: $userCoupon->user,
                data: [
                    'coupon' => $userCoupon->toArray(),
                    'expired_at' => $userCoupon->effective_end_at,
                    'days_left' => now()->diffInMinutes($userCoupon->effective_end_at)
                ]
            )->onQueue('default');

            // 更新邮件发送状态
            $userCoupon->update(['is_sendEmail' => 1]);

            $this->info("已发送优惠券临期提醒邮件给用户: {$userCoupon->user->email}");
        }

        $this->info('优惠券临期提醒邮件发送完成');
    }
} 