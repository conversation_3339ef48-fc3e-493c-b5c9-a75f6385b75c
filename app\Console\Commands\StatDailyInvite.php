<?php

namespace App\Console\Commands;

use App\Models\DailyInviteStat;
use App\Models\Enums\User\InviteUserTypeEnum;
use App\Models\User\User;
use App\Models\User\UserInviteReward;
use App\Services\UserService;
use Date;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;

class StatDailyInvite extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stat:daily:invite';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每日邀请统计, 每天00:00:00执行一次';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        un_limit();
        $stats = UserService()->inviteStatByDate(Date::yesterday()->startOfDay(), Date::yesterday()->endOfDay());
        foreach ($stats as $stat) {
            $dailyInviteStat = new DailyInviteStat();
            $dailyInviteStat->fill([
                ...$stat,
                'date' => Date::yesterday()->toDateString()
            ])->save();
        }
        $this->info('统计完成');
        return self::SUCCESS;
    }
}
