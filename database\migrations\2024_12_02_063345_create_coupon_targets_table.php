<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_targets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('coupon_id')->index();
            // 范围类，范围id
            $table->string('target_type');
            $table->bigInteger('target_id')->index();
            $table->engine('InnoDB');
            $table->comment('优惠券使用范围(目标)表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_targets');
    }
};
