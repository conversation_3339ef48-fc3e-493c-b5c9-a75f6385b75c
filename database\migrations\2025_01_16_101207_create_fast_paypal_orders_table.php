<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fast_paypal_orders', function (Blueprint $table) {
            $table->id();
            $table->string('no')->index();
            $table->bigInteger('paypal_order_id')->nullable()->comment('PayPal订单ID');
            $table->bigInteger('cart_id')->unsigned()->comment('购物车ID');
            $table->timestamps();
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fast_paypal_orders');
    }
};
