<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $pc_login_before_image_id
 * @property int $pc_login_after_image_id
 * @property int $mobile_login_before_image_id
 * @property int $mobile_login_after_image_id

 * @property bool $is_enabled
 * @property Image $pcLoginBeforeImage
 * @property Image $pcLoginAfterImage
 * @property Image $mobileLoginBeforeImage
 * @property Image $mobileLoginAfterImage
 */
class MembershipPageConfig extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_enabled' => 'boolean',
    ];

    protected $appends = [
        'pc_login_before_image_url',
        'pc_login_after_image_url',
        'mobile_login_before_image_url',
        'mobile_login_after_image_url'
    ];

    /**
     * 获取PC端登录前图片URL
     */
    public function getPcLoginBeforeImageUrlAttribute(): ?string
    {
        return $this->pcLoginBeforeImage?->url;
    }

    /**
     * 获取PC端登录后图片URL
     */
    public function getPcLoginAfterImageUrlAttribute(): ?string
    {
        return $this->pcLoginAfterImage?->url;
    }

    /**
     * 获取移动端登录前图片URL
     */
    public function getMobileLoginBeforeImageUrlAttribute(): ?string
    {
        return $this->mobileLoginBeforeImage?->url;
    }

    /**
     * 获取移动端登录后图片URL
     */
    public function getMobileLoginAfterImageUrlAttribute(): ?string
    {
        return $this->mobileLoginAfterImage?->url;
    }

    /**
     * PC端登录前图片关联
     */
    public function pcLoginBeforeImage(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'pc_login_before_image_id');
    }

    /**
     * PC端登录后图片关联
     */
    public function pcLoginAfterImage(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'pc_login_after_image_id');
    }

    /**
     * 移动端登录前图片关联
     */
    public function mobileLoginBeforeImage(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'mobile_login_before_image_id');
    }

    /**
     * 移动端登录后图片关联
     */
    public function mobileLoginAfterImage(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'mobile_login_after_image_id');
    }

    /**
     * 获取配置（单例模式）
     */
    public static function getConfig(): self
    {
        return static::with([
            'pcLoginBeforeImage',
            'pcLoginAfterImage',
            'mobileLoginBeforeImage',
            'mobileLoginAfterImage'
        ])->first() ?: new static();
    }

    /**
     * 根据设备和登录状态获取对应的图片URL
     */
    public function getImageByDeviceAndLogin(string $device, bool $isLoggedIn): ?string
    {
        $prefix = $device . '_login_' . ($isLoggedIn ? 'after' : 'before');

        return $this->{$prefix . '_image_url'};
    }
}
