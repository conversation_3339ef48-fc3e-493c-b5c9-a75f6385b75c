<?php

namespace App\Admin\Controllers;

use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Attachment;
use App\Models\AttributeValue;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\Attribute;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class ProductVariantController extends Controller
{
    /**
     * 变体列表
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Product $product, Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(ProductVariant::class, $request)
            ->with(['image:id,path,disk,module', 'colorAttribute', 'sizeAttribute'])
            ->where('product_id', $product->id)
            ->allowedSorts(['sort, id'])
            ->defaultSort('-sort');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 新增变体
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function store(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'price' => ['numeric', 'required'],
            'original_price' => ['numeric', 'required'],
            'sort' => ['numeric'],
            'stock' => ['numeric'],
            'min_show_stock' => ['numeric'],
            'color_attribute_value_id' => ['required', new Exists(AttributeValue::class, 'id')],
            'size_attribute_value_id' => ['nullable', new Exists(AttributeValue::class, 'id')]
        ]);

        try {
            DB::beginTransaction();
            // 检查变体是否已存在
            $exists = ProductVariant::query()
                ->where('product_id', $product->id)
                ->where('color_attribute_value_id', $validated['color_attribute_value_id'])
                ->where('size_attribute_value_id', $validated['size_attribute_value_id'])
                ->exists();
            if ($exists) {
                throw new DataException('该颜色和尺码组合的变体已存在');
            }

            // 更新图片
            $color_id = Arr::get($validated, 'color_attribute_value_id', 0);
            $images = $product->colors()->where('color_id', $color_id)->first()->images;

            // 新增变体
            $variant = $product->variants()->create([
                ...$validated,
                'image_id' => $images->first()?->id,
                'weight' => 0.3
            ]);

            $imagesToSync = Arr::mapWithKeys($images->toArray(), function ($image) {
                return [$image['id'] => ['sort' => $image['sort'] ?? 0]];
            });
            $variant->images()->sync($imagesToSync);

            // 商品信息
            $product->refresh();
            $product->update([
                'origin_price' => $product->variants()->min('original_price'),
                'min_price' => $product->variants()->min('price'),
                'max_price' => $product->variants()->max('price'),
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($variant);
    }

    /**
     * 更新变体
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\ProductVariant $variant
     * @throws \App\Exceptions\DataException
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function update(Request $request, ProductVariant $variant): JsonResource
    {
        $validated = $request->validate([
            'price' => ['numeric', 'required'],
            'original_price' => ['numeric', 'required'],
            'sort' => ['numeric'],
            'stock' => ['numeric'],
            'min_show_stock' => ['numeric'],
            'is_publish' => ['boolean'],
            'color_attribute_value_id' => ['required', new Exists(AttributeValue::class, 'id')],
            'size_attribute_value_id' => ['nullable', new Exists(AttributeValue::class, 'id')],
            'weight' => ['required', 'numeric']
        ]);
        if ($variant->product->is_publish && $variant->is_publish) {
            throw new DataException('变体已上架，无法修改');
        }

        try {
            DB::beginTransaction();
            // 检查变体是否重复
            $exists = ProductVariant::query()
                ->where('product_id', $variant->product_id)
                ->where('color_attribute_value_id', $validated['color_attribute_value_id'])
                ->where('size_attribute_value_id', $validated['size_attribute_value_id'])
                ->where('id', '!=', $variant->id)  // 排除自身
                ->exists();

            if ($exists) {
                throw new DataException('该颜色和尺码组合的变体已存在');
            }
            // 更新图片
            $color_id = Arr::get($validated, 'color_attribute_value_id', 0);
            $images = $variant->product->colors()->where('color_id', $color_id)->first()->images;

            // 变种信息
            $variant->update([
                ...$validated,
                'image_id' => $images->first()?->id,
            ]);

            $imagesToSync = Arr::mapWithKeys($images->toArray(), function ($image) {
                return [$image['id'] => ['sort' => $image['sort'] ?? 0]];
            });
            $variant->images()->sync($imagesToSync);

            // 商品信息
            $variant->refresh();
            $variant->product->update([
                'origin_price' => $variant->product->variants()->min('original_price'),
                'min_price' => $variant->product->variants()->min('price'),
                'max_price' => $variant->product->variants()->max('price'),
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($variant);
    }

    /**
     * 展示变体
     * @param \App\Models\Product\ProductVariant $variant
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(ProductVariant $variant): JsonResource
    {
        $variant->loadMissing([
            'images',
            'image',
            'colorAttribute',
            'sizeAttribute',
        ]);
        $variant->images = $variant->images->map(function ($image) {
            // 将 `sort` 提取并放到顶级
            $image->sort = $image->pivot->sort;
            // unset($image->pivot);
            return $image;
        });

        return JsonResource::make($variant);
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(ProductVariant::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        $errors = [];
        foreach ($ids as $id) {
            $variant = ProductVariant::query()->find($id);
            if ($variant->product->is_publish && $variant->is_publish) {
                $errors[] = "变体{$variant->spu}已上架，无法删除";
            } else {
                $variant->delete();
            }
        }

        // 返回错误
        if ($errors) {
            throw new DataException(implode(';', $errors));
        }
        return response()->json();
    }

    /**
     * Summary of attachedInfo
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function attachedInfo(Request $request)
    {
        $colors = QueryBuilder::for(AttributeValue::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('value'),
            ])
            ->where('attribute_id', Attribute::query()->where('name', 'color')->first()->id)
            ->select([DB::raw('value as label'), DB::raw('id as value')])
            ->get();
        $sizes = QueryBuilder::for(AttributeValue::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('value'),
            ])
            ->where('attribute_id', Attribute::query()->where('name', 'size')->first()->id)
            ->select([DB::raw('value as label'), DB::raw('id as value')])
            ->get();

        return JsonResource::make([
            'colors' => $colors,
            'sizes' => $sizes
        ]);
    }

    /**
     * 修改部分值
     * @param Request $request
     * @param ProductVariant $variant
     * @return JsonResource
     */
    public function patchUpdate(Request $request, ProductVariant $variant): JsonResource
    {
        $validated = $request->validate([
            'is_publish' => ['bool'],
        ]);
        $variant->update($validated);
        return JsonResource::make($variant);
    }

    /**
     * 批量更新同一产品的所有变体价格
     * @param Request $request
     * @param Product $product
     * @return JsonResponse
     */
    public function batchUpdateProductVariantsPrice(Request $request, Product $product): JsonResponse
    {
        $validated = $request->validate([
            'price' => ['required', 'numeric', 'min:0'],
            'original_price' => ['required', 'numeric', 'min:0'],
        ]);

        try {
            DB::beginTransaction();

            // 检查价格逻辑
            if ($validated['original_price'] < $validated['price']) {
                throw new DataException('原价不能小于实际价格');
            }

            // 检查是否有已上架的变体
            $publishedVariants = ProductVariant::where('product_id', $product->id)
                ->where('is_publish', true)
                ->get();

            if ($product->is_publish && $publishedVariants->isNotEmpty()) {
                $skus = $publishedVariants->pluck('sku')->join(', ');
                throw new DataException("以下变体已上架，无法修改：{$skus}");
            }

            // 直接更新所有变体价格
            ProductVariant::where('product_id', $product->id)
                ->update([
                    'price' => $validated['price'],
                    'original_price' => $validated['original_price']
                ]);

            // 更新商品价格范围（所有变体价格相同，直接使用设置的价格）
            $product->update([
                'origin_price' => $validated['original_price'],
                'min_price' => $validated['price'],
                'max_price' => $validated['price'],
            ]);

            DB::commit();
            return response()->json(['message' => '价格更新成功']);
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
    }
}
