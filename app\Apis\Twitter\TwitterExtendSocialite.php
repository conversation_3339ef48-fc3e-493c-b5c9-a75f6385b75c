<?php

namespace App\Apis\Twitter;


use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\Twitter\Provider;

class TwitterExtendSocialite extends \SocialiteProviders\Twitter\TwitterExtendSocialite
{
    /**
     * Register the provider.
     *
     * @param \SocialiteProviders\Manager\SocialiteWasCalled $socialiteWasCalled
     */
    public function handle(SocialiteWasCalled $socialiteWasCalled)
    {
        $socialiteWasCalled->extendSocialite('twitter', Provider::class, TwitterServer::class);
    }

}
