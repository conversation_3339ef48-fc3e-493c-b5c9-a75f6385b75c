<?php

namespace App\Console\Commands;

use App\Apis\Airwallex\AirwallexService;
use App\Apis\Paypal\PaypalService;
use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Jobs\EmailRuleNotice;
use App\Jobs\Product\ProductCommentStatic;
use App\Jobs\Product\ProductSyncErp;
use App\Jobs\Product\ProductSyncSaleNum;
use App\Mail\TemplateEmail;
use App\Models\Attachment;
use App\Models\AttributeValue;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Collection;
use App\Models\Comment;
use App\Models\Email\EmailTemplate;
use App\Models\Enums\AttachmentModuleEnum;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Promotion\PromotionProductTypeEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\ExternalClient;
use App\Models\Order\Order;
use App\Models\Product\Product;
use App\Models\Product\ProductColor;
use App\Models\Product\ProductVariant;
use App\Models\Promotion;
use App\Models\User\User;
use App\Models\User\UserWallet;
use App\Services\ProductService;
use Carbon\Carbon;
use DB;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始替换 meta_title 字段...');

        try {
            $count = DB::table('collections')
                ->where('meta_title', 'like', '%Ardoreva%')
                ->update([
                    'meta_title' => DB::raw("REPLACE(meta_title, 'Ardoreva', 'Ardor Eva')")
                ]);

            $this->info("替换完成！共更新了 {$count} 条记录。");
        } catch (\Exception $e) {
            $this->error("替换过程中发生错误：" . $e->getMessage());
        }
        exit();
        $paypal_order_id = '35K704489D730302B';
        $orderResponse = PaypalService::showOrderDetails($paypal_order_id);
        $transaction_id = Arr::get($orderResponse, 'purchase_units.0.payments.captures.0.id');
        var_dump($transaction_id);
        exit();
        $cart_items = CartItem::query()->limit(10)->select(['id','price'])->get();
        $cart_items = $cart_items->sortByDesc('price');
        var_dump($cart_items->toArray());
        dd();
        $products = Product::query()->with('image', 'colors.images')->get();
        foreach ($products as $product) {
            try {
                // 产品图片
                if ($product->image) {
                    if ($product->image?->module?->value != AttachmentModuleEnum::Product->value) {
                        if ($image = ProductService()->uploadFileFromUrl($product->image->url)) {
                            $product->image_id = $image->id;
                            $product->save();
                        }
                    }
                }

                // 产品颜色图片
                foreach ($product->colors as $color) {
                    $update = false;
                    $imagesToSync = [];
                    foreach ($color->images as $color_image) {
                        if ($color_image?->module?->value != AttachmentModuleEnum::Product->value) {
                            if ($image = ProductService()->uploadFileFromUrl($color_image->url)) {
                                $imagesToSync[$image->id] = $color_image->pivot->sort;
                                $update = true;
                            } else {
                                $imagesToSync[$color_image->id] = $color_image->pivot->sort;
                            }
                        } else {
                            $imagesToSync[$color_image->id] = $color_image->pivot->sort;
                        }
                    }
                    if ($update) {
                        $color->images()->sync($imagesToSync);
                        $color->refresh();
                        $variants = $product->variants()->where('color_attribute_value_id', $color->color_id)->get();
                        // 同步对应颜色的变体
                        $variants->map(function ($variant) use ($color, $imagesToSync) {
                            $variant->update([
                                'sort' => $color->sort,
                                'image_id' => $color->images()->first()->id ?? 0,
                            ]);
                            $variant->images()->sync($imagesToSync);
                        });
                    }
                }
            } catch (\Exception $e) {
                echo $product->id . ':' . $e->getMessage();
            }

        }
        dd(1);
        $products = Product::query()->with('variants')->get();
        $products->each(function ($product) {
            $product->update([
                'title' => trim($product->title),
                'min_price' => $product->variants->min('price') ?: 0,
                'max_price' => $product->variants->max('price') ?: 0,
                'origin_price' => $product->variants->min('origin_price') ?: 0,
            ]);;
        });
        dd('success');
        $product = Product::query()->with('variants')->find(1);
        dd('success');
        $productColorWithMaxImages = DB::table('product_colors')
        ->leftJoin('product_color_images as images', 'product_colors.id', '=', 'images.product_color_id')
        ->selectRaw('product_colors.id, COUNT(images.id) as images_count')
        ->groupBy('product_colors.id')
        ->orderByDesc('images_count')
        ->first();

        if ($productColorWithMaxImages) {
            $idWithMaxImages = $productColorWithMaxImages->id;
            $maxImagesCount = $productColorWithMaxImages->images_count;
            // 输出结果
            dd(['id' => $idWithMaxImages, 'images_count' => $maxImagesCount]);
        } else {
            dd(null);
        }
        dd(trim(null));
        $attachments = Attachment::query()->get();
        foreach ($attachments as $attachment) {
            if ($attachment->module) {
                $module_path = $attachment->module->getPath();
                $attachment->path = Str::replace($module_path, '', $attachment->path);
                $attachment->save();
            }
        }
        dd(1);
        // 更新固定SKU的数据广告数据
        $promotions = Promotion::query()->where('type', PromotionTypeEnum::ProductType->value)->with('contents')->get();
        foreach ($promotions as $promotion) {
            if (count($promotion->contents)) {
                $product_ids = $promotion->contents->pluck('product_id')->toArray();
                $promotion->productContents()->updateOrCreate([
                    'type' => PromotionProductTypeEnum::Fixed->value,
                    'data' => [
                        'product_ids' => $product_ids,
                        'sort' => '-new_publish_at'
                    ]
                ]);
            }
            $promotion->contents()->delete();
        }
        dd(1);
        // 大码品类以及SKU转换
        $data = [
            'XL' => '1X',
            '2XL' => '2X',
            '3XL' => '3X',
            '4XL' => '4X',
            '5XL' => '5X',
        ];
        $collection = Collection::query()->find(19);
        $ids = $collection->products->pluck('id')->toArray();
        $products = Product::query()->whereIn('id', $ids)->get();
        foreach ($products as $product) {
            try {
                DB::beginTransaction();
                // 更新产品的SKU
                $product->update([
                    'category_id' => 4,
                    'spu' => substr_replace($product->spu, 'AP', 0, 2)
                ]);
                $variants = $product->variants;
                foreach ($variants as $variant) {
                    $size = $variant->sizeAttribute->value; // 获取尺寸
                    $newSize = Arr::get($data, $size, $size);
                    if (!$value_id = AttributeValue::query()->where('value', $newSize)->where('attribute_id', 2)->pluck('id')->first()) {
                        $value = new AttributeValue();
                        $value->fill([
                            'attribute_id' => 2,
                            'value' => $newSize,
                        ])->save();
                        $value_id = $value->id;
                    }
                    $variant->update([
                        'size_attribute_value_id' => $value_id,
                        'sku' => $variant->generateSku()
                    ]);
                }
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error('SKU: ' . $product->spu . ':' . $e->getMessage());
                continue;
            }
        }
        dd(1);
        dd(User::query()
            ->join('user_groups', 'users.id', '=', 'user_groups.user_id')
            ->whereIn('user_groups.group_id', [3])
            ->distinct()
            ->pluck('users.email')
            ->toArray());
        ProductCommentStatic::dispatch(1)->onQueue(QueueKey::Comment->value);
        dd(1);
        $variants = ProductVariant::query()->with('images')->get();
        $colors = [];
        foreach ($variants as $variant) {
            if (!Arr::exists($colors, $variant->product_id . '-' . $variant->color_attribute_value_id)) {
                $colors[$variant->product_id . '-' . $variant->color_attribute_value_id] = [
                    'product_id' => $variant->product_id,
                    'color_id' => $variant->color_attribute_value_id,
                    'sort' => $variant->sort,
                    'images' => []
                ];
            }
            foreach (Arr::get($variant, 'images', []) as $image) {
                $colors[$variant->product_id . '-' . $variant->color_attribute_value_id]['images'][$image->id] = ['sort' => $image->pivot->sort];
            }
        }
        foreach ($colors as $color) {
            $images = Arr::pull($color, 'images');
            $productColor = new ProductColor();
            $productColor->fill($color)->save();
            if ($images) {
                $productColor->images()->sync($images);
            }
        }
        dd(1);
        $products = Product::query()->get();
        foreach ($products as $product) {
            $product->update([
                'slug_title' => $product->generateSlugTitle()
            ]);
        }
        dd(1);
        $product = Product::query()->find(1);
        $productInfo = $product->loadMissing([
            'brand:id,name',
            'image',
            'variants',
            'variants.colorAttribute',
            'variants.sizeAttribute',
            'variants.images',
            'variants.image'
        ]);
        $timestamp = time();
        $notice = Str::random(5);
        $client = ExternalClient::query()->where('name', 'erp')->first();
        $url = env("ERP_URL") . "/supershare/api/api_product_sync.php?submitEdit=true";
        $sign = md5("{$client->key}{$client->secret}{$timestamp}{$notice}");
        $newProduct = $productInfo->toArray();
        $newProduct['images'] = [$productInfo->image];
        $param = [
            'product' => $newProduct,
            'key' => $client->key,
            'notice' => $notice,
            'timestamp' => $timestamp,
            'sign' => $sign,
            'update_image' => true // 手动执行直接替换
        ];
        $response = Http::timeout(30000)->post($url, $param);
        dd($response->body());

        dd(Storage::disk('public')->allFiles('storage/import'));
        $order = Order::query()->latest()->first();
        $res = paymentService()->payByPaypal($order);
        dd($res);
        $v = Product::query()->find(27);
        ProductSyncSaleNum::dispatchSync(Order::query()->find(2));
        /**
         * @var Product $product
         */
        $model = Order::query()->find(220);
        $wallet = UserWallet::query()->first();
        userWalletService()->walletChangeInviteAmount($wallet, WalletChangeTypeEnum::OrderCompleted, 20);
        //        EmailRuleNotice::dispatchSync(EmailRuleEventEnum::OrderPaidSuccess, $model);
        dd(1);


        // ProductSyncErp::dispatchSync(Product::query()->find(23));
        dd(1);
        dd(Carbon::parse("2024-12-31")->submonth());
        //
        $order = Order::query()->find(2);

        EmailRuleNotice::dispatch(EmailRuleEventEnum::LogisticsInspection, order: $order)->onQueue(QueueKey::Default ->value);
        dd(1);
        //        $model = Order::query()->find(25);
//        CouponGrantCheck::dispatchSync(CouponGrantType::ShareInvite, $model);
//        dd(1);
        $model = Comment::query()->find(26);
        CouponGrantCheck::dispatchSync(CouponGrantType::Comment, $model);
        dd(1);
        //
        $response = Http::withOptions([
            'verify' => false,
            'proxy' => [
                //                'http' => "http://192.168.0.111:7890",
//                'https' => "http://192.168.0.111:7890",
            ],
        ])->post('https://api.x.com/oauth/request_token');

        dd($response->status());


        AirwallexService::getProducts();
        dd(111);
        //
        $order = Order::query()->find(2);

        EmailRuleNotice::dispatchSync(EmailRuleEventEnum::OrderPaidSuccess, order: $order);

        dd(1);

        /**
         * @var EmailTemplate $template
         */
        $template = EmailTemplate::query()->first();

        $res = Mail::to(['<EMAIL>'])
            ->later(
                now()->addSeconds(10),
                new TemplateEmail($template, ['name' => '测试', 'data' => ['name' => '法外狂徒']])
            );
        //            ->later(now()->addSeconds(2), new TemplateEmail($template, ['name' => '测试']));

        dd(1, $res);
        $htmlTemplate = '<!DOCTYPE html>
        <h1>Welcome, {{ $name }}</h1><p>Your email is: {{ $email }}</p>
        <html>';
        $html = Blade::render($htmlTemplate, [
            'name' => 'Samantha',
            'email' => '<EMAIL>'
        ]);
        dd($html);
        dd(array_diff(['zhangsan', 'lisi', 'wangwu'], ['lisi']));


        return Command::SUCCESS;
    }
}
