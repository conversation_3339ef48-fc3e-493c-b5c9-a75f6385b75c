<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $avatar
 * @property string $email
 * @property string $name
 * @property string $nickname
 * @property bool $email_verified
 * @property User $user
 */
class TwitterUser extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
    ];


    /**
     * 用户
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }


}
