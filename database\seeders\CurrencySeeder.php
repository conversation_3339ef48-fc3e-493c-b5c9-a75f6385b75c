<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currencies = [
            [
                'currency_code' => 'USD',
                'symbol' => '$',
                'currency_name' => 'United States Dollar',
                'exchange_rate' => 1.00,
                'symbol_position' => 'before',
                'active' => true,
            ],
            [
                'currency_code' => 'CNY',
                'symbol' => '￥',
                'currency_name' => 'Chinese Yuan',
                'exchange_rate' => 6.45,
                'symbol_position' => 'before',
                'active' => true,
            ],
            [
                'currency_code' => 'EUR',
                'symbol' => '€',
                'currency_name' => 'Euro',
                'exchange_rate' => 0.85,
                'symbol_position' => 'after',
                'active' => true,
            ],
        ];

        DB::table('currencies')->insert($currencies);
        $this->command->info('Seeded: Successfully generated the corresponding currency.');
    }
}
