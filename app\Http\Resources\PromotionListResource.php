<?php

namespace App\Http\Resources;

use App\Constants\CacheKey;
use App\Models\AssociatedCollection;
use App\Models\AssociatedMode;
use App\Models\Collection;
use App\Models\Enums\CollectionJumpTypeEnum;
use App\Models\Enums\Promotion\PromotionPositionEnum;
use App\Models\Enums\Promotion\PromotionProductTypeEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Product\Product;
use App\Models\PromotionProduct;
use App\Models\User\User;
use Arr;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @property  Product $resource
 */
class PromotionListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $contents = [];
        // 广告内容
        if ($this->resource->type == PromotionTypeEnum::ProductType->value) {
            // 产品内容
            $contents = $this->productContents($request, $this->resource->productContents, $this->resource->position);
        } else {
            // 普通内容
            $this->resource->contents->each(function ($content) use (&$contents) {
                $contents[] = [
                    'id' => $content->id,
                    'promotion_id' => $content->promotion_id,
                    'collection_id' => $content->collection_id,
                    'title' => $content->title,
                    'attachment_id' => $content->attachment_id,
                    'sort' => $content->sort,
                    'link' => $content->link,
                    'collection' => $content->collection,
                    'attachment' => $content->attachment,
                ];
            });
        }

        return [
            'id' => $this->resource->id,
            'title' => $this->resource->title,
            'description' => $this->resource->description,
            'button_text' => $this->resource->button_text,
            'button_link' => $this->resource->button_link,
            'button_background_color' => $this->resource->button_background_color,
            'position' => $this->resource->position,
            'alignment_method' => $this->resource->alignment_method,
            'type' => $this->resource->type,
            'status' => $this->resource->status,
            'sort' => $this->resource->sort,
            'equipment_type' => $this->resource->equipment_type,
            'contents' => $contents,
        ];
    }

    /**
     * 获取产品广告内容
     * @param \App\Models\PromotionProduct $promotion_product
     * @param mixed $position
     * @param mixed $product_ids
     * @return array
     */
    protected function productContents(Request $request, PromotionProduct $promotion_product, $position)
    {
        // 产品广告配置内容
        $type = $promotion_product->type;
        $data = $promotion_product->data;
        $request_data = $request->all();
        // 产品主要关联数据
        $with = [
            'image:id,path,disk,module',
            'variants' => function ($query) {
                $query->select(['id', 'product_id', 'price', 'image_id', 'original_price', 'color_attribute_value_id', 'size_attribute_value_id', 'stock'])
                    ->where('is_publish', true)
                    ->orderBy('sort', 'desc');
            },
            'variants.images:id,path,disk,module',
            'variants.image:id,path,disk,module',
            'variants.colorAttribute',
            'variants.sizeAttribute',
            'colors.color:id,value,extra',
            'colors.images:id,path,disk,module',
            'commentStatic'
        ];
        // 允许的排序
        $allowedSorts = [
            'min_price',
            'max_price',
            'first_publish_at',
            'new_publish_at',
            'title',
            'id',
            'sale_num',
            'is_featured'
        ];
        $userId = Auth::id();
        // 产品广告内容
        $products = [];
        // 排序
        $sort = Arr::get($data, 'sort', '-new_publish_at');
        switch ($type) {
            case PromotionProductTypeEnum::Fixed->value:
                // 查询商品
                $query = QueryBuilder::for(Product::class)
                    ->with($with)
                    ->where('is_publish', true)
                    ->when($userId, function (Builder $query, $userId) {
                        // 是否收藏
                        $query->withExists([
                            'collectUserIds' => function ($builder) use ($userId) {
                                $builder->where('user_id', $userId);
                            }
                        ]);
                    })
                    ->whereIn('id', array_unique(Arr::get($data, 'product_ids', [])))
                    ->allowedSorts($allowedSorts)
                    ->defaultSort($sort);
                if (Arr::exists($request_data, 'limit')) {
                    $query->limit(Arr::get($request_data, 'limit'));
                }
                $products = $query->get();
                break;
            case PromotionProductTypeEnum::Customize->value:
                // 数量
                if (Arr::exists($request_data, 'limit')) {
                    $limit = Arr::get($request_data, 'limit');
                } else {
                    $limit = Arr::get($data, 'limit');
                }
                switch ($position) {
                    case PromotionPositionEnum::IndexMiddle->value:
                    case PromotionPositionEnum::MobileMenu->value:
                    case PromotionPositionEnum::CartMiddle->value:
                    case PromotionPositionEnum::CartModalMiddle->value:
                        // 分类是否使用推荐项(注意去重)
                        if (Arr::get($data, 'collection_sell', false)) {
                            $query_collection_ids = array_unique(AssociatedCollection::query()
                                ->whereIn('collection_id', Arr::get($data, 'collection_ids', []))
                                ->pluck('associated_id')
                                ->toArray());
                        } else {
                            $query_collection_ids = array_unique(Arr::get($data, 'collection_ids', []));
                        }
                        // 风格是否使用推荐项(注意去重)
                        if (Arr::get($data, 'mode_sell', false)) {
                            $query_mode_ids = array_unique(AssociatedMode::query()
                                ->whereIn('mode_id', Arr::get($data, 'mode_ids', []))
                                ->pluck('associated_id')
                                ->toArray());
                        } else {
                            $query_mode_ids = array_unique(Arr::get($data, 'mode_ids', []));
                        }
                        // 查询商品
                        $products = QueryBuilder::for(Product::class)
                            ->with($with)
                            ->when($userId, function (Builder $query, $userId) {
                                // 是否收藏
                                $query->withExists([
                                    'collectUserIds' => function ($builder) use ($userId) {
                                        $builder->where('user_id', $userId);
                                    }
                                ]);
                            })
                            ->where('is_publish', true)
                            ->where(function ($query) use ($query_collection_ids, $query_mode_ids) {
                                $query->whereHas('collectionIds', function ($query) use ($query_collection_ids) {
                                    $query->whereIn('collection_id', $query_collection_ids);
                                })->orWhereHas('modeIds', function ($query) use ($query_mode_ids) {
                                    $query->whereIn('mode_id', $query_mode_ids);
                                });
                            })
                            ->allowedSorts($allowedSorts)
                            ->defaultSort($sort)
                            ->limit($limit)
                            ->get();
                        break;
                    case PromotionPositionEnum::ProductMatch->value:
                    case PromotionPositionEnum::ProductMiddle->value:
                        if (Arr::exists($request_data, 'slug_title')) {
                            $product = Product::query()
                                ->where('slug_title', Arr::get($request_data, 'slug_title'))
                                ->first();
                            $attributes = Arr::get($data, 'attributes', []);
                            // 用于查询的分类IDs(注意去重)
                            $query_collection_ids = [];
                            if (in_array('collection', $attributes)) {
                                if (Arr::get($data, 'collection_sell', false)) {
                                    $query_collection_ids = array_unique(AssociatedCollection::query()
                                        ->whereIn('collection_id', $product->collections()
                                            ->where('is_exclude', false)
                                            ->where('jump_type', CollectionJumpTypeEnum::Collection->value)
                                            ->pluck('collection_id')->toArray())
                                        ->pluck('associated_id')
                                        ->toArray());
                                } else {
                                    $query_collection_ids = array_unique($product->collections()
                                        ->where('is_exclude', false)
                                        ->where('jump_type', CollectionJumpTypeEnum::Collection->value)
                                        ->pluck('collection_id')
                                        ->toArray());
                                }
                            }
                            // 用于查询的风格IDs(注意去重)
                            $query_mode_ids = [];
                            if (in_array('mode', $attributes)) {
                                if (Arr::get($data, 'mode_sell', false)) {
                                    $query_mode_ids = array_unique(AssociatedMode::query()
                                        ->whereIn('mode_id', $product->modeIds->pluck('mode_id')->toArray())
                                        ->pluck('associated_id')
                                        ->toArray());
                                } else {
                                    $query_mode_ids = array_unique($product->modeIds->pluck('mode_id')->toArray());
                                }
                            }
                            // 查询商品
                            $products = QueryBuilder::for(Product::class)
                                ->with($with)
                                ->when($userId, function (Builder $query, $userId) {
                                    // 是否收藏
                                    $query->withExists([
                                        'collectUserIds' => function ($builder) use ($userId) {
                                            $builder->where('user_id', $userId);
                                        }
                                    ]);
                                })
                                ->where('is_publish', true)
                                ->where(function ($query) use ($query_collection_ids, $query_mode_ids) {
                                    $query->whereHas('collectionIds', function ($query) use ($query_collection_ids) {
                                        $query->whereIn('collection_id', $query_collection_ids);
                                    })->orWhereHas('modeIds', function ($query) use ($query_mode_ids) {
                                        $query->whereIn('mode_id', $query_mode_ids);
                                    });
                                })
                                ->where('id', '!=', $product->id)
                                ->allowedSorts($allowedSorts)
                                ->defaultSort($sort)
                                ->limit($limit)
                                ->get();

                            // 兜底计划
                            if ($products->isEmpty()) {
                                $products = QueryBuilder::for(Product::class)
                                ->with($with)
                                ->when($userId, function (Builder $query, $userId) {
                                    // 是否收藏
                                    $query->withExists([
                                        'collectUserIds' => function ($builder) use ($userId) {
                                            $builder->where('user_id', $userId);
                                        }
                                    ]);
                                })
                                ->where('is_publish', true)
                                ->whereIn(
                                    'collection_id', Collection::query()->where('is_exclude', true)->pluck('id')->toArray()
                                )
                                ->where('id', '!=', $product->id)
                                ->allowedSorts($allowedSorts)
                                ->defaultSort($sort)
                                ->limit($limit)
                                ->get();
                            }
                        }
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        // 分配商品给contents
        $contents = [];
        foreach ($products as $product) {
            $contents[] = [
                'product' => ProductListResource::make($product)
            ];
        }
        return $contents;
    }
}
