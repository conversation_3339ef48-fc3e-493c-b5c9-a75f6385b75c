<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
//        $this->call(BannerSeeder::class);
//        $this->call(FooterSeeder::class);
//        $this->call(CurrencySeeder::class);
        $this->call(CollectionTestSeeder::class);
        $this->call(PermissionsSeeder::class);
        // $this->call(MembershipLevelSeeder::class); // 可选：创建默认会员等级
        // $this->call(TaskPointConfigSeeder::class); // 可选：创建默认任务积分配置
    }
}
