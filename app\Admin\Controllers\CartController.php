<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Cart;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CartController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CartsUpdate)->only(['update']);
        $this->hasPermissionOr(Permissions::CartsUpdate, Permissions::CartsIndex)->only(['index', 'show']);
        $this->hasPermissionOr(Permissions::CartsStat)->only(['stat']);
    }

    public function index(Request $request)
    {
        $builder = QueryBuilder::for(Cart::class)
            ->whereHas('items')
            ->AllowedFilters([
                AllowedFilter::exact('is_checkout'),
                AllowedFilter::exact('shipping_id'),
                AllowedFilter::callback('no', function (Builder $builder, $value) {
                    $builder->whereHas('order', function ($query) use ($value) {
                        $query->where('no', 'like', '%' . $value . '%');
                    });
                }),
                AllowedFilter::callback('user_type', function (Builder $builder, $value) {
                    if ($value == 1) {
                        $builder->whereNotNull('user_id');
                    } else if ($value == 0) {
                        $builder->whereNull('user_id');
                    }
                }),
                AllowedFilter::callback('start_at', function (Builder $builder, $value) {
                    $builder->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('end_at', function (Builder $builder, $value) {
                    $builder->where('created_at', '<=', $value);
                })
            ])
            ->with([
                'user:id,email,first_name,last_name',
                'shipping:id,name',
                'order:id,no'
            ])
            ->allowedSorts(['id', 'is_checkout', 'created_at'])
            ->defaultSort('-id');
         // 分页
         $res = $builder->paginate($this->getPerPage());
         return JsonResource::collection($res);
    }

    public function show(Cart $cart)
    {
        $cart->loadMissing([
            'user:id,email,first_name,last_name,created_at',
            'shipping:id,name',
            'items',
            'items.product',
            'items.productVariant.image:id,path,disk,module',
        ]);
        // user的订单总额
        if ($cart->user) {
            $cart->user->total = $cart->user->orders()->sum('total');
            // 有效订单数
            $cart->user->valid_orders = $cart->user->orders()->where('valid', true)->count();
        }
        return new JsonResource($cart);
    }

    public function stat()
    {
        //近7天有效购物车数量
        $cartNum = QueryBuilder::for(Cart::class)
            ->whereHas('items')
            ->where('created_at', '>=', now()->subDays(7))
            ->count();
        //近7天有效购物车总金额
        $cartTotal = QueryBuilder::for(Cart::class)
            ->whereHas('items')
            ->where('created_at', '>=', now()->subDays(7))
            ->sum('total');
        //近7天平均购物车金额
        $cartAvgPrice = $cartNum ? round($cartTotal/$cartNum, 2) : 0;
        //近7天有效购物车商品总数
        $cartItemsCount = QueryBuilder::for(Cart::class)
            ->whereHas('items')
            ->where('created_at', '>=', now()->subDays(7))
            ->withSum('items', 'num')
            ->get()
            ->sum('items_sum_num');
        //近7天平均购物车商品数量
        $cartAvgCount = $cartNum ? round($cartItemsCount/$cartNum, 2) : 0;
        return JsonResource::make([
            'cart_num' => $cartNum,
            'cart_avg_price' => $cartAvgPrice,
            'cart_avg_count' => $cartAvgCount,
            'cart_items_count' => $cartItemsCount,
            'cart_total' => round($cartTotal,2)
        ]);
    }
}
