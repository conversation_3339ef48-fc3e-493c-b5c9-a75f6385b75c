<?php

namespace App\Models\Enums\Promotion;

use Illuminate\Support\Arr;

enum PromotionPositionEnum: string
{
    case IndexMiddle = 'index_middle';
    case ProductMiddle = 'product_middle';
    case ProductMatch = 'product_match';
    case CartMiddle = 'cart_middle';
    case CartModalMiddle = 'cart_modal_middle';
    case TopNavSearchBox = 'top_nav_search_box';
    case TrackingOrder = 'tracking_order';
    case MobileMenu = 'mobile_menu';

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::IndexMiddle->value => '首页',
            self::ProductMiddle->value => '详情页',
            self::ProductMatch->value => '详情页推荐栏',
            self::CartMiddle->value => '购物车页',
            self::CartModalMiddle->value => '购物车弹框',
            self::TopNavSearchBox->value => '顶部搜索导航栏',
            self::TrackingOrder->value => '订单物流页',
            self::MobileMenu->value => '移动端菜单',
        ];
    }
}
