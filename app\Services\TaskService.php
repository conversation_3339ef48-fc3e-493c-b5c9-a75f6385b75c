<?php

namespace App\Services;

use App\Models\Task\TaskPointConfig;
use App\Models\Task\UserTaskRecord;
use App\Models\User\User;
use App\Models\Enums\User\PointChangeTypeEnum;
use App\Services\UserPointService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskService
{
    /**
     * 检查用户是否可以执行任务
     */
    public function canExecuteTask(User $user, TaskPointConfig $task): bool
    {
        // 检查任务是否激活
        if (!$task->is_active) {
            return false;
        }

        // 检查今日限制
        if ($task->daily_limit > 0) {
            $todayCount = $this->getTodayCompletedCount($user, $task);
            if ($todayCount >= $task->daily_limit) {
                return false;
            }
        }

        // 检查总限制
        if ($task->total_limit > 0) {
            $totalCount = $this->getTotalCompletedCount($user, $task);
            if ($totalCount >= $task->total_limit) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取用户今日剩余可执行次数
     */
    public function getRemainingCount(User $user, TaskPointConfig $task): int
    {
        if ($task->daily_limit <= 0) {
            return -1; // 无限制
        }

        $todayCount = $this->getTodayCompletedCount($user, $task);
        return max(0, $task->daily_limit - $todayCount);
    }

    /**
     * 执行任务
     */
    public function executeTask(User $user, string $taskType, ?string $channel = null, array $extraData = []): array
    {
        // 查找任务配置
        $taskConfig = TaskPointConfig::where('task_type', $taskType)
            ->where('channel', $channel)
            ->where('is_active', true)
            ->first();

        if (!$taskConfig) {
            throw new \Exception('任务配置不存在或已禁用');
        }

        // 检查是否可以执行
        if (!$this->canExecuteTask($user, $taskConfig)) {
            throw new \Exception('任务执行次数已达上限');
        }

        DB::beginTransaction();
        try {
            // 创建任务记录
            $taskRecord = $this->createTaskRecord($user, $taskConfig, $extraData);

            // 发放积分
            $this->awardPoints($user, $taskConfig, $taskRecord);

            DB::commit();

            Log::info('Task executed successfully', [
                'user_id' => $user->id,
                'task_type' => $taskType,
                'channel' => $channel,
                'points_earned' => $taskConfig->points_per_action,
            ]);

            return [
                'task_record' => $taskRecord,
                'points_earned' => $taskConfig->points_per_action,
                'remaining_count' => $this->getRemainingCount($user, $taskConfig),
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Task execution failed', [
                'user_id' => $user->id,
                'task_type' => $taskType,
                'channel' => $channel,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 创建任务记录
     */
    protected function createTaskRecord(User $user, TaskPointConfig $taskConfig, array $extraData = []): UserTaskRecord
    {
        return UserTaskRecord::createRecord(
            $user->id,
            $taskConfig->id,
            $taskConfig->task_type,
            $taskConfig->channel,
            $taskConfig->points_per_action,
            $extraData
        );
    }

    /**
     * 发放积分
     */
    protected function awardPoints(User $user, TaskPointConfig $taskConfig, UserTaskRecord $taskRecord): void
    {
        // 使用新的积分服务立即发放任务积分
        $pointService = new UserPointService();
        $pointService->addPoints(
            user: $user,
            points: $taskConfig->points_per_action,
            changeType: PointChangeTypeEnum::TaskReward,
            description: "任务奖励：{$taskConfig->task_name}",
            source: $taskRecord,
            immediate: true // 任务积分立即发放
        );
    }

    /**
     * 获取用户今日完成次数
     */
    protected function getTodayCompletedCount(User $user, TaskPointConfig $task): int
    {
        return UserTaskRecord::where('user_id', $user->id)
            ->where('task_type', $task->task_type)
            ->where('channel', $task->channel)
            ->today()
            ->count();
    }

    /**
     * 获取用户总完成次数
     */
    protected function getTotalCompletedCount(User $user, TaskPointConfig $task): int
    {
        return UserTaskRecord::where('user_id', $user->id)
            ->where('task_type', $task->task_type)
            ->where('channel', $task->channel)
            ->count();
    }

    /**
     * 获取用户任务统计
     */
    public function getUserTaskStats(User $user): array
    {
        // 使用新的积分记录系统获取任务积分统计
        $todayTaskPoints = $user->pointChangeRecords()
            ->where('change_type', PointChangeTypeEnum::TaskReward->value)
            ->whereDate('created_at', today())
            ->sum('change_amount');

        $totalTaskPoints = $user->pointChangeRecords()
            ->where('change_type', PointChangeTypeEnum::TaskReward->value)
            ->sum('change_amount');

        return [
            'today_tasks' => UserTaskRecord::byUser($user->id)->today()->count(),
            'today_points' => $todayTaskPoints,
            'total_tasks' => UserTaskRecord::byUser($user->id)->count(),
            'total_points' => $totalTaskPoints,
        ];
    }
}
