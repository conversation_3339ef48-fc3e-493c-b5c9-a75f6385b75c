<?php

namespace App\Http\Controllers;

use App\Models\ActivityNav;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spa<PERSON>\QueryBuilder\QueryBuilder;

class ActivityNavController extends Controller
{
    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(ActivityNav::class, $request)
            ->with([
                'image:id,path,disk,module'
            ])
            ->where('status', 1)
            ->allowedSorts(['sort'])
            ->defaultSort('-sort');

        $res = $builder->take(2)->get();
        return JsonResource::collection($res);
    }
}
