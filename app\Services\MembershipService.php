<?php

namespace App\Services;

use App\Models\User\User;
use App\Models\Order\Order;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\SharingRule;
use App\Models\Enums\User\PointChangeTypeEnum;
use App\Models\User\UserPointChangeRecord;
use App\Services\UserPointService;
use Illuminate\Support\Facades\DB;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use Carbon\Carbon;

class MembershipService
{
    /**
     * 检查并升级用户会员等级
     */
    public function checkAndUpgradeUserLevel(User $user): ?UserMembershipLevel
    {
        // 计算用户总消费金额
        $totalSpendAmount = $this->calculateUserTotalSpend($user);
        
        // 获取当前应该的会员等级
        $targetLevel = MembershipLevel::getLevelBySpendAmount($totalSpendAmount);
        
        if (!$targetLevel) {
            return null;
        }

        // 获取用户当前会员等级
        $currentUserLevel = UserMembershipLevel::getCurrentLevel($user->id);
        
        // 如果用户还没有会员等级或者需要升级
        if (!$currentUserLevel || $currentUserLevel->membership_level_id !== $targetLevel->id) {
            return $this->upgradeUserLevel($user, $targetLevel, $totalSpendAmount, $currentUserLevel);
        }

        // 更新消费金额
        if ($currentUserLevel) {
            $currentUserLevel->update(['total_spend_amount' => $totalSpendAmount]);
        }

        return $currentUserLevel;
    }

    /**
     * 升级用户会员等级
     */
    protected function upgradeUserLevel(
        User $user, 
        MembershipLevel $targetLevel, 
        float $totalSpendAmount, 
        ?UserMembershipLevel $currentUserLevel
    ): UserMembershipLevel {
        DB::beginTransaction();
        try {
            $fromLevelId = $currentUserLevel ? $currentUserLevel->membership_level_id : null;
            
            // 创建或更新用户会员等级
            $newUserLevel = UserMembershipLevel::createOrUpdate(
                $user->id, 
                $targetLevel->id, 
                $totalSpendAmount
            );

            // 记录升级历史
            if ($fromLevelId && $fromLevelId !== $targetLevel->id) {
                $newUserLevel->recordUpgrade($fromLevelId, $targetLevel->id, $totalSpendAmount);
            }

            DB::commit();
            return $newUserLevel;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 计算用户总消费金额
     */
    public function calculateUserTotalSpend(User $user): float
    {
        return $user->orders()
            ->where('paid_status', OrderPaidStatusEnum::Paid)
            ->sum('total');
    }

    /**
     * 处理订单完成后的积分奖励
     */
    public function processOrderPoints(Order $order): ?UserPointChangeRecord
    {
        if (!$order->user_id || $order->total <= 0) {
            return null;
        }

        $user = $order->user;
        if (!$user) {
            return null;
        }

        // 检查并升级会员等级
        $userLevel = $this->checkAndUpgradeUserLevel($user);
        
        if (!$userLevel) {
            return null;
        }

        // 计算积分
        $pointRate = $userLevel->membershipLevel->point_rate;
        $earnedPoints = $order->total * $pointRate;

        // 获取延迟发放天数
        $delayDays = $this->getPointDelayDays();
        $scheduledAt = $delayDays > 0 ? now()->addDays($delayDays) : null;

        DB::beginTransaction();
        try {
            // 使用新的积分服务
            $pointService = new UserPointService();
            $immediate = $delayDays === 0;

            $pointRecord = $pointService->addPoints(
                user: $user,
                points: $earnedPoints,
                changeType: PointChangeTypeEnum::OrderReward,
                description: "会员订单积分奖励",
                source: $order,
                immediate: $immediate,
                scheduledAt: $scheduledAt
            );

            DB::commit();
            return $pointRecord;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 发放生日积分奖励
     */
    public function processBirthdayBonus(User $user): ?UserPointChangeRecord
    {
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);

        if (!$userLevel) {
            return null;
        }

        $benefits = $userLevel->membershipLevel->benefits ?? [];
        $birthdayBonus = $benefits['birthday_bonus'] ?? null;

        if (!$birthdayBonus) {
            return null;
        }

        // 解析生日奖励（例如："生日当月额外10%积分"）
        // 这里简化处理，给予固定积分
        $bonusPoints = 100; // 可以根据会员等级调整

        try {
            // 使用新的积分服务
            $pointService = new UserPointService();
            $pointRecord = $pointService->addPoints(
                user: $user,
                points: $bonusPoints,
                changeType: PointChangeTypeEnum::BirthdayReward,
                description: "生日奖励积分 - {$userLevel->membershipLevel->name}",
                source: $userLevel,
                immediate: true
            );

            return $pointRecord;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取用户会员信息
     */
    public function getUserMembershipInfo(User $user): array
    {
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);
        $totalSpend = $this->calculateUserTotalSpend($user);

        // 使用新的积分服务获取积分统计
        $pointService = new UserPointService();
        $pointStats = $pointService->getUserPointStats($user);

        if (!$userLevel) {
            // 用户还没有会员等级，获取最低等级作为默认等级
            $defaultLevel = MembershipLevel::getLowestLevel();
            $userLevel = $this->checkAndUpgradeUserLevel($user);
            return [
                'current_level' => $userLevel ?? null,
                'total_spend_amount' => $totalSpend,
                'next_level' => $defaultLevel ? $defaultLevel->getNextLevel() : null,
                'upgrade_required_amount' => $defaultLevel ? $defaultLevel->getUpgradeRequiredAmount($totalSpend) : null,
                'point_stats' => $pointStats,
            ];
        }

        $currentLevel = $userLevel->membershipLevel;
        $nextLevel = $currentLevel->getNextLevel();

        return [
            'current_level' => $userLevel,
            'total_spend_amount' => $totalSpend,
            'next_level' => $nextLevel,
            'upgrade_required_amount' => $currentLevel->getUpgradeRequiredAmount($totalSpend),
            'point_stats' => $pointStats,
            'benefits' => $currentLevel->getBenefitsDescription(),
        ];
    }

    /**
     * 获取所有会员等级列表
     */
    public function getAllMembershipLevels(): \Illuminate\Database\Eloquent\Collection
    {
        return MembershipLevel::getAllLevels();
    }

    /**
     * 处理订单退款时的积分扣除
     */
    public function processOrderRefund(Order $order): void
    {
        // 查找该订单相关的积分记录
        $pointRecords = UserPointChangeRecord::where('source_type', Order::class)
            ->where('source_id', $order->id)
            ->where('change_amount', '>', 0) // 只查找积分增加的记录
            ->where('status', 'completed')
            ->get();

        if ($pointRecords->isEmpty()) {
            return;
        }

        $user = $order->user;
        if (!$user) {
            return;
        }

        DB::beginTransaction();
        try {
            $pointService = new UserPointService();

            foreach ($pointRecords as $record) {
                // 扣除之前奖励的积分
                $pointService->deductPoints(
                    user: $user,
                    points: $record->change_amount,
                    changeType: PointChangeTypeEnum::OrderCancel,
                    description: "订单退款扣除积分（原订单：{$order->order_no}）",
                    source: $order
                );
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 处理用户生日积分
     */
    public function processBirthdayPoints(int $userId): ?UserPointChangeRecord
    {
        $user = User::find($userId);
        if (!$user || !$user->birthday) {
            return null;
        }

        // 检查今天是否是用户生日
        $today = now();
        $birthday = Carbon::parse($user->birthday);

        if ($today->month !== $birthday->month || $today->day !== $birthday->day) {
            return null; // 不是生日
        }

        // 获取用户当前会员等级
        $userMembership = UserMembershipLevel::where('user_id', $userId)->first();
        if (!$userMembership) {
            return null;
        }

        $membershipLevel = $userMembership->membershipLevel;
        if (!$membershipLevel || !$membershipLevel->isBirthdayPointsEnabled()) {
            return null; // 等级不支持生日积分
        }

        // 检查今年是否已经发放过生日积分
        $currentYear = $today->year;
        $existingRecord = UserPointChangeRecord::where('user_id', $userId)
            ->where('change_type', PointChangeTypeEnum::BirthdayReward->value)
            ->whereYear('created_at', $currentYear)
            ->first();

        if ($existingRecord) {
            return null; // 今年已经发放过
        }

        // 创建生日积分记录
        $birthdayPoints = $membershipLevel->getBirthdayPointsAmount();

        try {
            $pointService = new UserPointService();
            $pointRecord = $pointService->addPoints(
                user: $user,
                points: $birthdayPoints,
                changeType: PointChangeTypeEnum::BirthdayReward,
                description: "生日赠送积分 - {$membershipLevel->name}",
                source: $userMembership,
                immediate: true
            );

            return $pointRecord;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取积分延迟发放天数
     */
    public function getPointDelayDays(): int
    {
        $rule = SharingRule::where('is_global', true)
            ->where('is_publish', true)
            ->first();

        if (!$rule || !$rule->condition) {
            return 45; // 默认45天
        }

        $condition = $rule->condition;
        if (!isset($condition['enabled']) || !$condition['enabled']) {
            return 0; // 立即发放
        }

        return (int) ($condition['days'] ?? 45);
    }
}
