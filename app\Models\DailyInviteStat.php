<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class DailyInviteStat extends MiddleModel
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = [];

    public static function booted()
    {
        static::creating(function (self $model) {
            if ($model->total_fans_count) {
                $model->share_count = 1;
            }
        });
    }

    // 补全日期内没有数据的日期
    public static function fillEmptyDate($data, $start_at, $end_at)
    {
        // 获取日期列表
        $startDate = Carbon::parse($start_at);
        $endDate = Carbon::parse($end_at);
        // 生成日期范围
        $dates = [];
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
        }
        // 创建最终结果
        $result = [];
        foreach ($dates as $date) {
            $result[$date] = $data[$date] ?? [
                'date' => $date,
                'share_count' => 0,
                'total_fans_count' => 0,
                'estimated_count' => 0,
                'estimated_amount' => 0
            ];
        }
        // 按日期排序
        ksort($result);
        $daily = [];
        foreach ($result as $item) {
            // 每日数据
            $daily['date'][] = Arr::get($item, 'date');
            $daily['share_count'][] = Arr::get($item, 'share_count');
            $daily['total_fans_count'][] = Arr::get($item, 'total_fans_count');
            $daily['estimated_count'][] = Arr::get($item, 'estimated_count');
            $daily['estimated_amount'][] = Arr::get($item, 'estimated_amount');
        }
        return $daily;
    }
}
