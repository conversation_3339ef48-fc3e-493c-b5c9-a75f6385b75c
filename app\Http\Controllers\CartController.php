<?php

namespace App\Http\Controllers;


use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Shipping;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use App\Models\User\User;
use App\Models\Enums\CartTypeEnum;
use Throwable;


class CartController extends Controller
{
    /**
     * 获取购物车中需要保留的非全局自动使用优惠券
     * @param Cart $cart
     * @return array [preservedCoupon, preservedUserCoupon]
     */
    private function getPreservedCoupon(Cart $cart): array
    {
        $preservedCoupon = null;
        $preservedUserCoupon = null;

        // 查找购物车中绑定的优惠券（排除全局自动使用的优惠券）
        $couponItem = $cart->items()->whereNotNull('coupon_id')->with(['coupon', 'userCoupon'])->first();
        if ($couponItem && $couponItem->coupon) {
            $coupon = $couponItem->coupon;
            // 如果不是全局自动使用的优惠券，则保留
            if (!($coupon->is_global && $coupon->is_auto)) {
                $preservedCoupon = $coupon;
                $preservedUserCoupon = $couponItem->userCoupon;
            }
        }

        return [$preservedCoupon, $preservedUserCoupon];
    }

    /**
     * 删除购物车中没有库存和下架的产品
     * @param Cart $cart
     * @return bool 是否有商品被删除
     */
    private function removeInvalidCartItems(Cart $cart): bool
    {
        $itemsToDelete = [];

        foreach ($cart->items as $item) {
            $shouldDelete = false;

            // 检查商品变体是否下架
            if (!$item->productVariant->is_publish) {
                $shouldDelete = true;
            }

            // 检查商品是否下架
            if (!$item->product->is_publish) {
                $shouldDelete = true;
            }

            // 检查库存是否不足
            if ($item->productVariant->stock < $item->num) {
                $shouldDelete = true;
            }

            if ($shouldDelete) {
                $itemsToDelete[] = $item->id;
            }
        }

        // 批量删除无效商品
        if (!empty($itemsToDelete)) {
            $cart->items()->whereIn('id', $itemsToDelete)->delete();
            return true;
        }

        return false;
    }
    public function show(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'country' => 'nullable|string',
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
            'auto_clean' => 'nullable|boolean', // 是否自动清理无效商品
        ]);
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /**
         * @var Cart $cart
         */
        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        $cart->load([
            'items',
            'items.product:id,spu,title,slug_title,is_publish',
            'items.product.commentStatic',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id,is_publish',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);

        // 如果传递了auto_clean参数且为true，则删除没有库存和下架的产品
        if (Arr::get($validated, 'auto_clean', false)) {
            $hasDeletedItems = $this->removeInvalidCartItems($cart);

            // 如果有商品被删除，重新加载购物车数据
            if ($hasDeletedItems) {
                $cart->load([
                    'items',
                    'items.product:id,spu,title,slug_title,is_publish',
                    'items.product.commentStatic',
                    'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id,is_publish',
                    'items.productVariant.image:id,path,disk,module',
                    'items.productVariant.sizeAttribute:id,value',
                    'items.productVariant.colorAttribute:id,value',
                    'items.coupon',
                ]);
            }
            [$preservedCoupon, $preservedUserCoupon] = $this->getPreservedCoupon($cart);
            // 更新总订单数据，传递保留的优惠券
            $cart = cartService()->refreshCart($cart, $preservedCoupon, true, null, $preservedUserCoupon);
        
        }

        // 刷新购物车数据
        if (cartService()->refreshCartInfo($cart)) {
            cartService()->refreshCart($cart);
        }

        // 获取第一个绑定优惠券的商品的优惠券信息
        $firstCouponItem = $cart->items->whereNotNull('coupon_id')->first();
        if ($firstCouponItem && $firstCouponItem->coupon) {
            $cart->coupon = $firstCouponItem->coupon->only(['id', 'code', 'is_auto', 'is_global', 'name']);
        }

        $shippings = cartService()->getShippingFee($cart);
        $cart->shippings = $shippings;
        // 运费实际金额
        return JsonResource::make($cart);
    }

    /**
     * 删除购物车产品
     * @param Request $request
     * @param CartItem $item
     * @return JsonResource
     * @throws DataException
     */
    public function itemDestroy(Request $request, CartItem $item): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'uuid',
            'country' => 'required|string',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        // 非本人 或非此购物车的数据
        if (!$cart->items()->where('id', $item->id)->exists()) {
            throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
        }

        // 在删除商品前，检查购物车中是否有非全局自动使用的优惠券
        [$preservedCoupon, $preservedUserCoupon] = $this->getPreservedCoupon($cart);

        $item->delete();

        // 更新总订单数据，传递保留的优惠券
        $cart = cartService()->refreshCart($cart, $preservedCoupon, true, $validated['country'], $preservedUserCoupon);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }


    /**
     * 修改购物车商品数量
     * @param Request $request
     * @param CartItem $item
     * @return JsonResource
     * @throws DataException
     */
    public function itemUpdate(Request $request, CartItem $item): JsonResource
    {
        $validated = $request->validate([
            'num' => 'numeric|min:1',
            'session_uuid' => 'uuid',
            'country' => 'required|string',
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        //重新下单和详情页支付单个商品时，传临时购物车id
        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        // 非本人 或非此购物车的数据
        if (!$cart->items()->where('id', $item->id)->exists()) {
            throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
        }
        // 更新购物车数量
        $cart = cartService()->updateCartItem($cart, $item, $validated['num']);
        // 刷新购物车数据
        $cart = cartService()->refreshCart($cart, null, true, $validated['country']);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 合并购物车
     * @param Request $request
     * @return JsonResource
     * @throws Throwable
     */
    public function mergeCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => ['required', 'uuid', new Exists(Cart::class, 'session_uuid')],
            'country' => 'required|string',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        /** @var Cart $userCart  */
        $userCart = userService()->getOrCreateCart($user);
        $country = Arr::get($validated, 'country');
        $sessionCart = userService()->getOrCreateCart(null, Arr::get($validated, 'session_uuid'));
        DB::beginTransaction();
        try {
            $items = $sessionCart->items()->with('productVariant')->get();
            // 获取用户的购物车特价商品
            $userCartItems = $userCart->items()->where('is_activity', 1)->get();
            // 获取session购物车中的特价商品
            $sessionCartItems = $sessionCart->items()->where('is_activity', 1)->get();
            // 如果都有保留最早添加到购物车的特价产品另外的还原价格
            if ($userCartItems->isNotEmpty() && $sessionCartItems->isNotEmpty()) {
                $allActivityItems = $userCartItems->merge($sessionCartItems);
                // 找到创建时间最早的那个
                $earliestCartItem = $allActivityItems->sortBy('created_at')->first();
                $preservedCartItemId = $earliestCartItem->id;
            }
            foreach ($items as $item) {
                $variant = $item->productVariant;
                $oldItem = $userCart->items()->where('product_variant_id', $item['product_variant_id'])->first();
                if ($oldItem) {
                    // 如果 oldItem 是 preservedCartItemId，跳过，新增一个新的原价
                    if (!empty($preservedCartItemId) && $oldItem->id === $preservedCartItemId) {
                        // 直接新增商品
                        $newItem = $userCart->items()->create([
                            ...$item->only(['product_id', 'product_variant_id']),
                            'add_price' => $variant->price, // 设置add_price
                            'price' => $variant->price,
                            'original_price' => $variant->original_price,
                            'weight' => $variant->weight,
                        ]);
                        continue;
                    }

                    $updateData = [
                        'num' => DB::raw('num + ' . $item->num),
                        'price' => $variant->price,
                        'original_price' => $variant->original_price,
                    ];

                    // 如果 oldItem 是特价商品，但不是 preservedCartItemId，则重置活动相关字段
                    if ($oldItem->is_activity) {
                        $updateData['user_experience_activity_id'] = null;
                        $updateData['is_activity'] = 0;
                    }

                    $oldItem->update($updateData);
                } else {
                    // 直接新增商品
                    $newItem = $userCart->items()->create([
                        ...$item->only(['product_id', 'product_variant_id', 'user_experience_activity_id', 'is_activity', 'price']),
                        // 'price' => $variant->price,
                        'add_price' => $item->price ?: $variant->price, // 设置add_price
                        'original_price' => $variant->original_price,
                        'weight' => $variant->weight,
                    ]);

                    // 如果新加入的是特价商品，且不是 preservedCartItemId，则重置活动相关字段
                    if ($newItem->is_activity && !empty($preservedCartItemId) && $newItem->id !== $preservedCartItemId) {
                        $newItem->update([
                            'user_experience_activity_id' => null,
                            'is_activity' => 0,
                            'price' => $variant->price,
                        ]);
                    }
                }
            }
            // 删除历史item数据
            $sessionCart->delete();
            $sessionCart->items()->delete();

            // 刷新购物车数据
            $userCart = cartService()->refreshCart($userCart, null, true, $country);
            $userCart->load([
                'items',
                'items.product:id,spu,title',
                'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
                'items.productVariant.image:id,path,disk,module',
                'items.productVariant.sizeAttribute:id,value',
                'items.productVariant.colorAttribute:id,value',
                'items.coupon',
            ]);
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
        }

        return JsonResource::make($userCart);
    }

    /**
     * 更新购物车运输方式
     * @param Request $request
     * @return JsonResource
     */
    public function updateCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'shipping_id' => ['int', new Exists(Shipping::class, 'id')],
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        // 更新购物车运费
        $shipping = Shipping::query()->find(Arr::get($validated, 'shipping_id'));
        $cart->update([
            'shipping_id' => $shipping->id,
            'shipping_type' => $shipping->type,
        ]);
        // 刷新购物车
        $cart = cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 添加购物车
     * @param Request $request
     * @return JsonResource
     */
    public function addCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'items' => 'array|required',
            'items.*.product_id' => ['required', 'int', 'exists:products,id'],
            'items.*.product_variant_id' => ['required', 'int', 'exists:product_variants,id'],
            'items.*.num' => 'numeric|required|min:1',
            'from_activity' => 'nullable|bool',
            'country' => 'required|string',
            'type' => 'nullable|numeric',
        ]);
        //type默认1正常
        $type = Arr::get($validated, 'type', CartTypeEnum::Normal->value);

        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        //type=2创建临时购物车
        if ($type == CartTypeEnum::Temp->value) {
            $cart = userService()->getOrCreateCart($user, $session_uuid, true);
        } else {
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        //是否活动页
        $from_activity = Arr::get($validated, 'from_activity', false);
        // 获取购物车内容
        $items = Arr::get($validated, 'items', []);
        // 添加购物车
        foreach ($items as $item) {
            cartService()->addCartItem($cart, $item, $user, $session_uuid, $from_activity);
        }
        // 刷新购物车
        $cart = cartService()->refreshCart($cart, null, true, $validated['country']);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }


    /**
     * 绑定优惠券 --- 现确认以后都没有运费优惠券 --- 确认人：许家旺 2025/05/23
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function selectCoupon(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'code' => 'required|string',
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
            'user_coupon_id' => ['nullable', 'int', 'exists:user_coupons,id'],
        ]);
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /**
         * @var Cart $cart
         */
        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        $code = Arr::get($validated, 'code');
        $userCouponId = Arr::get($validated, 'user_coupon_id');
        $coupon = null;
        $selectedUserCoupon = null;

        // 客户只能使用非自动使用的优惠券
        $coupon = Coupon::query()->where('code', $code)->where('is_auto', 0)->first();
        if (!$coupon) {
            throw new DataException("Coupon failed.");
        }

        // 判断优惠券使用类型， 两者都需要判断该用户是否还有可用库存
        if ($coupon->is_global) {
            // 全局优惠券
            if ($user) {
                // 检查用户使用次数是否超出限制
                if($coupon->user_count > 0){
                    $usedCount = $user->userCoupons()->where('code', $code)->whereNotNull('used_at')->count();
                    if($usedCount >= $coupon->user_count){
                        throw new DataException('This coupon has already been used.');
                    }
                }
            }
            // 判断优惠券是否过期
            if (now()->lt($coupon->effective_start_at)) {
                throw new DataException('This coupon is not yet available.');
            }
            if (now()->gt($coupon->effective_end_at)) {
                throw new DataException('This coupon has expired.');
            };
        } else {
            // 个人优惠券
            if (!$user) {
                throw new DataException("Please login to use personal coupons.");
            }

            // 如果指定了 user_coupon_id，则使用指定的优惠券
            if ($userCouponId) {
                $selectedUserCoupon = $user->userCoupons()
                    ->where('id', $userCouponId)
                    ->where('code', $code)
                    ->whereNull('used_at')
                    ->first();
                if (!$selectedUserCoupon) {
                    throw new DataException("Specified coupon not found or has been used.");
                }
            } else {
                $selectedUserCoupon = $user->userCoupons()->where('code', $code)->whereNull('used_at')->first();
                if (!$selectedUserCoupon) {
                    throw new DataException("Coupon failed.");
                }
            }

            if (now()->lt($selectedUserCoupon->effective_start_at)) {
                throw new DataException('This coupon has expired.');
            };
            if (now()->gt($selectedUserCoupon->effective_end_at)) {
                throw new DataException('This coupon has expired.');
            };
        }

        try {
            DB::beginTransaction();
            // 绑定新的优惠券，传递指定的用户优惠券
            $cart = cartService()->refreshCart($cart, $coupon, false, null, $selectedUserCoupon);
            DB::commit();
        } catch (DataException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $exception) {
            DB::rollBack();
            throw new DataException('Coupon Use Error.');
        }

        $cart->refresh();
        // 返回购物车信息
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        // 折扣率
        return JsonResource::make($cart);
    }

    /**
     * 获取运费优惠
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function shippingCoupon(Request $request)
    {
        // 获取所有满足条件的优惠券
        $coupon_rules = Coupon::query()
            ->where('type', DiscountTypeEnum::FreeShippingPromotion->value)
            ->where('is_global', true)
            ->where('enabled', true)
            ->where('effective_start_at', '<=', now())
            ->where('effective_end_at', '>=', now())
            ->whereNull('total_count')
            ->whereNull('user_count')
            ->pluck('rules');
        // 规则
        $rules = [];
        if ($coupon_rules->isNotEmpty()) {
            foreach ($coupon_rules as $coupon_rule) {
                foreach ($coupon_rule as $rule) {
                    $rules[Arr::get($rule, 'shipping_id')] = [
                        'price' => Arr::get($rule, 'price'),
                        'price_agent' => convertPrice(Arr::get($rule, 'price'), currentCurrency()),
                        'shipping' => Shipping::query()->where('id', Arr::get($rule, 'shipping_id'))->first(),
                    ];
                }
            }
        }
        return JsonResource::make($rules);
    }

    /**
     * 更新购物车
     * @param Request $request
     * @return JsonResource
     */
    public function removeCoupon(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }
        // 刷新购物车
        cartService()->refreshCart($cart);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 切换收货地址
     * @param Request $request
     * @return JsonResource
     */
    public function switchCountry(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'country' => ['required', 'string'],
            'from' => ['nullable', 'string'],
            'cart_id' => ['nullable', 'int', 'exists:carts,id'],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');

        if(Arr::get($validated, 'cart_id')){
            $cart = Cart::find(Arr::get($validated, 'cart_id'));
        }else{
            $cart = userService()->getOrCreateCart($user, $session_uuid);
        }

        $country = Arr::get($validated, 'country');

        // 检查购物车中是否有非全局自动使用的优惠券
        [$preservedCoupon, $preservedUserCoupon] = $this->getPreservedCoupon($cart);

        $from = Arr::get($validated, 'from', 'checkout');
        //只要结算页切换地址才更新购物车绑定国家，其他页面切换国家只计算运费不改变购物车绑定国家
        if($from == 'checkout'){
            $cart->update([
                'country' => $country,
                'country_updated_at' => now(),
            ]);
            $cart->refresh();
        }

        $cart = cartService()->refreshCart($cart, $preservedCoupon, true, $country, $preservedUserCoupon);

        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);

        return JsonResource::make($cart);
    }
}
