<?php

namespace App\Http\Controllers;


use App\Constants\CacheKey;
use App\Models\Coupon\Coupon;
use App\Models\Enums\AttachmentModuleEnum;
use App\Models\Enums\AttributesEnum;
use App\Models\Enums\Broadcast\BroadcastColumnContentTypeEnum;
use App\Models\Enums\Broadcast\BroadcastColumnPositionsEnum;
use App\Models\Enums\CollectionJumpTypeEnum;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Enums\CurrencyEnum;
use App\Models\Enums\Discount\DiscountAmountTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\EmailRuleTypeEnum;
use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Enums\Order\Payment\PaymentMethodTypeEnum;
use App\Models\Enums\Promotion\PromotionAlignmentMethodEnum;
use App\Models\Enums\Promotion\PromotionStatusEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Enums\SysConfigKeyEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\Order\Payment\PaymentMethod;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use ReflectionClass;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Http\Request;

class CommonController extends Controller
{
    //
    protected array $dictEnums = [
        UserEmailPreferencesEnum::class,
        UserStatusEnum::class,
        UserRegisterTypesEnum::class,
        WalletChangeTypeEnum::class,
        CollectionJumpTypeEnum::class,
        BroadcastColumnPositionsEnum::class,
        BroadcastColumnContentTypeEnum::class,
        CommentStatusEnum::class,
        DiscountAmountTypeEnum::class,
        DiscountPriceTypeEnum::class,
        DiscountTypeEnum::class,
        OrderAddressTypeEnum::class,
        OrderPaidStatusEnum::class,
        OrderPaidTypeEnum::class,
        PromotionAlignmentMethodEnum::class,
        OrderStatusEnum::class,
        EquipmentTypeEnum::class,
        PromotionStatusEnum::class,
        PromotionTypeEnum::class,
        AttachmentModuleEnum::class,
        AttributesEnum::class,
        CurrencyEnum::class,
        EmailRuleEventEnum::class,
        EmailRuleTypeEnum::class,
        SysConfigKeyEnum::class,
    ];

    public function dictionaries(): JsonResource
    {
        $res = Cache::remember(CacheKey::DictEnums->getKey(), now()->addDay(), function () {
            foreach ($this->dictEnums as $key => $model) {
                $list = [];
                foreach ($model::cases() as $value) {
                    $list[] = ['key' => $value->value, 'label' => method_exists($value, 'desc') ? $value->desc() : $value->name];
                }
                $res[] = [
                    'tag' => Str::before(Str::snake((new ReflectionClass($model))->getShortName()), '_enum'),
                    'options' => $list
                ];
            }
            return $res;
        });
        return JsonResource::make($res);
    }


    /**
     * 优惠券全局的
     * @return AnonymousResourceCollection
     */
    public function coupons(): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Coupon::class)
            ->where('is_global', true)
            ->allowedFilters([
                AllowedFilter::exact('enabled')->default(true),
            ]);
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 二维码扫码
     * @return JsonResource
     */
    public function qrCode(Request $request): JsonResource
    {
        $validated = $request->validate([
            'q' => ['required', 'string'],
        ]);
        $q = Arr::get($validated, 'q');
        $maps = sysConfigService()->get(SysConfigKeyEnum::QRCodeRedirectLinkConfig);
        $links = collect(Arr::get($maps, 'links'));
        $redirect_url = $links->where('url', $q)->value('redirect_url');
        return JsonResource::make(['redirect_url' => $redirect_url]);
    }

    // paypal支付配置
    public function paypalConfig(Request $request)
    {
        $config = PaymentMethod::query()->where('type', PaymentMethodTypeEnum::PayPal->value)->first()?->config;
        if (Arr::get($config, 'sandbox', false)) {
            $client_id = Arr::get($config, 'sandbox_client_id', '');
        } else {
            $client_id = Arr::get($config, 'client_id', '');
        }
        return JsonResource::make(['client_id' => $client_id]);
    }
}
