<?php

namespace App\Jobs\Order;

use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderIsFirstOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 重试间隔时间（秒）
     */
    public int $backoff = 60;
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = $this->order;
            // 如果订单状态有效，则判断是否是第一次下单
            if ($order->status->valid()) {
                $is_first_order = !Order::query()
                    ->where('id', '!=', $order->id)
                    ->where('user_id', $order->user_id)
                    ->where('valid', true)
                    ->exists();
                if ($is_first_order) {
                    $order->is_first_order = $is_first_order;
                    $order->save();
                }
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
