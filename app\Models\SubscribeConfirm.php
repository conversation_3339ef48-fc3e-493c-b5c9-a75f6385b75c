<?php

namespace App\Models;

use App\Constants\QueueKey;
use App\Jobs\EmailRuleNotice;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubscribeConfirm extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    public static function booted()
    {
        static::created(function (self $subscribeConfirm) {
            EmailRuleNotice::dispatch(EmailRuleEventEnum::SubscribeConfirm,
                user: $subscribeConfirm->user,
                data: $subscribeConfirm->only(['token'])
            )->onQueue(QueueKey::Default->value);
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
