<?php

namespace App\Admin\Requests;

use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\Promotion\PromotionAlignmentMethodEnum;
use App\Models\Enums\Promotion\PromotionPositionEnum;
use App\Models\Enums\Promotion\PromotionStatusEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Enums\Reward\RewardFansTypeEnum;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\Enums\Reward\RewardTypeEnum;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;

class PromotionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 根据需要进行授权检查
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => ['required', 'integer', new Enum(PromotionTypeEnum::class)],
            'title' => ['required_unless:type,' . PromotionTypeEnum::SearchNavType->value, 'nullable', 'string', 'max:32'],
            'description' => ['required_if:type,' . PromotionTypeEnum::BoxSetType->value . ',' . PromotionTypeEnum::DesignType->value, 'nullable', 'string', 'max:128'],
            'button_text' => ['required_if:type,' . PromotionTypeEnum::BoxSetType->value . ',' . PromotionTypeEnum::DesignType->value, 'nullable', 'string', 'max:32'],
            'button_link' => ['required_if:type,' . PromotionTypeEnum::BoxSetType->value . ',' . PromotionTypeEnum::DesignType->value, 'nullable', 'string', 'max:255'],
            'button_background_color' => ['required_if:type,' . PromotionTypeEnum::BoxSetType->value . ', ' . PromotionTypeEnum::DesignType->value,'nullable', 'string', 'max:32'],
            'position' => ['required', 'string', new Enum(PromotionPositionEnum::class)],
            // 'alignment_method' => ['nullable', 'integer', new Enum(PromotionAlignmentMethodEnum::class)],
            'equipment_type' => ['required', 'integer', new Enum(EquipmentTypeEnum::class)],
            'sort' => ['nullable', 'integer'],
            'contents' => ['required_unless:type,' . PromotionTypeEnum::ProductType->value, 'nullable', 'array'],
            'product_contents' => ['required_if:type,' . PromotionTypeEnum::ProductType->value, 'nullable', 'array'],
            'status' => ['required', 'boolean'],
        ];
    }

    /**
     * Validate reward type and its rules.
     *
     * @param RewardFansTypeEnum $fansType
     * @return void
     * @throws ValidationException
     */
    protected function validateRewardType(RewardFansTypeEnum $fansType)
    {
        $rewardTypeValue = Arr::get($this->validated(), 'reward_type');
        $rewardType = RewardTypeEnum::tryFrom($rewardTypeValue);

        $validationRules = $rewardType->validateRules($fansType);
        $validator = Validator::make($this->all(), $validationRules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Handle a passed validation attempt.
     *
     * @return void
     */
    protected function passedValidation()
    {
        $typeValue = Arr::get($this->validated(), 'type');
        $type = PromotionTypeEnum::tryFrom($typeValue);

        $validationRules = $type->rules();
        $validator = Validator::make($this->all(), $validationRules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }
}
