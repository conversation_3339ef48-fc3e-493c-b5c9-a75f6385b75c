<?php

use App\Models\Enums\CouponGrantType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_grant_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('规则名称');
            $table->bigInteger('coupon_id')->comment('发放的优惠券id');
            $table->tinyInteger('type')->default(CouponGrantType::FirstOrder)->comment('规则类型 enum:CouponGrantType');
            $table->json('config')->comment('优惠券相关配置');
            $table->dateTime('effective_start_at')->nullable()->comment('有效期（固定开始时间）');
            $table->dateTime('effective_end_at')->nullable()->comment('有效期（固定过期时间）');
            $table->boolean('send_email')->default(false)->comment('发送邮件');
            $table->boolean('email_template_id')->nullable()->comment('邮件模板id');
            $table->integer('total_count')->nullable()->comment('总发送数量');
            $table->integer('grant_count')->default(0)->comment('已发送数量');
            $table->boolean('enabled')->default(false)->comment('启用状态');
            $table->string('desc')->comment('描述');
            $table->string('url')->nullable()->default(NULL)->comment('动效跳转链接');

            $table->engine('InnoDB');
            $table->comment('优惠券发放规则');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_grant_rules');
    }
};
