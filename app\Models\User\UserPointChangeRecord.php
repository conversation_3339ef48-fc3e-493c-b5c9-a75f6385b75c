<?php

namespace App\Models\User;

use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * 用户积分变化记录
 * 
 * @property int $id
 * @property int $user_id
 * @property float $change_amount 积分变化数量（正数为增加，负数为减少）
 * @property float $balance_before 变化前积分余额
 * @property float $balance_after 变化后积分余额
 * @property string $change_type 变化类型
 * @property string $description 变化描述
 * @property string $source_type 来源模型类型
 * @property int $source_id 来源模型ID
 * @property string $status 状态：pending/completed/failed
 * @property \Carbon\Carbon $scheduled_at 计划执行时间
 * @property \Carbon\Carbon $completed_at 完成时间
 * @property string $coupon_code 兑换的优惠券码
 * @property string $coupon_name 兑换的优惠券名称
 */
class UserPointChangeRecord extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    /**
     * 指示模型是否应该被打上时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    protected $casts = [
        'change_amount' => 'float',
        'balance_before' => 'float',
        'balance_after' => 'float',
        'scheduled_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 所属用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 来源模型（多态关联）
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按变化类型筛选
     */
    public function scopeByChangeType(Builder $query, string $changeType): Builder
    {
        return $query->where('change_type', $changeType);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：积分增加记录
     */
    public function scopeIncrease(Builder $query): Builder
    {
        return $query->where('change_amount', '>', 0);
    }

    /**
     * 作用域：积分减少记录
     */
    public function scopeDecrease(Builder $query): Builder
    {
        return $query->where('change_amount', '<', 0);
    }

    /**
     * 作用域：待发放记录
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * 作用域：已完成记录
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * 创建积分变化记录
     */
    public static function createRecord(
        int $userId,
        float $changeAmount,
        float $balanceBefore,
        float $balanceAfter,
        string $changeType,
        string $description,
        $source = null,
        string $status = 'completed',
        ?\Carbon\Carbon $scheduledAt = null,
        ?string $couponCode = null,
        ?string $couponName = null
    ): self {
        $record = self::create([
            'user_id' => $userId,
            'change_amount' => $changeAmount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'change_type' => $changeType,
            'description' => $description,
            'status' => $status,
            'scheduled_at' => $scheduledAt,
            'completed_at' => $status === 'completed' ? now() : null,
            'coupon_code' => $couponCode,
            'coupon_name' => $couponName,
        ]);

        if ($source) {
            $record->source()->associate($source);
            $record->save();
        }

        return $record;
    }

    /**
     * 标记为已完成
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
        ]);
    }

    /**
     * 是否为积分增加
     */
    public function isIncrease(): bool
    {
        return $this->change_amount > 0;
    }

    /**
     * 是否为积分减少
     */
    public function isDecrease(): bool
    {
        return $this->change_amount < 0;
    }

    /**
     * 是否为待发放状态
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * 是否为已完成状态
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }
}
