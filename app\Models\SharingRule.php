<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class SharingRule extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'effective_start_at' => 'datetime',
        'effective_end_at' => 'datetime',
        'is_global' => 'boolean',
        'is_publish' => 'boolean',
        'rules' => 'json',
        'condition' => 'json',
        'email_setting' => 'json',
    ];

    // 更新人
    public function updateUser()
    {
        return $this->belongsTo(AdminUser::class, 'update_user_id', 'id');
    }
}
