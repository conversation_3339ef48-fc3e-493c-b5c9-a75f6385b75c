<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_users', function (Blueprint $table) {
            $table->id();
            $table->string('email', 128)->comment('邮箱')->index()->nullable();
            $table->string('avatar')->nullable();
            $table->string('google_id')->nullable();
            $table->string('name')->nullable();
            $table->string('given_name')->nullable();
            $table->string('family_name')->nullable();
            $table->boolean('email_verified')->default(false);
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('谷歌用户');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_users');
    }
};
