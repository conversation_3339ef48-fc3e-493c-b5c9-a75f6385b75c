<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderStatusEnum: int
{
    case  Unpaid = 0;
    case  Paid = 1;
    case  Shipped = 2;
    case  Received = 3;
    case  Completed = 4;
    case  Refund = 5;
    case  Cancel = 6;
    case  PaidFail = 7;

    public function desc(): string
    {
//        return match ($this) {
//            self::StatusEnable => "开启",
//            self::StatusDisabled => "禁用",
//        };
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Unpaid->value => '未付款',
            self::Paid->value => '已付款',
            self::Shipped->value => '已发货',
            self::Received->value => '已签收',
            self::Completed->value => '已完成',
            self::Refund->value => '已退款',
            self::Cancel->value => '已取消',
            self::PaidFail->value => '支付失败',
        ];
    }

    public function en_desc(): string
    {
        return Arr::get(self::en_list(), $this->value);
    }


    public static function en_list(): array
    {
        return [
            self::Unpaid->value => 'unpaid',
            self::Paid->value => 'paid',
            self::Shipped->value => 'shipped',
            self::Received->value => 'received',
            self::Completed->value => 'completed',
            self::Refund->value => 'refund',
            self::Cancel->value => 'cancel',
            self::PaidFail->value => 'paid fail',
        ];
    }

    /**
     * 是否有效
     * @return bool
     */
    public function valid()
    {
        return match ($this) {
            self::Unpaid, self::Cancel, self::PaidFail => false,
            default => true,
        };
    }

}
