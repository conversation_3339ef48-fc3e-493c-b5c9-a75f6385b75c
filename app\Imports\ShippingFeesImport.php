<?php

namespace App\Imports;

use App\Models\ShippingFee;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;
use Illuminate\Support\Facades\DB;
use App\Models\Shipping;

class ShippingFeesImport implements ToArray, WithHeadingRow, WithEvents, WithValidation, SkipsEmptyRows, SkipsOnFailure
{
    use RegistersEventListeners;

    public static function beforeImport(BeforeImport $event): void
    {
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            return Arr::get(static::$header, $value, $value);
        });
        HeadingRowFormatter::default('slug');
    }

    public static function afterImport(AfterImport $event): void
    {
        HeadingRowFormatter::reset();
    }

    public static array $header = [
        'country' => 'country',
        'weight_from' => 'min_weight',
        'weight_to' => 'max_weight',
        'shipping_fee' => 'shipping_fee',
        'delivery_type' => 'delivery_type',
    ];

    public function rules(): array
    {
        return [
            'country' => ['required', 'string', 'max:20'],
            'min_weight' => ['required', 'numeric', 'min:0'],
            'max_weight' => ['required', 'numeric', 'min:0'],
            'shipping_fee' => ['required', 'numeric', 'min:0'],
            'delivery_type' => ['required', 'exists:shipping,id'],
        ];
    }

    public function array(array $array): array
    {
        $errors = [];
        try {
            DB::beginTransaction();
            //清空原来数据
            ShippingFee::where('id', '>', 0)->delete();
            $insertData = [];
            foreach ($array as $key => $item) {
                $line = $key + 2;
                // 校验必填字段
                if (!Arr::get($item, 'country') || !Arr::get($item, 'min_weight') || !Arr::get($item, 'max_weight') || !Arr::get($item, 'shipping_fee') || !Arr::get($item, 'delivery_type')) {
                    $errors[] = "第{$line}行数据缺失，country、min_weight、max_weight、shipping_fee、delivery_type为必填";
                    continue;
                }
                $insertData[] = [
                    'country' => Arr::get($item, 'country'),
                    'min_weight' => Arr::get($item, 'min_weight'),
                    'max_weight' => Arr::get($item, 'max_weight'),
                    'shipping_fee' => Arr::get($item, 'shipping_fee'),
                    'shipping_id' => Arr::get($item, 'delivery_type'),
                ];
            }
            ShippingFee::insert($insertData);
            //更新shipping表的updated_at
            Shipping::where('id', '>', 0)->update(['updated_at' => now()]);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
        }
        return $errors;
    }

    public function onFailure(Failure ...$failures)
    {
        // 可扩展失败处理逻辑
    }
}
