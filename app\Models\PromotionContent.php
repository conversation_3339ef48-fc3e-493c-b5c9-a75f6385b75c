<?php

namespace App\Models;

use App\Constants\CacheKey;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;


class PromotionContent extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected static function booted()
    {
        parent::booted();
        static::creating(function ($content) {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
        static::saved(function ($content) {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
    }

    // 广告位
    public function promotion()
    {
        return $this->belongsTo(Promotion::class);
    }

    // 集合
    public function collection()
    {
        return $this->belongsTo(Collection::class);
    }

    // 文件：图片/视频: Attachment
    public function attachment()
    {
        return $this->belongsTo(Attachment::class);
    }
}
