<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google API OAuth</title>
</head>
<script >
    function test(){
        console.log(document.getElementById('g_id_onload'))
       console.log( document.querySelector("body > div.g_id_signin > div > div > div").click())
       res =  document.getElementById('g_id_onload').click();
        console.log(res)
    }
</script>
<body>
<button onclick="test()">123213</button>


<div class="hide" id="g_id_onload"
     data-client_id="*************-2m8oen42ufat11o749vlpj09snqaernv.apps.googleusercontent.com"
     data-context="signin"
     data-ux_mode="popup"
     data-callback="loginbuuser"
     data-auto_prompt="false">
</div>

<div class="g_id_signin"
     data-type="standard"
     data-shape="rectangular"
     data-theme="outline"
     data-text="signin_with"
     data-size="large"
     data-callback="onsignin"
     data-logo_alignment="left">
</div>
<script src="https://accounts.google.com/gsi/client" async></script>

<script>
    function onsignin(res){
        console.log(res)
    }
    function loginbuuser(res,code) {
        // const responsePayload = decodeJwtResponse(response.credential);

        console.log(res)
    }

</script>
</body>
</html>
