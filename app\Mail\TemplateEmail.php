<?php

namespace App\Mail;

use App\Models\Email\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Blade;
use Throwable;

class TemplateEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(public EmailTemplate $template, public array $data)
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Ardoreva'),
            subject: $this->template->title ?: 'Ardoreva',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $html = "";
        try {
            $html = Blade::render($this->template->content, $this->data);
        } catch (Throwable $exception) {
            logger()->error('TemplateEmail:: ' . $exception->getMessage());
        }
        return new Content(htmlString: $html);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
