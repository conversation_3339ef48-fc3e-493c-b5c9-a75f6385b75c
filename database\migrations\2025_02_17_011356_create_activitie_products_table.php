<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_products', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_experience_activity_id')->index()->comment('活动ID');
            $table->bigInteger('product_id')->index()->comment('产品ID');
            $table->tinyInteger('type')->default(1)->comment('类型 1：固定金额 2：现价百分比');
            $table->decimal('price', 10, 4)->default(0)->comment('价格');
            $table->decimal('percentage', 10, 4)->default(0)->comment('现价百分比');
            $table->bigInteger('currency_id')->index()->comment('货币ID');
            $table->comment('用户体验关联产品');
            $table->unique(['user_experience_activity_id', 'product_id'], 'activity_product_unique');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_products');
    }
};
