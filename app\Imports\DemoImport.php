<?php

namespace App\Imports;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class DemoImport implements ToArray, WithHeadingRow, WithEvents
{
    use RegistersEventListeners;

    /**
     * @var Collection
     */
    protected Collection $projects;

    public static function beforeImport(BeforeImport $event): void
    {
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            return Arr::get(static::$header, $value);
        });
        HeadingRowFormatter::default('slug');
    }

    public static function afterImport(AfterImport $event): void
    {
        HeadingRowFormatter::reset();
    }

    //
    public static array $header = [
        "Bug编号" => "code",
        "所属产品" => "_product",
        "所属模块" => "defect_model_name",
        "所属项目" => 'project_code',
        "相关需求" => '_abort_case',
        "相关任务" => '_abort_task',
        "Bug标题" => 'title',
        "关键词" => 'keywords',
        "严重程度" => 'severity',
        "优先级" => 'priority',
        "Bug类型" => 'type',
        "操作系统" => 'systems',
        "浏览器" => '_browser',
        "重现步骤" => 'detail',
        "Bug状态" => 'status',
        "截止日期" => 'end_date',
        "激活次数" => 'activation_times',
        "是否确认" => 'is_confirmed',
        "抄送给" => 'cc_users',
        "由谁创建" => 'created_user',
        "创建日期" => 'created_at',
        "影响版本" => 'versions',
        "指派给" => 'assigned_user',
        "指派日期" => 'assigned_time',
        "解决者" => 'resolve_user',
        "解决方案" => 'resolve_type',
        "解决版本" => 'resolve_version',
        "解决日期" => 'resolved_time',
        "由谁关闭" => 'closed_user',
        "关闭日期" => 'closed_time',
        "重复ID" => '_repeat_id',
        "相关Bug" => 'abort_case',
        "相关用例" => '_abort',
        "最后修改者" => 'updated_user',
        "修改日期" => 'updated_at',
        "附件" => '_file',
    ];

    public function array(array $array)
    {
        $defectResolveTypeDesc = array_flip(DefectResolveType::list());
        $statusDesc = [
            '激活' => DefectStatus::Activation->value,
            '已解决' => DefectStatus::Resolved->value,
            '已关闭' => DefectStatus::Closed->value,
        ];
        $users = User::query()->select(['id', 'name'])->get();
        $systems = System::query()->select(['id', 'name'])->get();
        $this->projects = Project::query()->select(['id', 'code', 'model_series'])
            ->with([
                // 项目管理员
                'stakeholders' => function (BelongsToMany $q) {
                    $q->select(['users.id', 'users.name'])
                        ->whereHas('roles', fn($q) => $q->where('name', Role::PROJECT_MANAGER));
                },
                'defectVersions',
            ])->get();
        $now = now('PRC');
        foreach ($array as $item) {
            $projectCode = Str::before($item['project_code'], "(#");
            /**
             * @var Project $project
             */
            if (trim($projectCode)) {
                $project = $this->getProject($projectCode);
            } else {
                continue;
            }
            // 模块
            $defectModelName = Str::before($item['defect_model_name'], "(#");
            $defectModel = $project->defectModels()->firstOrCreate([
                'name' => $defectModelName
            ]);
            // 重复导入跳过
            if ($project->defects()
                ->where('defect_model_id', $defectModel->{'id'})
                ->where('title', Arr::get($item, 'title'))
                ->exists()) {
                continue;
            }
            //
            $defect = new Defect\Defect();
            $defect->fill(Arr::only($item, ['keywords', 'title', 'severity', 'priority', 'detail', 'activation_times', 'abort_case', 'code']));
            $defect->priority ??= 1;
            $defect->project()->associate($project);
            $defect->model()->associate($defectModel);
            $data['sku_model_name'] = $project->model_series;
            $data['manager_name'] = $project->stakeholders->value('name') ?: "无";
            $data['type'] = match (Arr::get($item, 'key', "")) {
                '代码错误', '界面优化', '系统功能', '配置相关', '标准规范', '设计缺陷', '安全相关' => DefectType::Function,
                '系统性能', '性能问题' => DefectType::Performance,
                '可靠性' => DefectType::Reliability,
                '硬件测试' => DefectType::Hardware,
                default => DefectType::Other,
            };
            $createUser = $item['created_user'] ? $users->where('name', $item['created_user'])->value('id') : null;
            $data['status'] = Arr::get($statusDesc, Arr::get($item, 'status', '激活'), DefectStatus::Activation->value);
            $data['end_date'] = $item['end_date'] != '0000-00-00' ? Carbon::parse($item['end_date'], 'PRC') : null;
            $data['confirmed_time'] = Arr::get($item, 'is_confirmed', '未确认') == '未确认' ? null : $now;
            $data['created_user_id'] = $createUser ?: Auth::id();
            $data['created_at'] = $item['created_at'] != '0000-00-00' ? Carbon::parse($item['created_at']) : $now;
            $data['assigned_to'] = $item['assigned_user'] ? $users->where('name', $item['assigned_user'])->value('id') : null;
            $data['assigned_time'] = Arr::get($item, 'assigned_time', '0000-00-00') != '0000-00-00' ? Carbon::parse($item['assigned_time']) : $now;
            $data['closed_user_id'] = $item['closed_user'] ? $users->where('name', $item['closed_user'])->value('id') : null;
            $data['closed_time'] = Arr::get($item, 'closed_time', '0000-00-00') != '0000-00-00' ? Carbon::parse($item['closed_time']) : null;
            $data['updated_user_id'] = $item['updated_user'] ? $users->where('name', $item['updated_user'])->value('id') : null;
            $data['updated_user_id'] ??= $data['created_user_id'];
            $data['updated_at'] = Arr::get($item, 'updated_at', '0000-00-00') != '0000-00-00' ? Carbon::parse($item['updated_at']) : $now;
            // 保存
            $defect->fill($data)->save();
            // 保存原始禅道信息
            $defect->chandao()->create(['data' => $item]);

            if ($system = Arr::get($item, 'systems',)) {
                $system = $systems->where('name', $system)->first()?->id;
                $system && $defect->systems()->sync($system);
            }
            if ($ccUser = array_filter(explode(',', $item['cc_users']))) {
                $ccUser = $users->whereIn('name', $ccUser)->pluck('id')->toArray();
                $ccUser && $defect->ccUsers()->sync($ccUser);
            }

            if ($versions = array_map(fn($v) => Str::before($v, '(#'), explode("\n", $item['versions']))) {
                $versionIds = [];
                foreach ($versions as $version) {
                    if ($version) {
                        $versionIds[] = $this->getVersionId($project, $version);
                    }
                }
                $versionIds && $defect->versions()->sync($versionIds);
            }
            $resolveUser = $item['resolve_user'] ? $users->where('name', $item['resolve_user'])->value('id') : null;
            if ($resolveUser) {
                $defect->resolve()->create([
                    'user_id' => $resolveUser,
                    'type' => Arr::get($defectResolveTypeDesc, $item['resolve_type'], DefectResolveType::Resolved->value),
                    'version_id' => $this->getVersionId($project, Str::before($item['resolve_version'], '(#')),
                    'resolved_time' => $item['resolved_time'] != '0000-00-00' ? Carbon::parse($item['resolved_time']) : $now
                ]);
            }
        }
    }


    public function getProject($code)
    {
        $project = $this->projects->where('code', $code)->first();
        if (!$project) {
            $project = new Project();
            $project->fill([
                'code' => $code,
                'model_series' => $code,
                'attribute' => ProjectAttribute::Maintain,
                'manpower_budget_unit' => 'month',
                'status' => ProjectStatus::InProgress,
                'cost_budget' => 0,
                'importance_level' => 1,
                'category' => '',
            ])->save();
            $this->projects->push($project);
        }
        return $project;
    }

    public function getVersionId(Project $project, $versionName)
    {
        if (!$versionName) {
            return null;
        }
        $version = $project->defectVersions->where('version', $versionName)->first();
        if (!$version) {
            $version = $project->defectVersions()->create([
                'version' => $versionName,
                'builder_date' => now("PRC"),
                'builder_user_name' => '禅道导入',
            ]);
            $project->defectVersions->push($version);
        }
        return $version->id;
    }

}
