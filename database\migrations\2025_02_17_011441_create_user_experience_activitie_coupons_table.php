<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_experience_activity_coupons', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_experience_activity_id')->comment('活动ID');
            $table->bigInteger('coupon_id')->index()->comment('优惠券ID');
            $table->comment('用户体验关联-优惠券');
            $table->index('user_experience_activity_id', 'user_experience_activity_id_index');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_experience_activity_coupons');
    }
};
