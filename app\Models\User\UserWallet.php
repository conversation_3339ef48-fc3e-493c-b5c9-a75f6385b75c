<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property float $point
 * @property float $invite_amount
 */
class UserWallet extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'point' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    protected $appends = [
        'invite_amount_agent'
    ];

    /**************** 金额币种转换 ****************/
    public function inviteAmountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->invite_amount, currentCurrency())
        );
    }

    /**
     * 所属用户
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }



    public function inviteAmountRecords(): HasMany
    {
        return $this->hasMany(UserWalletInviteAmountRecord::class);
    }

}
