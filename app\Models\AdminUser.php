<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property  string $account
 * @property  string $name
 * @property  string $phone
 * @property  string $password
 * @property int $id
 */
class AdminUser extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    use HasFactory;
    use HasRoles, HasPermissions;

    protected $guarded = [];
    protected $casts = [
        'last_login_date' => 'datetime',
        'enable' => 'boolean',
    ];

    protected $hidden = ['password'];

    // protected function serializeDate(DateTimeInterface $date)
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 重要， 用于permission
    public function guardName(): string
    {
        return 'admin';
    }

    /**
     * 密码
     * @return Attribute
     */
    public function password(): Attribute
    {
        return Attribute::set(fn($v) => bcrypt($v));
    }
}
