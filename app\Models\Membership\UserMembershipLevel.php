<?php

namespace App\Models\Membership;

use App\Models\MiddleModel;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * @property int $id
 * @property int $user_id
 * @property int $membership_level_id
 * @property float $total_spend_amount
 * @property float $current_year_spend
 * @property Carbon|null $achieved_at
 * @property Carbon|null $expires_at
 * @property bool $is_current
 * @property array|null $upgrade_history
 */
class UserMembershipLevel extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    /**
     * 指示模型是否应该被打上时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    protected $casts = [
        'total_spend_amount' => 'float',
        'current_year_spend' => 'float',
        'achieved_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_current' => 'boolean',
        'upgrade_history' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 所属用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 会员等级
     */
    public function membershipLevel(): BelongsTo
    {
        return $this->belongsTo(MembershipLevel::class);
    }

    /**
     * 作用域：当前等级
     */
    public function scopeCurrent(Builder $query): Builder
    {
        return $query->where('is_current', true);
    }

    /**
     * 作用域：未过期的等级
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * 检查等级是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 检查等级是否即将过期（30天内）
     */
    public function isExpiringSoon(): bool
    {
        return $this->expires_at && 
               $this->expires_at->isFuture() && 
               $this->expires_at->diffInDays(now()) <= 30;
    }

    /**
     * 添加消费金额
     */
    public function addSpendAmount(float $amount): void
    {
        $this->increment('total_spend_amount', $amount);
        
        // 如果是当年的消费，也更新当年消费金额
        if ($this->achieved_at && $this->achieved_at->year === now()->year) {
            $this->increment('current_year_spend', $amount);
        }
    }

    /**
     * 重置当年消费金额（年初调用）
     */
    public function resetCurrentYearSpend(): void
    {
        $this->update(['current_year_spend' => 0]);
    }

    /**
     * 记录升级历史
     */
    public function recordUpgrade(int $fromLevelId, int $toLevelId, float $spendAmount): void
    {
        $history = $this->upgrade_history ?? [];
        $history[] = [
            'from_level_id' => $fromLevelId,
            'to_level_id' => $toLevelId,
            'spend_amount' => $spendAmount,
            'upgraded_at' => now()->toISOString(),
        ];
        
        $this->update(['upgrade_history' => $history]);
    }

    /**
     * 获取升级历史
     */
    public function getUpgradeHistory(): array
    {
        return $this->upgrade_history ?? [];
    }

    /**
     * 获取最近一次升级记录
     */
    public function getLastUpgrade(): ?array
    {
        $history = $this->getUpgradeHistory();
        return empty($history) ? null : end($history);
    }

    /**
     * 设置为当前等级
     */
    public function setAsCurrent(): void
    {
        // 先将该用户的其他等级设为非当前
        self::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_current' => false]);
        
        // 设置当前等级
        $this->update(['is_current' => true]);
    }

    /**
     * 获取用户当前会员等级
     */
    public static function getCurrentLevel(int $userId): ?self
    {
        return self::with('membershipLevel')
            ->where('user_id', $userId)
            ->current()
            ->notExpired()
            ->first();
    }

    /**
     * 创建或更新用户会员等级
     */
    public static function createOrUpdate(int $userId, int $levelId, float $totalSpend): self
    {
        $userLevel = self::firstOrNew([
            'user_id' => $userId,
            'membership_level_id' => $levelId,
        ]);

        if (!$userLevel->exists) {
            $userLevel->fill([
                'total_spend_amount' => $totalSpend,
                'current_year_spend' => $totalSpend,
                'achieved_at' => now(),
                'is_current' => true,
            ]);
        } else {
            $userLevel->total_spend_amount = $totalSpend;
            if ($userLevel->achieved_at && $userLevel->achieved_at->year === now()->year) {
                $userLevel->current_year_spend = $totalSpend;
            }
        }

        $userLevel->save();
        $userLevel->setAsCurrent();

        return $userLevel;
    }
}
