<?php

namespace App\Services\CouponGrant;

use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRule;
use App\Models\Enums\CouponGrantType;
use App\Services\CouponGrant\Strategies\CommentStrategy;
use App\Services\CouponGrant\Strategies\FirstOrderStrategy;
use App\Services\CouponGrant\Strategies\RandomGrantStrategy;
use App\Services\CouponGrant\Strategies\RegisterUserStrategy;
use App\Services\CouponGrant\Strategies\RuleStrategy;
use App\Services\CouponGrant\Strategies\ShareInviteStrategy;
use App\Services\CouponGrant\Strategies\SubscribedStrategy;
use App\Traits\SingletonTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class CouponGrantService
{
    use SingletonTrait;

    public function __construct(public RuleStrategy $strategy)
    {
    }

    public static function getInstance(CouponGrantRule $rule, Model $model): static
    {
        if (!self::$instance || !Arr::exists(self::$instance, $rule->id)) {
            $strategy = match ($rule->type) {
                CouponGrantType::FirstOrder => new FirstOrderStrategy($rule, $model), // 这里的model是Order
                CouponGrantType::Subscribed => new SubscribedStrategy($rule, $model), // 这里的model是User
                CouponGrantType::Comment => new CommentStrategy($rule, $model), // 这里的model是Comment
                CouponGrantType::RegisterUser => new RegisterUserStrategy($rule, $model), // 这里的model是User
                CouponGrantType::ShareInvite => new ShareInviteStrategy($rule, $model), // 这里的model是User/Order
                CouponGrantType::RandomGrant => new RandomGrantStrategy($rule, $model), // 这里的model是Coupon
            };
            self::$instance[$rule->id] = new self($strategy);
        }
        return self::$instance[$rule->id];
    }

    /**
     * 发放
     * @return Coupon|false|void
     * @throws \Throwable
     */
    public function grant($is_framed = false)
    {
       return $this->strategy->grant($is_framed);
    }

}
