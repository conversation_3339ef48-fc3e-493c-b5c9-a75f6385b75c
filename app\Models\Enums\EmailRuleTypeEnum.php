<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum EmailRuleTypeEnum: int

{
    case Order = 1;
    case Subscribe = 2;
    case Register = 3;
    case Fqa = 4;
    case Coupon = 5;


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Order->value => '订单',
            self::Subscribe->value => '订阅',
            self::Register->value => '登录/注册',
            self::Fqa->value => '问题咨询',
            self::Coupon->value => '优惠券'
        ];
    }

}
