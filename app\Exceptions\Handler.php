<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
        DataException::class
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];


    public function render($request, Throwable $e): Response|JsonResponse|\Symfony\Component\HttpFoundation\Response
    {
        // 正式环境需要去敏感
        if (!app()->hasDebugModeEnabled()) {
            if ($e instanceof QueryException) {
                throw new DataException("Database connection error.", ErrorCode::DataError, $e);
            }
        }
        switch (true) {
            case $e instanceof NotFoundHttpException:
            case $e instanceof ModelNotFoundException:
                throw new DataException("Access route does not exist.", ErrorCode::HttpNotFound);
            case $e instanceof MethodNotAllowedHttpException:
                throw new DataException("Access routing method error.", ErrorCode::HttpMethodNotFound);
            case $e instanceof AuthenticationException:
                throw new DataException("User login token is invalid or expired.", ErrorCode::Unauthenticated);
            default:
                break;
        }

        return parent::render($request, $e);
    }

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        // 处理无效商品异常
        $this->renderable(function (InvalidProductException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'code' => $e->getErrorCode(),
                'data' => [
                    'data' => ['invalid_products' => $e->getInvalidProducts()]
                ],
            ]);
        });

        // 默认
        $this->renderable(function (BaseException $e) {
            return response()->json(['message' => $e->getMessage(), 'code' => $e->getErrorCode()]);
        });
    }
}
