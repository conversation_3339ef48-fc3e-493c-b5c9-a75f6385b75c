<?php

namespace App\Models;

use App\Constants\CacheKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;


class PromotionProduct extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];
    protected $casts = [
        'data' => 'json',
    ];

    protected static function booted()
    {
        parent::booted();
        static::creating(function () {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
        static::saved(function () {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
    }

    // 广告位
    public function promotion()
    {
        return $this->belongsTo(Promotion::class);
    }
}
