### API的设计原则：
- 控制API的粒度和数量
- 命名要遵循简单、可读、统一原则；
- 优先设计API，然后编码
### HTTP 状态码、 API 返回格式
200：请求(或处理)成功
```
{
    "code": 200,
    "message": "successful",
    "data": {
    }
}
```
400：逻辑错误
```
{
    "code": 400,
    "message": "密码错误",
    "data": {}
}
```
401：Authorization 不存在或已经失效 \
4012：Authorization 账号禁用 \
4013：Authorization 账号已注销
```
{
    "code": 401,
    "message": "请先登录",
    "data": {}
}
```
403：权限不匹配(无权限)
```
{
    "code": 403,
    "message": "User does not have the right roles.",
    "data": {}
}
```
404：资源找不到
```
{
    "code": 404,
    "message": "用户不存在",
    "data": {}
}
```
422：params 验证错误
>该结构报错一般是表达那内容错误， 格式为固定统一， data.message 为报错总述，如果多个字段一场， 会额外表示 `(and x more error)` ，具体报错内容再 data.errors中，其中key与提交内容的key对应，内容为数组， 多个报错内容，具体展示前端自行判断

```
{
    "data": {
        "message": "The email field is required. (and 1 more error)",
        "errors": {
            "email": [
                "The email field is required."
            ],
            "password": [
                "The password field is required."
            ]
        }
    },
    "code": 422,
    "message": "The email field is required. (and 1 more error)"
}
```
500：系统错误
```
{
    "code": 500,
    "message": "系统错误Division by zero",
    "data": {}
}
```

### Api 出参约定
```json
# 单个对象
{
"code": 200,
"message": "successful",
"data": {
"token": ""
}
}
# list 多个对象
{
"code": 200,
"message": "successful",
"data": {
"data": [
{
"id":1
},
{
"id":2
}
]
},
"meta": {
"total": 100,
"current_page": 1,
"per_page": 10,
"last_page": 1
}
}
```
### 入参约定
除了上传文件用 form-data 外，其他参数一律为 json。请求头：
```
Content-Type: application/json; charset=UTF-8
```
### Restful 接口约定
```
GET：读取（Read）
POST：新建（Create,Update,Delete）
DELETE：删除（Delete）
PUT：更新（Update）
PATCH：更新（Update），通常是部分更新
```
### POST 方法
新增一条项目，比如 /api/projects，则代表新增一条项目入参 json 如下：
```json
{
    "name": "项目管理v1",
    "task": "123",
    "img":["",""]
    // ...
}
```
### PUT 方法
更新某条项目记录，比如 /api/projects/50，入参 json  如下：
```json
{
    "name":"项目管理v2",
    "task": "234",
    "img":["",""]
    // ...
}
```
表示更新一条ID=50的记录
### POST 方法获取列表
获得项目列表，比如 api/projects/_search<br>
因为有分页，需在接口后面追加 ?page=1&~~per_page~~=10
某个具体项目里还包含一个 list，如该项目评论列表，则：api/projects/{id}/comments
我们约定了所有名词复数，都会返回 list，且 list 每个对象都有字段为 id 的唯一 id。<br>
获取项目下某个任务，比如 api/projects/{id}/tasks<br>

### DELETE 方法
删除某条任务，比如 api/projects/{id}

