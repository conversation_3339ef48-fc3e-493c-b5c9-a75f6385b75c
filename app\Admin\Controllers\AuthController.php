<?php

namespace App\Admin\Controllers;

use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{

    /**
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws ValidationException
     */
    public function login(Request $request): JsonResource
    {
        $validated = $request->validate([
            'account' => 'required|string',
            'password' => 'required|string'
        ]);
        $user = AdminUser::query()->where('account', Arr::get($validated, 'account'))->first();
        if (!$user) {
            throw ValidationException::withMessages(['account' => ['账号不存在.'],]);
        }
        if (!$user->{'enable'}) {
            throw new DataException("账号已被禁用,请联系管理员查看原因", ErrorCode::UserStatusDisabled);
        }
        if ($user->{'deleted_at'}) {
            throw new DataException("账号已被删除,请联系管理员查看原因", ErrorCode::UserStatusDestroyed);
        }
        if (!Hash::check(Arr::get($validated, 'password'), $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['输入的密码错误.'],
            ]);
        }
        // token
        $expireAt = now()->addDays(30);
        $token = $user->createToken('admin', ['*'], $expireAt);
        // 更新最后登录时间
        $user->update(['last_login_date' => now()]);

        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt
        ]);
    }

    /**
     * 用户信息
     * @return JsonResource
     */
    public function adminUser(): JsonResource
    {
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        // 当前用户权限
        $user->{'all_permissions'} = $user->getAllPermissions()->select(['id', 'name', 'desc'])->toArray();
        $user->{'all_roles'} = $user->{'roles'}->select(['id', 'name', 'desc'])->toArray();

        // 隐藏角色
        $user->makeHidden(['roles', 'permissions']);

        return JsonResource::make($user);
    }
}
