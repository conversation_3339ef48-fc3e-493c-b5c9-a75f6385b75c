<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Mode extends MiddleModel
{
    use HasFactory;
    protected $guarded = [];

    /**
     * 推荐风格IDs
     * @return HasMany
     */
    public function associatedModeIds(): HasMany
    {
        return $this->hasMany(AssociatedMode::class);
    }

    /**
     * 推荐风格
     * @return BelongsToMany
     */
    public function associatedModes(): BelongsToMany
    {
        return $this->belongsToMany(
            Mode::class,
            AssociatedMode::class,
            'mode_id',
            'associated_id',
            'id',
            'id'
        );
    }
}
