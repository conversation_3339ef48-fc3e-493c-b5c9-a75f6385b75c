<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $name
 */
class Permission extends \Spatie\Permission\Models\Permission
{
    use HasFactory;

    protected $guarded = [];

    // 不可以声明, 包内有用
    protected $casts = [
//        'name' => Permissions::class,
    ];

    // protected function serializeDate(DateTimeInterface $date)
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

}
