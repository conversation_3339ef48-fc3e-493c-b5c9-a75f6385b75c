<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum InviteUserTypeEnum: int
{
    case ExclusiveFans = 1;
    case OrdinaryFans = 2;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::ExclusiveFans->value => '专属粉丝',
            self::OrdinaryFans->value => '普通粉丝',
        ];
    }

}
