<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderUserTypeEnum: int
{
    case Guest = 0;
    case Customer = 1;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Guest->value => 'Guest', // 游客
            self::Customer->value => 'Customer', // 客户
        ];
    }

}
