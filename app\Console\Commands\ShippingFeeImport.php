<?php

namespace App\Console\Commands;

use App\Imports\ShippingFeesImport;
use App\Models\ImportShippingfee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ShippingFeeImport extends Command
{
    /**
     * 命令名称与签名
     */
    protected $signature = 'import:shippingfee';

    /**
     * 命令描述
     */
    protected $description = '导入运费数据';

    public function handle()
    {
        un_limit();
        // 获取待处理的数据
        $imports = ImportShippingfee::query()
            ->where('status', 0) // 0-待处理
            ->select('id', 'file')
            ->get();

        // 更新状态为处理中
        ImportShippingfee::query()
            ->whereIn('id', $imports->pluck('id')->toArray())
            ->update(['status' => 1]); // 1-处理中

        foreach ($imports as $import) {
            try {
                $shippingFeeImport = new ShippingFeesImport();
                $rows = Excel::toCollection($shippingFeeImport, Storage::disk('public')->path($import->file))->first();
                $errors = $shippingFeeImport->array($rows->toArray());

                if ($errors) {
                    $this->saveErrorLog($import, $errors);
                } else {
                    $import->update([
                        'status' => 2, // 2-处理完成
                    ]);
                }
            } catch (\Exception $e) {
                $this->saveErrorLog($import, [$e->getMessage()]);
            }
        }

        $this->info('运费数据导入完成');
        return self::SUCCESS;
    }

    /**
     * 保存错误日志
     */
    protected function saveErrorLog(ImportShippingfee $import, $errors)
    {
        $errorString = is_array($errors) ? implode("\n", $errors) : $errors;

        $logDir = 'storage' . DIRECTORY_SEPARATOR . 'import' . DIRECTORY_SEPARATOR . 'shippingfee' . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR;
        if (!Storage::disk('public')->exists($logDir)) {
            Storage::disk('public')->makeDirectory($logDir);
        }

        $logFile = $logDir . 'error_' . $import->id . '.log';

        Storage::disk('public')->put($logFile, $errorString);

        $import->update([
            'status' => 3, // 3-处理失败
            'error_log' => $logFile,
        ]);
    }
} 