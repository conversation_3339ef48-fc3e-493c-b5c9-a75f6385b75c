<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('currency_code')->unique()->comment('币种代码');
            $table->string('symbol')->comment('符号');
            $table->string('currency_name')->comment('币种名称');
            $table->decimal('exchange_rate', 15, 8)->default(1)->comment('汇率（以$为准）');
            $table->enum('symbol_position', ['before', 'after'])->default('before')->comment('符号位置');
            $table->boolean('active')->default(true)->comment('是否启用');
            $table->timestamps();
            $table->boolean('is_default')->default(false)->comment('是否默认');
            $table->engine('InnoDB');
            $table->comment('语言对应货币配置');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
