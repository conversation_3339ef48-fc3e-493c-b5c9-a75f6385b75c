<?php

use App\Http\Controllers\PaymentController;
use App\Http\Controllers\TestController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('index');
});
Route::get('/facebook', function () {
    return view('facebook');
});
Route::get('/login', function () {
    return view('login');
});
Route::get('/login-google', function () {
    return view('login-google');
});
Route::get('/payment', function () {
    return view('payment');
});
// 中间跳转
Route::any('/payment/airwallex/3ds-check', [PaymentController::class, 'payment3dsCheck']);
//测试发送邮件
Route::get('/test/send_mail', [TestController::class, 'sendMail'])->name('test.send_mail');