<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('货币名称');
            $table->string('code', 10)->unique()->comment('货币代码');
            $table->string('symbol', 10)->comment('货币符号');
            $table->decimal('rate', 10, 4)->default(1)->comment('汇率');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('status')->default(true)->comment('状态：0-禁用，1-启用');
            $table->boolean('is_default')->default(false)->comment('是否默认货币');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
}; 