<?php

namespace App\Models\Enums;

use Carbon\Carbon;
use Illuminate\Support\Arr;

enum AttachmentModelEnum: string
{
    case Product = 'product';
    case User = 'user';
    case Index = 'index';
    case Comment = 'comment';
    case Other = 'other';
    case Video = 'video';

    public function getPath(): string
    {
        return match ($this) {
            self::Product => 'media/catalog/product/' . Carbon::today()->format("Ym"),
            self::User => 'media/catalog/users/' . Carbon::today()->format("Ym"),
            self::Index => 'media/catalog/index/' . Carbon::today()->format("Ym"),
            self::Comment => 'media/catalog/comment/' . Carbon::today()->format("Ym"),
            self::Other => 'media/catalog/other/' . Carbon::today()->format("Ym"),
            self::Video => 'media/catalog/video/' . Carbon::today()->format("Ym"),
        };
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Product->value => '商品',
            self::User->value => '用户',
            self::Index->value => '主页',
            self::Comment->value => '评论',
            self::Other->value => '其他',
            self::Video->value => '视频',
        ];
    }
}
