<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mode_products', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('mode_id')->index()->comment('模式ID');
            $table->bigInteger('product_id')->index()->comment('商品ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mode_products');
    }
};
