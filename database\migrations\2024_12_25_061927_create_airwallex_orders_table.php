<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('airwallex_orders', function (Blueprint $table) {
            $table->id();
            $table->string('intent_id', 32)->index();
            $table->json('detail')->nullable();
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('airwallex支付订单');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('airwallex_orders');
    }
};
