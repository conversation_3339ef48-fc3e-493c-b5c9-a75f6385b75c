<?php

namespace App\Http\Controllers;

use App\Constants\ErrorCode;
use App\Constants\LockKey;
use App\Exceptions\DataException;
use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRule;
use App\Models\CouponRandomGrantRecord;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\User\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;


class CouponGrantController extends Controller
{


    public function randomGrantLottery(CouponGrantRule $rule, Request $request): JsonResource
    {
        if (!$rule->type == CouponGrantType::RandomGrant) {
            throw new DataException("The activity does not exist", ErrorCode::HttpNotFound);
        }
        if (!$rule->enabled) {
            throw new DataException("The event has ended");
        }
        
        if ($rule->total_count !== null && $rule->total_count <= $rule->grant_count) {
            throw new DataException("The rewards have been distributed completely");
        }
        $config = $rule->config;
        $email = $request->validate([
            'email' => 'required|email'
        ])['email'];
        // 需要创建用户
        $user = userService()->findByEmail($email);
        if (!$user instanceof User) {
            $user = userService()->fastCreateUserByEmail($email, UserRegisterTypesEnum::CouponRandomGrant);
        }
        // 无脑的2秒一次
        $lock = Cache::lock(LockKey::UserRandomGrantLottery->getKey($user->id), 2);
        if (!$lock) {
            throw new DataException("The operation is too frequent, please try again later");
        }

        // 检查抽奖次数
        $userRewardTimes = CouponRandomGrantRecord::query()
            ->where('user_id', $user->id)
            ->where('coupon_grant_rule_id', $rule->id)
            ->count();
        if ($userRewardTimes >= Arr::get($config, 'user_lottery_limit', 1)) {
            throw new DataException("The maximum number of lucky draws has been reached");
        }
        // 抽奖 产品无要求做成纯随机
        $weights = [];
        $totalWeight = 0;
        
        // 计算总权重
        foreach ($config['coupons'] as $item) {
            $totalWeight += Arr::get($item, 'weight');
        }
        
        // 如果总权重小于100，添加谢谢参与奖
        if ($totalWeight < 100) {
            $config['coupons'][] = [
                'coupon_id' => null,
                'weight' => 100 - $totalWeight,
                'name' => 'No Luck Today'
            ];
        }
        
        // 生成权重数组
        foreach ($config['coupons'] as $item) {
            $weights = [...$weights, ...array_fill(0, Arr::get($item, 'weight'), Arr::get($item, 'coupon_id'))];
        }
        $rewardId = Arr::random($weights);
        // 奖励发放
        $userReward = new CouponRandomGrantRecord([
            'user_id' => $user->id,
            'coupon_grant_rule_id' => $rule->id,
            'reward_coupon_id' => $rewardId,
        ]);
        $userReward->save();
        return JsonResource::make($userReward);
    }

    /**
     * 随机优惠券活动
     * @param CouponGrantRule $rule
     * @return JsonResource
     * @throws DataException
     */
    public function randomGrantRule(CouponGrantRule $rule): JsonResource
    {
        if (!$rule->type == CouponGrantType::RandomGrant) {
            throw new DataException("The activity does not exist", ErrorCode::HttpNotFound);
        }
        if (!$rule->enabled) {
            throw new DataException("The event has ended");
        }
        $couponIds = Arr::pluck($rule->config['coupons'], 'coupon_id');
        $coupons = Coupon::query()->whereIn('id', $couponIds)->get()->keyBy('id');
        $config = $rule->config;
        $totalWeight = 0;
        foreach ($config['coupons'] as &$coupon) {
            $totalWeight += $coupon['weight'];
            $coupon['coupon'] = $coupons->get($coupon['coupon_id'])->toArray();
        }
        // 如果总权重小于100，添加谢谢参与奖项
        if ($totalWeight < 100) {
            $config['coupons'][] = [
                'weight' => 100 - $totalWeight,
                'coupon_id' => null,
                'name' => 'No Luck Today'
            ];
        }
        $rule->config = $config;
        return JsonResource::make($rule);
    }

}
