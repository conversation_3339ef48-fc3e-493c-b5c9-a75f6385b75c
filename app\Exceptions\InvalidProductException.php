<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use Exception;

class InvalidProductException extends Exception implements BaseException
{
    public function __construct(
        string $message = "",
        public ErrorCode $errorCode = ErrorCode::InvalidProduct,
        public array $invalidProducts = [],
        $previous = null,
        int $code = 0
    ) {
        parent::__construct($message, $code, $previous);
    }

    public function getErrorCode(): int
    {
        return $this->errorCode->value;
    }

    public function getInvalidProducts(): array
    {
        return $this->invalidProducts;
    }
}
