<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use App\Models\InviteAmountWithdraw;

class InviteAmountWithdrawExport implements FromQuery, WithHeadings, WithMapping
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    public function query()
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'ID',
            'User Name',
            'User Email',
            'Inviter Rule',
            'Amount',
            'Status',
            'Created At',
            'Updated At',
        ];
    }

    public function map($withdraw): array
    {
        return [
            $withdraw->id,
            $withdraw->user ? ($withdraw->user->first_name . ' ' . $withdraw->user->last_name) : '',
            $withdraw->user ? $withdraw->user->email : '',
            $withdraw->user && $withdraw->user->invitersRule ? $withdraw->user->invitersRule->title : '',
            $withdraw->amount,
            $withdraw->status,
            $withdraw->created_at,
            $withdraw->updated_at,
        ];
    }
}