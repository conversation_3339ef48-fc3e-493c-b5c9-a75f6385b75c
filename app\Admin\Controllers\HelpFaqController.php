<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\HelpFaq;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class HelpFaqController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::HelpFaqsIndex, Permissions::HelpFaqsIndex)->only(['index', 'show']);
        $this->hasPermissionOr(Permissions::HelpFaqsUpdate)->only(['store', 'update', 'destroy']);
    }

    public function store(Request $request, HelpFaq $helpFaq): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'parent_id' => ['nullable', new Exists(HelpFaq::class, 'id')],
            'icon_id' => ['nullable', 'exists:attachments,id'],
            'desc' => ['nullable', 'string'],
            'sort' => ['required', 'numeric'],
        ]);
        $helpFaq->fill($validated)->save();
        $helpFaq->loadMissing('icon:id,path,disk,module');

        return JsonResource::make($helpFaq);
    }

    public function update(Request $request, HelpFaq $helpFaq): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'parent_id' => ['nullable', new Exists(HelpFaq::class, 'id')],
            'icon_id' => ['nullable', 'exists:attachments,id'],
            'desc' => ['nullable', 'string'],
            'sort' => ['required', 'numeric'],
        ]);
        $helpFaq->update($validated);
        $helpFaq->loadMissing('icon:id,path,disk,module');
        return JsonResource::make($helpFaq);
    }

    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(HelpFaq::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        HelpFaq::query()
            ->whereIn('id', $ids)
            ->delete();

        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $banners = QueryBuilder::for(HelpFaq::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('title'),
                AllowedFilter::exact('parent_id')->default(null),
            ])
            ->with(['icon:id,path,disk,module'])
            ->defaultSort('sort')
            ->allowedSorts('-sort');
        $res = $banners->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(HelpFaq $helpFaq): JsonResource
    {
        $helpFaq->loadMissing([
            'icon:id,path,disk,module',
        ]);
        return JsonResource::make($helpFaq);
    }
}
