<?php

namespace App\Admin\Controllers;

use App\Constants\CacheKey;
use App\Constants\Permissions;
use App\Constants\QueueKey;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Http\Resources\ProductColorResource;
use App\Imports\ProductsImport;
use App\Jobs\Product\ProductSyncErp;
use App\Models\AdminUser;
use App\Models\AssociatedCollection;
use App\Models\AssociatedMode;
use App\Models\Attachment;
use App\Models\AttributeValue;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Enums\ImportProductStatusEnum;
use App\Models\ExternalClient;
use App\Models\Mode;
use App\Models\ImportProduct;
use App\Models\Material;
use App\Models\Product\Product;
use App\Models\Product\ProductColor;
use App\Models\Product\ProductColorImage;
use App\Models\Product\ProductVariant;
use App\Models\Style;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Exists;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\Filters\Filter;
use App\Models\Product\ProductRecommend;


class ProductController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::ProductsUpdate)->only(['updateDetail', 'updateSeo', 'storeVariant', 'store', 'patchUpdate', 'destroy', 'attachedInfo']);
        $this->hasPermissionOr(Permissions::ProductsIndex, Permissions::ProductsUpdate)->only(['index', 'export']);
    }

    public function refreshCache(): JsonResource
    {
        cachePartialDelete(CacheKey::ProductSearchList->getKey('*'));
        return JsonResource::make([]);
    }

    /**
     * 导入商品
     * @param Request $request
     * @return JsonResource
     */
    public function import(Request $request): JsonResource
    {
        $validated = $request->validate([
            'file' => ['required', 'file', 'mimes:xlsx,xls,csv'],
        ]);
        $file = Arr::get($validated, 'file');
        // 获取文件名
        $extension = $file->getClientOriginalExtension();
        // 本地目的文件夹
        $directory = 'storage' . DIRECTORY_SEPARATOR . 'import' . DIRECTORY_SEPARATOR . 'products' . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR;
        // 使用 Laravel Storage 类创建文件夹（如果不存在）
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
        }
        // 生成文件路径
        $fileName = date('YmdHis') . '.' . $extension;
        $path = $directory . $fileName;
        // 将文件移动到指定目录中
        Storage::disk('public')->putFileAs($directory, $file, $fileName);

        // 创建导入对象
        $import = new ImportProduct();
        $import->fill([
            'file' => $path,
            'admin_user_id' => Auth::id(),
        ])->save();
        return JsonResource::make($import);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function store(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:512'],
            'desc' => ['nullable', 'string'],
            'short_desc' => ['nullable', 'string'],
            'model_desc' => ['nullable', 'string'],
            'meta_title' => ['nullable', 'string'],
            'meta_keywords' => ['nullable', 'string'],
            'meta_description' => ['nullable', 'string'],
            'keywords' => ['nullable', 'string'],
            'slug_title' => ['nullable', 'string', 'max:1024'],
            'image_id' => ['required', new Exists(Attachment::class, 'id')],
            'video_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'brand_id' => ['required', new Exists(Brand::class, 'id')],
            'category_id' => ['required', new Exists(Category::class, 'id')],
            'style_id' => ['required', new Exists(Style::class, 'id')],
            'material_id' => ['required', new Exists(Material::class, 'id')],
            'collection_ids' => ['required', 'array'],
            'collection_ids.*' => [new Exists(Collection::class, 'id')],
            'mode_ids' => ['nullable', 'array'],
            'mode_ids.*' => [new Exists(Mode::class, 'id')],
            'make_at' => ['date', 'required'],
            'is_new' => ['bool'],
            'is_featured' => ['bool']
        ]);
        try {
            DB::beginTransaction();
            //默认商品重量
            $validated['weight'] = 0.3;
            // 关联集合
            $collectionIds = Arr::pull($validated, 'collection_ids', default: []);
            // 关联风格
            $ModeIds = Arr::pull($validated, "mode_ids", default: []);
            // 商品信息
            $slug_title = Arr::pull($validated, 'slug_title');
            // 商品信息
            $product->fill($validated);
            // 生成slug title
            if (!$slug_title) {
                $slug_title = $product->generateSlugTitle();
            }
            // 判断slug_title是否重复
            if (Product::query()->where('slug_title', $slug_title)->exists()) {
                throw new DataException('slug_title重复');
            }
            // 无重复则保存
            $product->save();
            // 关联集合
            if ($collectionIds) {
                $product->collections()->sync($collectionIds);
            }
            //更新风格
            if ($ModeIds) {
                $product->modes()->sync($ModeIds);
            }

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($product);
    }

    /**
     * Summary of attachedInfo
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function attachedInfo(Request $request)
    {
        $brands = QueryBuilder::for(Brand::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        $categories = QueryBuilder::for(Category::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        $styles = QueryBuilder::for(Style::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        $materials = QueryBuilder::for(Material::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();

        $modes = QueryBuilder::for(Mode::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();

        return JsonResource::make([
            'brands' => $brands,
            'categories' => $categories,
            'styles' => $styles,
            'materials' => $materials,
            'modes' => $modes,
        ]);
    }

    /**
     * 更新详情信息
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return \Illuminate\Http\Resources\Json\JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function updateDetail(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'brand_id' => ['required', new Exists(Brand::class, 'id')],
            'category_id' => ['required', new Exists(Category::class, 'id')],
            'style_id' => ['required', new Exists(Style::class, 'id')],
            'material_id' => ['required', new Exists(Material::class, 'id')],
            'image_id' => ['required', new Exists(Attachment::class, 'id')],
            'video_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'collection_ids' => ['required', 'array'],
            'collection_ids.*' => [new Exists(Collection::class, 'id')],
            'mode_ids' => ['nullable', 'array'],
            'mode_ids.*' => [new Exists(Mode::class, 'id')],
            'make_at' => ['date', 'required'],
            'is_new' => ['bool'],
            'is_featured' => ['bool'],
            'weight' => ['required', 'numeric']
        ]);
        // if ($product->is_publish) {
        //     throw new DataException('上架的产品不可修改');
        // }

        try {
            DB::beginTransaction();

            // 关联集合
            $collectionIds = Arr::pull($validated, 'collection_ids', default: []);
            // 关联风格
            $ModeIds = Arr::pull($validated, "mode_ids", default: []);
            // 商品信息
            $product->update($validated);
            // 更新集合
            if (is_array($collectionIds)) {
                $product->collections()->sync($collectionIds);
            }
            //更新风格
            if ($ModeIds) {
                $product->modes()->sync($ModeIds);
            }

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($product);
    }

    /**
     * 更新SEO信息
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return \Illuminate\Http\Resources\Json\JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function updateSeo(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:512'],
            'desc' => ['nullable', 'string'],
            'short_desc' => ['nullable', 'string'],
            'model_desc' => ['nullable', 'string'],
            'meta_title' => ['nullable', 'string'],
            'meta_keywords' => ['nullable', 'string'],
            'meta_description' => ['nullable', 'string'],
            'slug_title' => ['nullable', 'string', 'max:1024'],
            'keywords' => ['nullable', 'string'],
        ]);
        // if ($product->is_publish) {
        //     throw new DataException('上架的产品不可修改');
        // }
        try {
            DB::beginTransaction();
            $product->fill($validated);
            $slug_title = Arr::pull($validated, 'slug_title') ?: $product->generateSlugTitle();
            if (Product::query()->where('slug_title', $slug_title)->where('id', '<>', $product->id)->exists()) {
                throw new DataException('slug_title重复');
            }
            $product->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($product);
    }

    /**
     * 修改部分值
     * @param Request $request
     * @param Product $product
     * @return JsonResource
     */
    public function patchUpdate(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'is_publish' => ['bool'],
            'is_featured' => ['bool'],
            'is_new' => ['bool'],
            'collection_ids' => ['array'],
            'collection_ids.*' => ['int', new Exists(Collection::class, 'id')],
            'publish_at' => ['nullable', 'date', function ($attribute, $value, $fail) use ($request) {
                if ($value && $request->input('is_publish')) {
                    $fail('已上架商品不能设置预上架时间');
                }
            }],
        ]);
        $collections = Arr::get($validated, 'collection_ids');
        $baseUpdate = Arr::except($validated, ['collection_ids']);
        // 上架判断
        if (Arr::get($validated, 'is_publish')) {
            if ($product->variants()->count() == 0) {
                throw new DataException('请先添加商品变体再上架');
            }
        }

        // 如果设置了预上架时间，将 is_publish 设置为 2
        if (Arr::get($validated, 'publish_at')) {
            $baseUpdate['is_publish'] = 2;
        }

        if (is_array($baseUpdate)) {
            $product->update($baseUpdate);
        }
        if (is_array($collections)) {
            $product->collections()->sync($collections);
        }
        return JsonResource::make($product);
    }

    /**
     * 无权限的用于后台筛选框
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function options(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Product::class, $request)
            ->select(['id', 'title', 'spu', 'image_id'])
            ->with(['image:id,path,disk,module'])
            ->allowedFilters([
                AllowedFilter::partial('spu'),
                AllowedFilter::partial('slug_title'),
                AllowedFilter::exact('brand_id'),
                AllowedFilter::exact('category_id'),
                AllowedFilter::exact('style_id'),
                AllowedFilter::exact('material_id'),
                AllowedFilter::exact('collection_ids', 'collections.id'),
                AllowedFilter::exact('mode_ids', 'modeProducts.id'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(Product::class, $request)
            ->with(['image:id,path,disk,module', 'collectionIds', 'modes', 'modeIds'])
            ->allowedFilters([
                AllowedFilter::partial('sku', 'spu'),
                AllowedFilter::partial('slug_title'),
                AllowedFilter::partial('title'),
                AllowedFilter::exact('keywords'),
                AllowedFilter::exact('is_publish'),
                AllowedFilter::exact('is_new'),
                AllowedFilter::exact('is_featured'),
                AllowedFilter::exact('brand_id'),
                AllowedFilter::exact('category_id'),
                AllowedFilter::exact('style_id'),
                AllowedFilter::callback('start_at', function ($query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('update_at', function ($query, $value) {
                    $query->where('updated_at', '>=', $value);
                }),
                AllowedFilter::callback('new_publish_at', function ($query, $value) {
                    $query->where('new_publish_at', '>=', $value)->orWhere('publish_at', '>=', $value);
                }),
                AllowedFilter::callback('material_ids', function ($query, $value) {
                    return $query->whereIn('material_id', (array)$value);
                }),
                AllowedFilter::exact('collection_ids', 'collections.id'),
                AllowedFilter::exact('mode_ids', 'modes.id'),
            ])
            ->allowedSorts(['id', 'updated_at'])
            ->defaultSort('-updated_at');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function indexAll(Request $request): AnonymousResourceCollection
    {
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(Product::class, $request)
            ->with(['image:id,path,disk,module', 'collectionIds', 'modes', 'modeIds'])
            ->allowedFilters([
                AllowedFilter::partial('sku', 'spu'),
                AllowedFilter::partial('slug_title'),
                AllowedFilter::partial('title'),
                AllowedFilter::exact('keywords'),
                AllowedFilter::exact('is_publish'),
                AllowedFilter::exact('brand_id'),
                AllowedFilter::exact('category_id'),
                AllowedFilter::exact('style_id'),
                AllowedFilter::callback('material_ids', function ($query, $value) {
                    return $query->whereIn('material_id', (array)$value);
                }),
                AllowedFilter::exact('collection_ids', 'collections.id'),
                AllowedFilter::exact('mode_ids', 'modes.id'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->get();
        return JsonResource::collection($res);
    }

    /**
     * 展示
     * @param \App\Models\Product\Product $product
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(Product $product): JsonResource
    {
        $product->loadMissing([
            'image:id,path,module',
            'video:id,path'
        ]);

        return JsonResource::make($product);
    }

    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Product::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Product::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

    /**
     * 同步erp
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed
     */
    public function syncErp(Request $request)
    {
        // un_limit();
        // $validated = $request->validate([
        //     'ids' => ['array', 'required'],
        //     'ids.*' => [(new Exists(Product::class, 'id'))],
        // ]);

        // $products = Product::query()->whereIn('id', $validated['ids'])->get();

        // // 同步到erp
        // foreach ($products as $product) {
        //     ProductSyncErp::dispatch($product, true)->onQueue(QueueKey::Product->value);
        // }

        return response()->json();
    }

    /**
     * 颜色
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return AnonymousResourceCollection
     */
    public function colors(Request $request, Product $product): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for($product->colors(), $request)
            ->allowedFilters([
                AllowedFilter::exact('color_id')
            ])
            ->with([
                'images:id,path,module',
                'color:id,value'
            ])
            ->allowedSorts(['id', 'created_at', 'sort'])
            ->defaultSort('-sort');

        return JsonResource::collection($builder->get());
    }

    /**
     * 选择
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return
     */
    public function colorsOptions(Request $request, Product $product)
    {
        $builder = QueryBuilder::for($product->colors(), $request)
            ->with([
                'color:id,value'
            ])
            ->allowedSorts(['sort'])
            ->defaultSort('-sort')
            ->get();

        return ProductColorResource::collection($builder);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function storeColors(Request $request, Product $product)
    {
        $validated = $request->validate([
            'color_id' => ['required', new Exists(AttributeValue::class, 'id')],
            'sort' => ['numeric'],
            'images' => ['required'],
            'images.*.id' => ['required', new Exists(Attachment::class, 'id')],
            'images.*.sort' => ['numeric'],
        ]);
        try {
            DB::beginTransaction();

            $images = Arr::pull($validated, 'images', []);

            // 新增
            $color = $product->colors()->create($validated);
            if ($images) {
                $imagesToSync = Arr::mapWithKeys($images, function ($image) {
                    return [$image['id'] => ['sort' => $image['sort'] ?? 0]];
                });
                $color->images()->sync($imagesToSync);
            }
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw new DataException($th->getMessage());
        }

        return JsonResource::make($color->loadMissing('images:id,path,module'));
    }

    /**
     * 更新
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\ProductColor $color
     * @return JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function updateColors(Request $request, ProductColor $color)
    {
        $validated = $request->validate([
            'color_id' => ['required', new Exists(AttributeValue::class, 'id')],
            'sort' => ['numeric'],
            'images' => ['required'],
            'images.*.id' => ['required', new Exists(Attachment::class, 'id')],
            'images.*.sort' => ['numeric'],
        ]);

        try {
            DB::beginTransaction();

            // 图片
            $images = Arr::pull($validated, 'images', default: []);
            // 关联的变体
            $variants = $color->product->variants()->where('color_attribute_value_id', $color->color_id)->get();

            // 更新
            $color->update($validated);
            $imagesToSync = Arr::mapWithKeys($images, function ($image) {
                return [$image['id'] => ['sort' => $image['sort'] ?? 0]];
            });
            $color->images()->sync($imagesToSync);
            // 刷新
            $color->refresh();

            // 同步对应颜色的变体
            $variants->map(function ($variant) use ($color, $imagesToSync) {
                $variant->update([
                    'color_attribute_value_id' => $color->color_id,
                    'sort' => $color->sort,
                    'image_id' => $color->images()->first()->id ?? 0,
                ]);
                $variant->images()->sync($imagesToSync);
            });
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw new DataException($th->getMessage());
        }

        return JsonResource::make($color->loadMissing('images:id,path,module'));
    }

    // 选择商品
    public function select(Request $request)
    {
        $request_data = $request->all();
        if (Arr::get($request_data, 'collection_sell', false)) {
            $query_collection_ids = array_unique(AssociatedCollection::query()
                ->whereIn('collection_id', Arr::get($request_data, 'collection_ids', []))
                ->pluck('associated_id')
                ->toArray());
        } else {
            $query_collection_ids = array_unique(Arr::get($request_data, 'collection_ids', []));
        }
        // 风格是否使用推荐项(注意去重)
        if (Arr::get($request_data, 'mode_sell', false)) {
            $query_mode_ids = array_unique(AssociatedMode::query()
                ->whereIn('mode_id', Arr::get($request_data, 'mode_ids', []))
                ->pluck('associated_id')
                ->toArray());
        } else {
            $query_mode_ids = array_unique(Arr::get($request_data, 'mode_ids', []));
        }

        // 筛选
        $builder = QueryBuilder::for(Product::class, $request)
            ->with(['image:id,path,disk,module', 'collectionIds', 'modes', 'modeIds'])
            ->where(function ($query) use ($query_collection_ids, $query_mode_ids) {
                $query->whereHas('collectionIds', function ($query) use ($query_collection_ids) {
                    $query->whereIn('collection_id', $query_collection_ids);
                })
                    ->orWhereHas('modeIds', function ($query) use ($query_mode_ids) {
                        $query->whereIn('mode_id', $query_mode_ids);
                    });
            })
            ->allowedSorts(['new_publish_at'])
            ->defaultSort('-new_publish_at');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     *  导出商品为CSV
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed
     */
    public function export(Request $request)
    {
        // 验证
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => ['required', 'integer', new Exists(Product::class, 'id')],
        ]);
        // 查数据
        $builder = QueryBuilder::for(ProductVariant::class, $request)
            ->with(['product'])
            ->whereIn('product_id', Arr::get($validated, 'ids'))
            ->allowedSorts(['product_id'])
            ->defaultSort('product_id');
        // 导出文件
        $excel = new \App\Exports\ProductExport($builder->getEloquentBuilder());
        // 下载文件
        return $excel->download('products.csv');
    }

    /**
     * 导入商品列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function importIndex(Request $request)
    {
        $builder = QueryBuilder::for(ImportProduct::class, $request)
            ->with([
                'adminUser:id,name'
            ])
            ->allowedFilters([
                AllowedFilter::callback('status', function (Builder $builder, $value) {
                    if ($value !== '') {
                        $builder->where('status', $value);
                    }
                }),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        // 分页
        $res = $builder->paginate($this->getPerPage());

        // 处理 status 数据
        $res->each(function ($item) {
            $item->status = ImportProductStatusEnum::from($item->status)->desc();
        });

        return JsonResource::collection($res);
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed
     */
    public function importDestroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => ['required', 'integer', new Exists(ImportProduct::class, 'id')],
        ]);
        $ids = Arr::get($validated, 'ids');
        ImportProduct::query()->whereIn('id', $ids)->delete();
        return response()->json();
    }

    /**
     * 推荐商品
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return JsonResource
     */
    public function recommend(Product $product): JsonResource
    {
        $builder = QueryBuilder::for(ProductRecommend::class)
            ->where('product_id', $product->id)
            ->with(['recommendProduct:id,spu,min_price,origin_price,image_id', 'recommendProduct.image:id,path,module']);
        $res = $builder->get();
        $result = $res->map(function ($item) {
            $recommend = $item->recommend;
            // 加入图片信息
            if ($item->recommendProduct && $item->recommendProduct->image) {
                $recommend['image'] = [
                    'id' => $item->recommendProduct->image->id,
                    'path' => $item->recommendProduct->image->url,
                    'url' => $item->recommendProduct->image->url,
                    'disk' => $item->recommendProduct->image->disk,
                    'module' => $item->recommendProduct->image->module,
                ];
            } else {
                $recommend['image'] = null;
            }
            return [
                'id' => $item->id,
                'recommend_product_id' => $item->recommend_product_id,
                'sort' => $item->sort,
                'recommend' => $recommend,
            ];
        });
        return JsonResource::collection($result);
    }

    /**
     * 新增推荐商品
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return JsonResource
     */
    public function storeRecommend(Request $request, Product $product): JsonResource
    {
        $validated = $request->validate([
            'sku' => ['required', 'string', new Exists(Product::class, 'spu')],
        ]);

        $product_id = Product::query()->where('spu', $validated['sku'])->value('id');
        // 判断唯一性
        $exists = $product->productRecommends()
            ->where('recommend_product_id', $product_id)
            ->exists();

        if ($exists) {
            throw new DataException('该商品已被推荐，不能重复添加');
        }
        $product->productRecommends()->create([
            'recommend_product_id' => $product_id,
        ]);
        // 重新获取所有推荐，并带上 recommendProduct 关联
        $recommends = $product->productRecommends()
            ->with(['recommendProduct:id,spu,min_price,origin_price,image_id', 'recommendProduct.image:id,path,module'])
            ->get();

        $result = $recommends->map(function ($item) {
            $recommend = $item->recommend;
            // 加入图片信息
            if ($item->recommendProduct && $item->recommendProduct->image) {
                $recommend['image'] = [
                    'id' => $item->recommendProduct->image->id,
                    'path' => $item->recommendProduct->image->path,
                    'disk' => $item->recommendProduct->image->disk,
                    'module' => $item->recommendProduct->image->module,
                ];
            } else {
                $recommend['image'] = null;
            }
            return [
                'id' => $item->id,
                'recommend_product_id' => $item->recommend_product_id,
                'sort' => $item->sort,
                'recommend' => $recommend,
            ];
        });

        return JsonResource::collection($result);
    }

    /**
     * 删除推荐商品
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product\Product $product
     * @return JsonResponse|mixed
     */
    public function destroyRecommend(Request $request, Product $product)
    {
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => ['required', 'integer', new Exists(Product::class, 'id')],
        ]);
        $recommendProductIds = Arr::get($validated, 'ids');
        ProductRecommend::query()
            ->where('product_id', $product->id)
            ->whereIn('recommend_product_id', $recommendProductIds)
            ->delete();
        return response()->json();
    }
}
