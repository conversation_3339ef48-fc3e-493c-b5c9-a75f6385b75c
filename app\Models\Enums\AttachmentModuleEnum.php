<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum AttachmentModuleEnum: string
{
    case Product = 'product';
    case User = 'user';
    case Index = 'index';
    case Comment = 'comment';
    case Other = 'other';
    case Video = 'video';

    // 上传图片路径
    public function getPath(): string
    {
        return match ($this) {
            self::Product => 'media/catalog/product/',
            self::User => 'media/catalog/users/',
            self::Index => 'media/catalog/index/',
            self::Comment => 'media/catalog/comment/',
            self::Other => 'media/catalog/other/',
            self::Video => 'media/catalog/video/',
        };
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Product->value => '商品',
            self::User->value => '用户',
            self::Index->value => '主页',
            self::Comment->value => '评论',
            self::Other->value => '其他',
            self::Video->value => '视频'
        ];
    }

    // 小图路径
    public function getCachePaths(): array
    {
        return match ($this) {
            self::Product => [
                'suffix' => 'webp',
                'small' => 'cache/1/image/resize/90x120/',
                'medium' => 'cache/1/image/resize/450x600/',
                'large' => 'cache/1/image/original/'
            ],
            self::User => [],
            self::Index => [],
            self::Comment => [],
            self::Other => [],
            self::Video => []
        };
    }
}
