<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Banner\Banner;
use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\SysConfigKeyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class BannerController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::BannersUpdate)->except(['index', 'store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::BannersIndex, Permissions::BannersUpdate)->only(['index', 'show']);
    }

    public function store(Request $request, Banner $banner): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'equipment_type' => ['required', 'int', new Enum(EquipmentTypeEnum::class)],
            'image_id' => ['required', 'exists:attachments,id'],
            'url' => ['nullable', 'string', 'max:512'],
            'sort' => ['required', 'numeric'],
            'is_visible' => ['nullable', 'boolean'],
            'video_text' => ['nullable', 'array'],
            'video_text.title' => ['nullable', 'string'],
            'video_text.title_color' => ['nullable', 'string'],
            'video_text.desc' => ['nullable', 'string'],
            'video_text.desc_color' => ['nullable', 'string'],
            'video_text.button_text' => ['nullable', 'string', 'max:16'],
            'video_text.button_color' => ['nullable', 'string'],
            'video_text.button_text_color' => ['nullable', 'string'],
            'video_text.position' => ['nullable', 'string'],
        ]);
        DB::beginTransaction();
        try {
            $banner->fill($validated)->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($banner);
    }

    public function update(Request $request, Banner $banner): JsonResource
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'equipment_type' => ['required', 'int', new Enum(EquipmentTypeEnum::class)],
            'image_id' => ['nullable', 'exists:attachments,id'],
            'url' => ['nullable', 'string', 'max:512'],
            'sort' => ['required', 'numeric'],
            'is_visible' => ['nullable', 'boolean'],
            'video_text' => ['nullable', 'array'],
            'video_text.title' => ['nullable', 'string'],
            'video_text.title_color' => ['nullable', 'string'],
            'video_text.desc' => ['nullable', 'string'],
            'video_text.desc_color' => ['nullable', 'string'],
            'video_text.button_text' => ['nullable', 'string', 'max:16'],
            'video_text.button_color' => ['nullable', 'string'],
            'video_text.button_text_color' => ['nullable', 'string'],
            'video_text.position' => ['nullable', 'string'],
        ]);

        DB::beginTransaction();
        try {
            $banner->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($banner);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Banner::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            Banner::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {

        $banners = QueryBuilder::for(Banner::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('is_visible'),
                AllowedFilter::exact('equipment_type'),
            ])
            ->with(['image:id,path,disk,module'])
            ->defaultSort('sort')
            ->allowedSorts('-sort');
        $res = $banners->paginate($this->getPerPage());
        $config = sysConfigService()->get(SysConfigKeyEnum::BannerCarouselConfig);
        $banner_carousel = Arr::get($config, 'value');
        return JsonResource::collection($res)->additional(['banner_carousel' => $banner_carousel]);
    }

    public function show(Banner $banner): JsonResource
    {
        $banner->loadMissing([
            'image:id,path,disk,module'
        ]);
        return JsonResource::make($banner);
    }

    public function status(Request $request, Banner $banner){
        $validated = $request->validate([
            'is_visible' => ['required', 'boolean'],
        ]);
        $banner->update($validated);
        return response()->json();
    }
}
