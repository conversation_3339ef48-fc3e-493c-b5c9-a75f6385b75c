<?php

namespace App\Models\Enums\Reward;

use App\Models\Coupon\Coupon;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;

enum RewardTypeEnum: int
{
    case  FixedAmount = 1;
    case  Percent = 2;
    case  Coupon = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::FixedAmount->value => '固定金额',
            self::Percent->value => '百分比',
            self::Coupon->value => '优惠券',
        ];
    }

    /**
     * 获取验证规则
     * @param RewardFansTypeEnum $fans_type
     * @return array<array<Enum|Exists|string>|string>
     */
    public function validateRules(RewardFansTypeEnum $fans_type): array
    {
        return match ($this) {
            self::FixedAmount => [
                "rules.{$fans_type->value}" => ['required', 'array'], // 粉丝规则
                "rules.{$fans_type->value}.first_order" => ['required', 'array'], // 首单规则
                "rules.{$fans_type->value}.first_order.price" => ['required', 'numeric', 'min:0'],
                "rules.{$fans_type->value}.first_order.currency" => ['required', 'string'],
                "rules.{$fans_type->value}.other_order" => ['required', 'array'], // 其他订单规则
                "rules.{$fans_type->value}.other_order.price" => ['required', 'numeric', 'min:0'],
                "rules.{$fans_type->value}.other_order.currency" => ['required', 'string'],
            ],
            self::Percent => [
                "rules.{$fans_type->value}" => ['required', 'array'], // 粉丝规则
                "rules.{$fans_type->value}.first_order" => ['required', 'array'], // 首单规则
                "rules.{$fans_type->value}.first_order.percent" => 'required|numeric', // 百分比
                "rules.{$fans_type->value}.first_order.max_price_type" => ['required', new Enum(RewardPercentMaxPriceTypeEnum::class)], // 最大金额类型
                "rules.{$fans_type->value}.first_order.max_price" => ["required_if:rules.{$fans_type->value}.first_order.max_price_type," . RewardPercentMaxPriceTypeEnum::FixedAmount->value, 'numeric'], // 最大金额
                "rules.{$fans_type->value}.first_order.currency" => ["required_if:rules.{$fans_type->value}.first_order.max_price_type," . RewardPercentMaxPriceTypeEnum::FixedAmount->value, 'string'], // 币种
                "rules.{$fans_type->value}.other_order" => ['required', 'array'], // 其他订单规则
                "rules.{$fans_type->value}.other_order.percent" => 'required|numeric', // 百分比
                "rules.{$fans_type->value}.other_order.max_price_type" => ['required', new Enum(RewardPercentMaxPriceTypeEnum::class)], // 最大金额类型
                "rules.{$fans_type->value}.other_order.max_price" => ["required_if:rules.{$fans_type->value}.other_order.max_price_type," . RewardPercentMaxPriceTypeEnum::FixedAmount->value, 'numeric'], // 最大金额
                "rules.{$fans_type->value}.other_order.currency" => ["required_if:rules.{$fans_type->value}.other_order.max_price_type," . RewardPercentMaxPriceTypeEnum::FixedAmount->value, 'string'], // 币种
            ],
            self::Coupon => [
                "rules.{$fans_type->value}" => ['required', 'array'], // 粉丝规则
                "rules.{$fans_type->value}.first_order" => ['required', 'array'], // 首单规则
                "rules.{$fans_type->value}.first_order.coupon_id" => ['required', new Exists(Coupon::class, 'id')],
                "rules.{$fans_type->value}.other_order" => ['required', 'array'], // 其他订单规则
                "rules.{$fans_type->value}.other_order.coupon_id" => ['required', new Exists(Coupon::class, 'id')],
            ],
            default => [],
        };
    }

}
