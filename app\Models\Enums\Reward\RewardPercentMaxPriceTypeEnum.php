<?php

namespace App\Models\Enums\Reward;

use Illuminate\Support\Arr;

enum RewardPercentMaxPriceTypeEnum: int
{
    case None = 0;
    case FixedAmount = 1;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::None->value => '无',
            self::FixedAmount->value => '固定金额'
        ];
    }
}
