<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\MembershipService;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Models\CouponGrantRule;
use App\Models\Enums\CouponGrantType;
use App\Exceptions\DataException;
use App\Models\Coupon\Coupon;
use App\Models\User\User;
use App\Models\User\UserPointChangeRecord;
use App\Services\UserPointService;

class MembershipController extends Controller
{
    protected MembershipService $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * 获取用户会员信息
     */
    public function me(): JsonResource
    {
        $user = Auth::user();
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);

        return JsonResource::make($membershipInfo);
    }

    /**
     * 获取所有会员等级列表
     */
    public function levels(): AnonymousResourceCollection
    {
        $levels = $this->membershipService->getAllMembershipLevels();

        return JsonResource::collection($levels);
    }

    /**
     * 获取用户积分记录（使用新的统一积分系统）
     */
    public function pointRecords(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();

        $records = QueryBuilder::for(UserPointChangeRecord::class, $request)
            ->byUser($user->id)
            ->with(['source'])
            ->allowedSorts(['created_at', 'change_amount'])
            ->allowedFilters(['change_type', 'status'])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }

    /**
     * 获取积分统计信息（使用新的统一积分系统）
     */
    public function pointStats(): JsonResource
    {
        $user = Auth::user();

        // 使用新的积分服务获取统计信息
        $pointService = new UserPointService();
        $stats = $pointService->getUserPointStats($user);

        return JsonResource::make($stats);
    }

    /**
     * 获取会员等级权益详情
     */
    public function levelBenefits(MembershipLevel $level): JsonResource
    {
        return JsonResource::make([
            'level' => $level,
            'benefits' => $level->getBenefitsDescription(),
            'next_level' => $level->getNextLevel(),
        ]);
    }

    /**
     * 获取用户会员等级历史
     */
    public function levelHistory(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        
        $history = QueryBuilder::for(UserMembershipLevel::class, $request)
            ->where('user_id', $user->id)
            ->with('membershipLevel')
            ->allowedSorts(['achieved_at', 'total_spend_amount'])
            ->defaultSort('-achieved_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($history);
    }

    /**
     * 获取会员等级升级进度
     */
    public function upgradeProgress(): JsonResource
    {
        $user = Auth::user();
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);
        
        $currentLevel = $membershipInfo['current_level'];
        $nextLevel = $membershipInfo['next_level'];
        $totalSpend = $membershipInfo['total_spend_amount'];
        $upgradeRequired = $membershipInfo['upgrade_required_amount'];

        $progress = [
            'current_level' => $currentLevel ? $currentLevel->membershipLevel : $membershipInfo['default_level'],
            'next_level' => $nextLevel,
            'total_spend_amount' => $totalSpend,
            'upgrade_required_amount' => $upgradeRequired,
            'progress_percentage' => 0,
        ];

        if ($nextLevel && $currentLevel) {
            $currentLevelMin = $currentLevel->membershipLevel->min_spend_amount;
            $nextLevelMin = $nextLevel->min_spend_amount;
            $levelRange = $nextLevelMin - $currentLevelMin;
            $currentProgress = $totalSpend - $currentLevelMin;
            $progress['progress_percentage'] = $levelRange > 0 ? min(100, ($currentProgress / $levelRange) * 100) : 100;
        }

        return JsonResource::make($progress);
    }

    /**
     * 获取本月积分获得情况
     */
    public function monthlyPoints(): JsonResource
    {
        $user = Auth::user();

        $monthlyRecords = UserPointChangeRecord::byUser($user->id)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->increase() // 只获取积分增加记录
            ->with('source')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalPoints = $monthlyRecords->sum('change_amount');
        $recordsBySource = $monthlyRecords->groupBy('change_type');

        return JsonResource::make([
            'total_points' => $totalPoints,
            'records_count' => $monthlyRecords->count(),
            'records_by_source' => $recordsBySource->map(function ($records, $source) {
                return [
                    'source' => $source,
                    'count' => $records->count(),
                    'total_points' => $records->sum('change_amount'),
                    'records' => $records->take(5), // 只返回最近5条
                ];
            }),
        ]);
    }

    /**
     * 获取年度积分获得情况
     */
    public function yearlyPoints(): JsonResource
    {
        $user = Auth::user();
        
        $yearlyRecords = UserPointChangeRecord::byUser($user->id)
            ->whereYear('created_at', now()->year)
            ->increase() // 只获取积分增加记录
            ->selectRaw('MONTH(created_at) as month, SUM(change_amount) as total_points, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $totalYearPoints = UserPointChangeRecord::byUser($user->id)
            ->whereYear('created_at', now()->year)
            ->increase()
            ->sum('change_amount');

        return JsonResource::make([
            'total_year_points' => $totalYearPoints,
            'monthly_breakdown' => $yearlyRecords,
        ]);
    }

    /**
     * 获取会员等级对比
     */
    public function levelComparison(): JsonResource
    {
        $levels = $this->membershipService->getAllMembershipLevels();
        
        $comparison = $levels->map(function ($level) {
            return [
                'id' => $level->id,
                'name' => $level->name,
                'slug' => $level->slug,
                'min_spend_amount' => $level->min_spend_amount,
                'max_spend_amount' => $level->max_spend_amount,
                'point_rate' => $level->point_rate,
                'point_rate_percentage' => ($level->point_rate * 100) . '%',
                'color' => $level->color,
                'benefits' => $level->getBenefitsDescription(),
                'sort_order' => $level->sort_order,
            ];
        });

        return JsonResource::make([
            'levels' => $comparison,
            'comparison_fields' => [
                'point_rate' => '积分比例',
                'benefits' => '会员权益',
                'min_spend_amount' => '升级门槛',
            ],
        ]);
    }

    /**
     * 获取可积分兑换的优惠券列表
     * @return JsonResource
     */
    public function pointExchangeCoupons(): JsonResource
    {

        // 查询所有启用的积分兑换规则
        $pointExchangeRules = CouponGrantRule::query()
            ->where('type', CouponGrantType::PointExchange->value)
            ->where('enabled', true)
            ->where('effective_start_at', '<=', now())
            ->where('effective_end_at', '>=', now())
            ->where(function($query) {
                $query->whereNull('total_count')
                      ->orWhereRaw('total_count > grant_count');
            })
            ->with(['coupon' => function($query) {
                $query->where('enabled', true)
                      ->whereNull('deleted_at');
            }])
            ->get();

        $exchangeableCoupons = [];

        foreach ($pointExchangeRules as $rule) {
            if (!$rule->config) {
                continue;
            }
            $config = $rule->config;
            $coupons = $config['coupons'] ?? [];

            foreach ($coupons as $couponConfig) {
                $couponId = $couponConfig['coupon_id'];
                $requiredPoints = $couponConfig['required_points'];

                // 获取优惠券详情
                $coupon = Coupon::find($couponId);
                if (!$coupon || !$coupon->enabled) {
                    continue;
                }

                $exchangeableCoupons[] = [
                    'rule_id' => $rule->id,
                    'coupon_id' => $coupon->id,
                    'coupon_name' => $coupon->name,
                    'coupon_desc' => $coupon->desc,
                    'coupon_type' => $coupon->type,
                    'coupon_rules' => $coupon->rules,
                    'required_points' => $requiredPoints,
                    'effective_start_at' => $coupon->effective_start_at,
                    'effective_end_at' => $coupon->effective_end_at,
                    'effective_days' => $coupon->effective_days,
                ];
            }
        }

        return JsonResource::make([
            'exchangeable_coupons' => $exchangeableCoupons
        ]);
    }

    /**
     * 积分兑换优惠券
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function exchangeCouponWithPoints(Request $request): JsonResource
    {
        $validated = $request->validate([
            'rule_id' => 'required|integer|exists:coupon_grant_rules,id',
            'coupon_id' => 'required|integer|exists:coupons,id',
        ]);

        /** @var User $user */
        $user = Auth::user();

        // 确保用户有钱包
        if (!$user->wallet) {
            $user->wallet()->create([
                'point' => 0,
                'invite_amount' => 0,
            ]);
            $user->refresh();
        }

        $ruleId = $validated['rule_id'];
        $couponId = $validated['coupon_id'];

        // 查找积分兑换规则
        $rule = CouponGrantRule::query()
            ->where('id', $ruleId)
            ->where('type', CouponGrantType::PointExchange->value)
            ->where('enabled', true)
            ->where('effective_start_at', '<=', now())
            ->where('effective_end_at', '>=', now())
            ->where(function($query) {
                $query->whereNull('total_count')
                      ->orWhereRaw('total_count > grant_count');
            })
            ->first();

        if (!$rule) {
            throw new DataException('The exchange rule does not exist or has expired');
        }

        // 检查优惠券是否在规则配置中
        $config = $rule->config;
        $coupons = $config['coupons'] ?? [];
        $requiredPoints = null;

        foreach ($coupons as $couponConfig) {
            if ($couponConfig['coupon_id'] == $couponId) {
                $requiredPoints = $couponConfig['required_points'];
                break;
            }
        }

        if ($requiredPoints === null) {
            throw new DataException('The coupon is not in the redemption list');
        }

        // 检查用户积分是否足够
        $userPoints = $user->wallet->point;
        if ($userPoints < $requiredPoints) {
            throw new DataException('Insufficient points, unable to redeem');
        }

        // 使用积分兑换策略
        $strategy = new \App\Services\CouponGrant\Strategies\PointExchangeStrategy($rule, $user, $couponId, $requiredPoints);

        try {
            $coupon = $strategy->grant(true);

            if (!$coupon) {
                throw new DataException('Exchange failed, please try again later');
            }

            return JsonResource::make([
                'success' => true,
                'message' => 'Exchange successful',
                'coupon' => [
                    'id' => $coupon->id,
                    'name' => $coupon->name,
                    'desc' => $coupon->desc,
                    'type' => $coupon->type,
                    'rules' => $coupon->rules,
                    'code' => $coupon->code
                ],
                'points_used' => $requiredPoints,
                'remaining_points' => $user->wallet->fresh()->point,
            ]);
        } catch (\Exception $e) {
            throw new DataException('Exchange failed：' . $e->getMessage());
        }
    }

    /**
     * 获取用户积分变化记录（统一的积分变化记录）
     */
    public function pointChangeRecords(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();

        $records = QueryBuilder::for(UserPointChangeRecord::class, $request)
            ->byUser($user->id)
            ->with(['source'])
            ->allowedSorts(['created_at', 'change_amount'])
            ->allowedFilters([
                'change_type',
                'status',
                AllowedFilter::callback('type', function (Builder $builder, $value) {
                    if ($value === 'increase') {
                        $builder->where('change_amount', '>', 0);
                    } elseif ($value === 'decrease') {
                        $builder->where('change_amount', '<', 0);
                    }
                })
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }
}
