<?php

namespace App\Models\User;

use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRule;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property Coupon $coupon
 * * @property Carbon $effective_start_at
 * * @property Carbon $effective_end_at
 */
class UserCoupon extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $appends = [
        'expire_at'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function couponGrantRule(): BelongsTo
    {
        return $this->belongsTo(CouponGrantRule::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function expireAt(): Attribute
    {
        return Attribute::get(function () {
            return $this->effective_end_at;
        });
    }
}
