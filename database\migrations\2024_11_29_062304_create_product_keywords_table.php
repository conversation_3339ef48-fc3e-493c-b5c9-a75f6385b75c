<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_keywords', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_id')->index();
            $table->string('title', 512)->comment('标题');
            $table->string('slug_title', 1024)->comment('slug标题');
            $table->string('category_name', 512)->nullable()->comment('分类');
            $table->string('material_name',512)->nullable()->comment('材质名称');
            $table->text('colors')->nullable()->comment('颜色');
            $table->text('sizes')->nullable()->comment('尺寸');
            $table->text('sku')->nullable()->comment('sku');
            $table->text('collection_names')->nullable()->comment('导航栏名称');

            $table->engine('InnoDB');
            $table->timestamps();;
            // 全文索引
            $table->fullText('title');
            $table->fullText('slug_title');
            $table->fullText('category_name');
            $table->fullText('material_name');
            $table->fullText('colors');
            $table->fullText('sizes');
            $table->fullText('sku');
            $table->fullText('collection_names');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_keywords');
    }
};
