<?php

namespace Database\Seeders;

use App\Models\Collection;
use App\Models\Enums\CollectionJumpTypeEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class CollectionTestSeeder extends Seeder
{

    protected array $collections = [
        [
            'title' => 'Lingerie',
            'sort' => 1,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [
                [
                    'title' => 'SHOP BY CATEGORY',
                    'sort' => 1,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::None,
                    'url' => null,
                    'extra' => [],
                    'children' => [
                        [
                            'title' => 'Babydoll and Slips',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Teddies and Bodysuits',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Bra and Panty sets',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Bra and Skirt sets',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ]
                    ],
                ], [
                    'title' => 'SHOP BY MATERIAL',
                    'sort' => 1,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::None,
                    'url' => null,
                    'extra' => [],
                    'children' => [
                        [
                            'title' => 'Lace',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Fishnets',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Mesh Fabrics',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Leather Lingerie',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Silk & Satin',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                        [
                            'title' => 'Cotton',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [
                            ],
                        ],
                    ],
                ],
                [
                    'title' => 'SHOP BY DESIGN',
                    'sort' => 1,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::None,
                    'url' => null,
                    'extra' => [],
                    'children' => [
                        [
                            'title' => 'Role Play Costumes',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [],
                        ],
                        [
                            'title' => 'Garter Lingerie',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [],
                        ],
                        [
                            'title' => 'Ultra Sexy Lingerie',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [],
                        ],
                        [
                            'title' => 'Crotchless Lingerie',
                            'sort' => 1,
                            'image_id' => null,
                            'jump_type' => CollectionJumpTypeEnum::Collection,
                            'url' => null,
                            'extra' => [],
                            'children' => [],
                        ],
                    ],
                ],

            ]
        ],
        [
            'title' => 'Plus Size',
            'sort' => 2,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [],
        ],
        [
            'title' => 'New In',
            'sort' => 3,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [],
        ],
        [
            'title' => 'Holiday',
            'sort' => 4,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [],
        ],
        [
            'title' => 'Sleepwear',
            'sort' => 5,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [
                [
                    'title' => 'Robes',
                    'sort' => 1,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::Collection,
                    'url' => null,
                    'extra' => [],
                    'children' => [],
                ],
                [
                    'title' => 'Mini Dress&Nightgowns',
                    'sort' => 2,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::Collection,
                    'url' => null,
                    'extra' => [],
                    'children' => [],
                ],
                [
                    'title' => 'Pajama Set',
                    'sort' => 3,
                    'image_id' => null,
                    'jump_type' => CollectionJumpTypeEnum::Collection,
                    'url' => null,
                    'extra' => [],
                    'children' => [],
                ],
            ],
        ],
        [
            'title' => 'Accessories',
            'sort' => 6,
            'image_id' => null,
            'jump_type' => CollectionJumpTypeEnum::Collection,
            'url' => null,
            'extra' => [],
            'children' => [],
        ],
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 空的时候填充
        if (!Collection::query()->exists()) {
            $this->saveCollection($this->collections);
        }
    }

    public function saveCollection($list, $parent_id = null)
    {
        foreach ($list as $collection) {
            $collection['parent_id'] = $parent_id;
            $data = Arr::except($collection, ['children']);
            $children = Arr::get($collection, 'children', []);
            $collection = new Collection();
            $collection->fill($data)->save();
            $children && $this->saveCollection($children, $collection->id);
        }
    }
}
