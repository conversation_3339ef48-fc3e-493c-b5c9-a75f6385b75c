<?php

namespace App\Models;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ActivityNav extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;
    public $timestamps = true;
    protected $guarded = [];

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    public function getStatusAttribute($value)
    {
        return (bool) $value;
    }

}
