<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\UserExperienceActivitie;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class UserExperienceActivitieController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::UserExperienceActivitiesUpdate)->except(['index', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::UserExperienceActivitiesIndex, Permissions::UserExperienceActivitiesUpdate)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $UserExperienceActivitie = QueryBuilder::for(UserExperienceActivitie::class, $request)
            ->with('adminUser')
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $UserExperienceActivitie->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function store(Request $request): JsonResource
    {
        // 验证请求数据
        $validated = $request->validate([
            'activity_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1024'],
            'is_listed' => ['nullable', 'boolean'],
            'is_coupon' => ['nullable', 'boolean'],
            'is_first' => ['nullable', 'boolean'],
            'is_register' => ['nullable', 'boolean'],
            'start_at' => ['required', 'date'],
            'end_at' => ['required', 'date'],
            'products' => ['required', 'array'], // 产品数组
            'products.*.product_id' => ['required', 'integer'], // 每个产品的ID
            'products.*.type' => ['required', 'integer'], // 产品类型
            'products.*.price' => ['nullable', 'numeric'], // 产品价格
            'products.*.percentage' => ['nullable', 'numeric'], // 产品百分比
            'coupon_ids' => ['nullable', 'array'], // 优惠券数组
            'coupon_ids.*' => ['nullable', 'integer'], // 每个优惠券的ID
        ]);

        DB::beginTransaction();

        try {
            $validated['admin_user_id'] = Auth::user()->id;
            // 创建活动
            $userExperienceActivitie = UserExperienceActivitie::create(Arr::only($validated, [
                'activity_name',
                'description',
                'is_listed',
                'is_coupon',
                'is_first',
                'is_register',
                'start_at',
                'end_at',
                'admin_user_id',
            ]));

            // 关联产品
            Arr::map($validated['products'], function ($productData) use ($userExperienceActivitie) {
                $userExperienceActivitie->activitieProducts()->create([
                    'product_id' => $productData['product_id'],
                    'type' => $productData['type'],
                    'price' => $productData['price'] ?: 0,
                    'percentage' => $productData['percentage'] ?: 0,
                    'currency_id' => $productData['currency_id'] ?? 1,
                ]);
            });

            // 关联优惠券
            Arr::map($validated['coupon_ids'], function ($couponData) use ($userExperienceActivitie) {
                $userExperienceActivitie->userExperienceActivitieCoupons()->create([
                    'coupon_id' => $couponData,
                ]);
            });

            DB::commit();

            return JsonResource::make($userExperienceActivitie);

        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
    }

    public function update(Request $request, $id): JsonResource
    {
        // 验证请求数据
        $validated = $request->validate([
            'activity_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1024'],
            'is_listed' => ['nullable', 'boolean'],
            'is_coupon' => ['nullable', 'boolean'],
            'is_first' => ['nullable', 'boolean'],
            'is_register' => ['nullable', 'boolean'],
            'start_at' => ['required', 'date'],
            'end_at' => ['required', 'date'],
            'products' => ['required', 'array'], // 产品数组
            'products.*.product_id' => ['required', 'integer'], // 每个产品的ID
            'products.*.type' => ['required', 'integer'], // 产品类型
            'products.*.price' => ['nullable', 'numeric'], // 产品价格
            'products.*.percentage' => ['nullable', 'numeric'], // 产品百分比
            'coupon_ids' => ['nullable', 'array'], // 优惠券数组
            'coupon_ids.*' => ['nullable', 'integer'], // 每个优惠券的ID
        ]);

        DB::beginTransaction();

        try {
            // 找到活动并更新
            $userExperienceActivitie = UserExperienceActivitie::findOrFail($id);
            $validated['admin_user_id'] = Auth::user()->id;
            $userExperienceActivitie->update(Arr::only($validated, [
                'activity_name',
                'description',
                'is_listed',
                'is_coupon',
                'is_first',
                'is_register',
                'start_at',
                'end_at',
                'admin_user_id',
            ]));

            // 删除原有的关联产品和优惠券（可以根据需求调整是否要保留之前的）
            $userExperienceActivitie->activitieProducts()->delete();
            $userExperienceActivitie->userExperienceActivitieCoupons()->delete();

            // 重新关联产品
            Arr::map($validated['products'], function ($productData) use ($userExperienceActivitie) {
                $userExperienceActivitie->activitieProducts()->create([
                    'product_id' => $productData['product_id'],
                    'type' => $productData['type'],
                    'price' => $productData['price'] ?: 0,
                    'percentage' => $productData['percentage'] ?: 0,
                    'currency_id' => $productData['currency_id'] ?? 1,
                ]);
            });

            // 重新关联优惠券
            Arr::map($validated['coupon_ids'], function ($couponData) use ($userExperienceActivitie) {
                $userExperienceActivitie->userExperienceActivitieCoupons()->create([
                    'coupon_id' => $couponData,
                ]);
            });

            DB::commit();

            return JsonResource::make($userExperienceActivitie);

        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
    }


    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(UserExperienceActivitie::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            UserExperienceActivitie::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $UserExperienceActivitie = QueryBuilder::for(UserExperienceActivitie::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('activity_name'),
            ])
            ->with('adminUser:id,name')
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $UserExperienceActivitie->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    //上下架
    public function status(Request $request, UserExperienceActivitie $UserExperienceActivitie): JsonResponse
    {
        $validated = $request->validate([
            'is_listed' => ['required', 'boolean'],
        ]);
        $isListed = $validated['is_listed'];

        if ($isListed) {
            $newStartAt = $UserExperienceActivitie->start_at;
            $newEndAt = $UserExperienceActivitie->end_at;

            $overlappingActivities = QueryBuilder::for(UserExperienceActivitie::class, $request)
                // ->allowedFilters([
                // ])
                ->where('is_listed', 1)
                ->where('is_automatic_listed', 0)
                ->where(function ($query) use ($newStartAt, $newEndAt) {
                    $query->whereBetween('start_at', [$newStartAt, $newEndAt])
                        ->orWhereBetween('end_at', [$newStartAt, $newEndAt])
                        ->orWhere(function ($query) use ($newStartAt, $newEndAt) {
                            $query->where('start_at', '<', $newStartAt)
                                ->where('end_at', '>', $newEndAt);
                        });
                })
                ->exists();

            if ($overlappingActivities) {
                return response()->json([
                    'message' => '新活动的时间周期与已有上线活动时间冲突，无法上线。',
                ], 400);
            }
        }

        // 更新活动上下架状态
        $UserExperienceActivitie->update([
            'is_listed' => $isListed,
        ]);
        return response()->json();
        // return JsonResource::make($UserExperienceActivitie);
    }

    public function show(UserExperienceActivitie $UserExperienceActivitie): JsonResource
    {
        $UserExperienceActivitie->loadMissing([
            'activitieProducts.product:id,title,spu,max_price,min_price,image_id,origin_price',
            'activitieProducts.product.image:id,path,disk,module',
            'UserExperienceActivitieCoupons.coupon:id,name'
        ]);
        return JsonResource::make($UserExperienceActivitie);
    }

}
