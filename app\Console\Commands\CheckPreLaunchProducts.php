<?php

namespace App\Console\Commands;

use App\Models\Product\Product;
use Illuminate\Console\Command;

class CheckPreLaunchProducts extends Command
{
    protected $signature = 'products:check-pre-launch';
    protected $description = '检查预上架商品并更新状态';

    public function handle()
    {
        $products = Product::query()
            ->where('is_publish', 2)
            ->whereNotNull('publish_at')
            ->where('publish_at', '<=', now())
            ->get();

        foreach ($products as $product) {
            $product->update(['is_publish' => true, 'new_publish_at' => now()]);
            $this->info("Product {$product->id} has been published and cache cleared.");
        }

        $this->info('Pre-launch products check completed.');
    }
} 