<?php

namespace Database\Seeders;

use App\Models\Task\TaskPointConfig;
use Illuminate\Database\Seeder;

class TaskPointConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configs = [
            // 注册相关
            [
                'task_type' => 'registration',
                'task_name' => '用户注册',
                'channel' => null,
                'points_per_action' => 100,
                'daily_limit' => 0,
                'total_limit' => 1,
                'is_active' => true,
                'description' => '新用户注册奖励',
                'sort_order' => 1,
            ],

            // 生日奖励
            [
                'task_type' => 'birthday',
                'task_name' => '生日奖励',
                'channel' => null,
                'points_per_action' => 200,
                'daily_limit' => 1,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '生日当天奖励积分',
                'sort_order' => 2,
            ],

            // 关注任务
            [
                'task_type' => 'wechat_follow',
                'task_name' => '关注微信',
                'channel' => 'wechat',
                'points_per_action' => 50,
                'daily_limit' => 0,
                'total_limit' => 1,
                'is_active' => true,
                'description' => '关注官方微信公众号',
                'sort_order' => 10,
            ],
            [
                'task_type' => 'weibo_follow',
                'task_name' => '关注微博',
                'channel' => 'weibo',
                'points_per_action' => 30,
                'daily_limit' => 0,
                'total_limit' => 1,
                'is_active' => true,
                'description' => '关注官方微博账号',
                'sort_order' => 11,
            ],
            [
                'task_type' => 'ins_follow',
                'task_name' => '关注INS',
                'channel' => 'instagram',
                'points_per_action' => 30,
                'daily_limit' => 0,
                'total_limit' => 1,
                'is_active' => true,
                'description' => '关注官方Instagram账号',
                'sort_order' => 12,
            ],
            [
                'task_type' => 'ytb_follow',
                'task_name' => '关注YTB',
                'channel' => 'youtube',
                'points_per_action' => 40,
                'daily_limit' => 0,
                'total_limit' => 1,
                'is_active' => true,
                'description' => '关注官方YouTube频道',
                'sort_order' => 13,
            ],

            // 分享任务
            [
                'task_type' => 'fb_share',
                'task_name' => '在FB上分享',
                'channel' => 'facebook',
                'points_per_action' => 10,
                'daily_limit' => 3,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '在Facebook上分享内容',
                'sort_order' => 20,
            ],
            [
                'task_type' => 'twitter_share',
                'task_name' => '在Twitter上分享',
                'channel' => 'twitter',
                'points_per_action' => 10,
                'daily_limit' => 3,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '在Twitter上分享内容',
                'sort_order' => 21,
            ],

            // 内容创作
            [
                'task_type' => 'post_review',
                'task_name' => '发表审核通过的帖子',
                'channel' => null,
                'points_per_action' => 20,
                'daily_limit' => 5,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '发表帖子并通过审核',
                'sort_order' => 30,
            ],
            [
                'task_type' => 'video_review',
                'task_name' => '发表审核通过的带视频评论',
                'channel' => null,
                'points_per_action' => 30,
                'daily_limit' => 3,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '发表带视频的评论并通过审核',
                'sort_order' => 31,
            ],

            // 日常任务
            [
                'task_type' => 'check_in',
                'task_name' => '签到',
                'channel' => null,
                'points_per_action' => 5,
                'daily_limit' => 1,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '每日签到奖励',
                'sort_order' => 40,
            ],

            // 问卷调查
            [
                'task_type' => 'questionnaire',
                'task_name' => '问卷',
                'channel' => null,
                'points_per_action' => 50,
                'daily_limit' => 2,
                'total_limit' => 0,
                'is_active' => true,
                'description' => '完成问卷调查',
                'sort_order' => 50,
            ],

            // 群组加入
            [
                'task_type' => 'fb_group_join',
                'task_name' => '加入FB群组',
                'channel' => 'facebook',
                'points_per_action' => 25,
                'daily_limit' => 0,
                'total_limit' => 3,
                'is_active' => true,
                'description' => '加入官方Facebook群组',
                'sort_order' => 60,
            ],
        ];

        foreach ($configs as $config) {
            TaskPointConfig::create($config);
        }
    }
}
