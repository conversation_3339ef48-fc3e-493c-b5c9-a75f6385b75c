<?php

use App\Models\Enums\EquipmentTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('title', 255)->comment('标题');
            $table->tinyInteger('equipment_type')->default(EquipmentTypeEnum::WebEquipment->value)->comment('设备类型');
            $table->foreignId('image_id')->nullable()->comment('关联的图片 ID');
            $table->string('url', 512)->nullable()->comment('跳转链接');
            $table->boolean('is_visible')->default(false)->comment('是否显示：1 显示，0 不显示');
            $table->unsignedInteger('sort')->default(0)->comment('排序，值越小优先级越高');
            $table->text('video_text')->nullable()->comment('视频文本配置项');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('Banner配置');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
};
