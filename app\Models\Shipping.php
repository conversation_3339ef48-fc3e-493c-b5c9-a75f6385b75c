<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\ShippingFee;

class Shipping extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'is_default' => 'boolean',
    ];

    protected $appends = [
        'fee_agent',
    ];

    /**************** 金额币种转换 ****************/
    public function feeAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->fee, currentCurrency())
        );
    }


    public static function getDefaultShipping()
    {
        return self::query()->where('is_default', 1)->first();
    }

    /**
     * 运费明细
     * @return HasMany
     */
    public function shippingFees(): HasMany
    {
        return $this->hasMany(ShippingFee::class, 'shipping_id');
    }

}
