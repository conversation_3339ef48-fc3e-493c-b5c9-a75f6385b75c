<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class ImportProduct extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    public static array $header = [
        "SKU编码",
        "商品名称",
        "描述",
        "关键词",
        "简介",
        "分类",
        "model描述",
        "meta标题",
        "meta关键词",
        "meta description",
        "slug标题",
        "品牌名称",
        "品类",
        "款式",
        "材质",
        "生产时间",
        "风格",
        "SKU主图",
        "推荐",
        "颜色",
        "尺码",
        "售价($)",
        "原价($)",
        "库存",
        "SKC排序",
        "SKC主图",
        "SKC图片",
        "是否上架"
    ];

    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class);
    }
}
