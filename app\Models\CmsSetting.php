<?php

namespace App\Models;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CmsSetting extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;
    public $timestamps = true;
    protected $guarded = [];

    protected $casts = [
        'status' => 'boolean',
    ];

    protected $appends = [
        'admin_user_name'
    ];

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    public function clickImage(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'click_image_id');
    }

    //用户信息
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'admin_user_id');
    }

    public function getAdminUserNameAttribute()
    {
        return $this->adminUser->name??'';
    }

    //关联配置文章
    public function cmsArticles(): hasMany
    {
        return $this->hasMany(CmsArticle::class);
    }

}
