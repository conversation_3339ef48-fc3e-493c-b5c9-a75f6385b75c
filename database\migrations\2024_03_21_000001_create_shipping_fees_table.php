<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_fees', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('country', 20)->comment('国家iso_code');
            $table->decimal('min_weight', 10, 3)->nullable()->comment('区间最小重量(kg)');
            $table->decimal('max_weight', 10, 3)->nullable()->comment('区间最大重量(kg)');
            $table->decimal('shipping_fee', 10, 2)->nullable()->comment('运费');
            $table->bigInteger('shipping_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_fees');
    }
}; 