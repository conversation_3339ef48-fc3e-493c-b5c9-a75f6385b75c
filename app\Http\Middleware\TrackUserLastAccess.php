<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class TrackUserLastAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // 只处理已认证的用户
        if (Auth::check()) {
            $user = Auth::user();
            $cacheKey = "user_last_access_{$user->id}";

            // 使用缓存来避免频繁更新数据库
            if (!Cache::has($cacheKey)) {
                $user->updateLastAccessed();
                // 缓存5分钟，避免频繁更新
                Cache::put($cacheKey, now(), 300);
            }
        }

        return $response;
    }
}
