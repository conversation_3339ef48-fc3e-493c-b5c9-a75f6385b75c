<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_settings', function (Blueprint $table) {
            $table->id();
            $table->string('title', 100)->comment('名称');
            $table->bigInteger('image_id')->index()->comment('图片ID');
            $table->bigInteger('click_image_id')->index()->comment('已点击图片ID');
            $table->integer('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态');
            $table->bigInteger('admin_user_id')->nullable()->index()->comment('更新人');
            $table->dateTime('published_at')->nullable()->comment('发布时间');
            $table->softDeletes();
            $table->timestamps();
            $table->comment('cms分类配置');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_settings');
    }
};
