<?php

namespace App\Http\Middleware;

use Alexkart\CurlBuilder\Command;
use App\Exceptions\DataException;
use Closure;
use GuzzleHttp\Psr7\ServerRequest;
use Illuminate\Http\Request;

class BuildCurlLog
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        /**
         * @var \Illuminate\Http\JsonResponse $response
         */
        $response = $next($request);

        if (app()->hasDebugModeEnabled()) {
            $command = new Command();
            $command->setRequest(new ServerRequest($request->getMethod(), $request->getUri(), $request->header(), $request->getContent()));
            logger()->channel('request_curl')->info($command->build());
        }
        return $response;
    }
}
