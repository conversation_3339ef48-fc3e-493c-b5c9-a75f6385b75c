<?php

namespace App\Models;

use App\Models\Coupon\Coupon;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserExperienceActivitieCoupon extends MiddleModel
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = [];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
    //关联优惠券信息
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

}
