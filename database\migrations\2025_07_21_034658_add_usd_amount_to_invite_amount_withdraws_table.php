<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invite_amount_withdraws', function (Blueprint $table) {
            $table->decimal('usd_amount', 10, 2)->nullable()->comment('美元提现金额');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invite_amount_withdraws', function (Blueprint $table) {
            $table->dropColumn('usd_amount');
        });
    }
};
