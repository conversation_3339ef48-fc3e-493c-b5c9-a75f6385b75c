<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('cart_id')->index();
            $table->bigInteger('product_id')->index()->comment('商品id');
            $table->bigInteger('product_variant_id')->index()->comment('商品变种id');
            $table->integer('num')->default(1)->comment('加入数量');
            $table->decimal('add_price', 10, 2)->default(0)->comment('加入时单价');
            $table->decimal('original_price', 10, 4)->comment('原价');
            $table->decimal('discount', 10, 4)->default(0)->comment('购物车优惠单条汇总（默认折扣的不算进来）');
            $table->bigInteger('coupon_id')->nullable()->comment('优惠券id');
            $table->decimal('price', 10, 4)->comment('单价(原价-discount)');
            $table->bigInteger('user_experience_activity_id')->nullable()->comment('用户体验活动id');
            $table->boolean('is_activity')->default(0)->comment('是否是活动产品');
            $table->decimal('weight', 10, 3)->nullable()->comment('单件商品重量kg');
            $table->timestamps();
            $table->comment('购物车详情表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
