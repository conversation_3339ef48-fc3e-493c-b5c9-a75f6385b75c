<?php

namespace App\Jobs\Order;

use App\Constants\QueueKey;
use App\Models\User\UserPointChangeRecord;
use App\Services\UserPointService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessDelayedMembershipPoints implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 处理延迟发放的会员积分
     */
    public function __construct(public UserPointChangeRecord $pointRecord)
    {
        $this->onQueue(QueueKey::Order->value);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 检查积分记录是否已经发放
            if ($this->pointRecord->isCompleted()) {
                return;
            }

            // 检查用户是否存在
            $user = $this->pointRecord->user;
            if (!$user) {
                return;
            }

            // 使用新的积分服务处理待发放积分
            $pointService = new UserPointService();
            $pointService->processPendingPoints($this->pointRecord);

            Log::info('Delayed membership points distributed successfully', [
                'point_record_id' => $this->pointRecord->id,
                'user_id' => $this->pointRecord->user_id,
                'change_amount' => $this->pointRecord->change_amount,
                'change_type' => $this->pointRecord->change_type,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process delayed membership points', [
                'point_record_id' => $this->pointRecord->id,
                'user_id' => $this->pointRecord->user_id,
                'change_type' => $this->pointRecord->change_type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessDelayedMembershipPoints job failed permanently', [
            'point_record_id' => $this->pointRecord->id,
            'user_id' => $this->pointRecord->user_id,
            'change_type' => $this->pointRecord->change_type,
            'change_amount' => $this->pointRecord->change_amount,
            'error' => $exception->getMessage(),
        ]);
    }
}
