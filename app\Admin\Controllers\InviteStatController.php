<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserInviteStatListResource;
use App\Models\DailyInviteStat;
use App\Models\Enums\Reward\RewardTypeEnum;
use App\Models\InviteAmountWithdraw;
use App\Models\User\User;
use App\Models\User\UserInviteReward;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\Enums\User\InviteAmountWithdrawStatusEnum;

class InviteStatController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::InvitesStatIndex)->only(['index', 'show']);
    }

    // 邀请统计
    public function stat(Request $request)
    {
        $start_at = request('filter.start_at', now()->subDays(14)->startOfDay());
        $end_at = request('filter.end_at', now()->endOfDay());
        $stats = QueryBuilder::for(DailyInviteStat::class)
            ->allowedFilters([
                AllowedFilter::partial('email'),
                // 以下作用为占位
                AllowedFilter::callback('start_at', function ($query, $value) use ($start_at) {
                    $query->where('date', '>=', $value ?? $start_at);
                }),
                AllowedFilter::callback('end_at', function ($query, $value) use ($end_at) {
                    $query->where('date', '<=', $value ?? $end_at);
                })
            ])
            ->selectRaw('
                date,
                SUM(share_count) as share_count,
                SUM(exclusive_fans_count) as exclusive_fans_count,
                SUM(ordinary_fans_count) as ordinary_fans_count,
                SUM(total_fans_count) as total_fans_count,
                SUM(estimated_count) as estimated_count,
                SUM(estimated_amount) as estimated_amount,
                SUM(withdraw_count) as withdraw_count,
                SUM(withdraw_amount) as withdraw_amount
            ')
            ->groupBy('date')
            ->get();

        // 整理结果
        $total = [
            'share_count' => 0,
            'exclusive_fans_count' => 0,
            'ordinary_fans_count' => 0,
            'estimated_count' => 0,
            'estimated_amount' => 0,
            'withdraw_count' => 0,
            'withdraw_amount' => 0
        ];
        // 每日数据
        $daily = [];
        foreach ($stats as $item) {
            // 总计的数据
            $total['share_count'] += $item->share_count;
            $total['exclusive_fans_count'] += $item->exclusive_fans_count;
            $total['ordinary_fans_count'] += $item->ordinary_fans_count;
            $total['estimated_count'] += $item->estimated_count;
            $total['estimated_amount'] += $item->estimated_amount;
            $total['withdraw_count'] += $item->withdraw_count;
            $total['withdraw_amount'] += $item->withdraw_amount;

            // 每日数据
            $daily[$item->date] = [
                'date' => $item->date,
                'share_count' => (int) $item->share_count,
                'total_fans_count' => (int) $item->total_fans_count,
                'estimated_count' => (int) $item->estimated_count,
                'estimated_amount' => (float) $item->estimated_amount,
            ];
        }

        // 是否查询了当日的数据
        if (date('Y-m-d') >= $start_at ||
            date('Y-m-d') <= $end_at) {
            // 查询当日数据
            $result = UserService()->todayInviteStat();

            // 更新总计的数据
            $total['share_count'] += Arr::get($result, 'share_count', 0);
            $total['exclusive_fans_count'] += Arr::get($result, 'exclusive_fans_count', 0);
            $total['ordinary_fans_count'] += Arr::get($result, 'ordinary_fans_count', 0);
            $total['estimated_count'] += Arr::get($result, 'estimated_count', 0);
            $total['estimated_amount'] += Arr::get($result, 'estimated_amount', 0);
            $total['withdraw_count'] += Arr::get($result, 'withdraw_count', 0);
            $total['withdraw_amount'] += Arr::get($result, 'withdraw_amount', 0);

            // 更新每日的数据
            // 每日数据
            $daily[now()->toDateString()] = [
                'date' => now()->toDateString(),
                'share_count' => Arr::get($result, 'share_count', 0),
                'total_fans_count' => Arr::get($result, 'total_fans_count', 0),
                'estimated_count' => Arr::get($result, 'estimated_count', 0),
                'estimated_amount' => Arr::get($result, 'estimated_amount', 0),
            ];
        }

        // 整理每日数据
        $daily = DailyInviteStat::fillEmptyDate($daily, $start_at, $end_at);

        return JsonResource::collection([
            'total' => $total,
            'daily' => $daily
        ]);
    }

    /**
     * 邀请统计列表
     * @param Request $request
     * @return JsonResource
     */
    public function index(Request $request): JsonResource
    {
        $query = QueryBuilder::for(User::class)
            ->allowedFilters([
                AllowedFilter::partial('email'),
                // 以下作用为占位
                AllowedFilter::callback('start_at', function ($query, $value) {
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                })
            ])
            ->with([
                'allInvitedUsers' => function ($query) {
                    // 获取请求参数
                    if (request()->has('filter.start_at')) {
                        $query->where('invite_users.created_at', '>=', request('filter.start_at'));
                    }
                    if (request()->has('filter.end_at')) {
                        $query->where('invite_users.created_at', '<=', request('filter.end_at'));
                    }
                },
                'inviteRewards' => function ($query) {
                    // 获取请求参数
                    if (request()->has('start_at')) {
                        $query->where('order_created_at', '>=', request('start_at'));
                    }
                    if (request()->has('end_at')) {
                        $query->where('order_created_at', '<=', request('end_at'));
                    }
                    $query->where('is_effective', true);
                }
            ])
            ->whereNotNull('first_share_date')
            ->allowedSorts([
                'first_share_date'
            ])
            ->defaultSort('-first_share_date');

        return UserInviteStatListResource::collection($query->paginate($this->getPerPage()));
    }

    /**
     * 用户信息邀请统计
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function showStat(Request $request, User $user): JsonResource
    {
        $user->loadMissing([
            'wallet:user_id,point,invite_amount',
            'parent:id,first_name,last_name',
        ]);
        // 订单成交次数
        $user->order_count = $user->orders()->count();
        // 订单成交金额
        $user->order_amount = $user->orders()->sum('total');
        // 平均没单成交金额
        $user->average_order_amount = $user->order_count > 0 ? $user->order_amount / $user->order_count : 0;
        // 专属粉丝数量
        $user->exclusive_fans_count = $user->exclusiveInvitedUsers()->count();
        // 普通粉丝数
        $user->ordinary_fans_count = $user->ordinaryInvitedUsers()->count();
        // 已提现的金额
        $user->withdraw_amount = $user->inviteAmountWithdraws()->sum('amount');
        // 预估奖励总金额
        $user->estimated_reward_amount = $user->inviteRewards()->where('is_cashback', false)->where('is_effective', true)->sum('reward_amount');
        // 无效奖励次数
        $user->invalid_reward_count = $user->inviteRewards()->where('is_effective', false)->count();
        // 生成邀请码
        $user->invite_code = hashids()->encode($user->id);

        return JsonResource::make($user);
    }

    /**
     * 提现明细
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function withdraw(Request $request, User $user)
    {
        $query = QueryBuilder::for(InviteAmountWithdraw::class)
            ->allowedFilters([
                AllowedFilter::callback('start_at', function ($query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                    $query->where('created_at', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'created_at'
            ])
            ->defaultSort('-created_at')
            ->where('user_id', $user->id)
            ->where('status', InviteAmountWithdrawStatusEnum::Passed);

        return $query->paginate($request->get('per_page'));
    }

    /**
     * 分裂汇总
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return
     */
    public function reward(Request $request, User $user)
    {
        // 对当前用户的allInvitedUsers进行筛选
        $query = QueryBuilder::for($user->allInvitedUsers())
            ->allowedFilters([
                AllowedFilter::callback('start_at', function ($query, $value) {
                    $query->where('users.created_at', '>=', $value);
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                    $query->where('users.created_at', '<=', $value);
                }),
                AllowedFilter::partial('email')
            ])
            // 分享规则
            ->with([
                'sharingRule:id,title'
            ])
            // 创造的订单总金额
            ->withSum([
                'orderRewards as orders_total' => function ($query) use ($user) {
                    $query->where('reward_user_id', $user->id); // 确保reward_user_id是当前用户
                }
            ], 'order_total')
            // 创造的预估奖励金额
            ->withSum([
                'orderRewards as estimated_commission' => function ($query) use ($user) {
                    $query->where('is_effective', true)
                        ->where('is_cashback', false)
                        ->where('reward_user_id', $user->id)
                        ->whereIn('reward_type', [RewardTypeEnum::FixedAmount, RewardTypeEnum::Percent]);
                }], 'reward_amount')
            // 创造的实际奖励金额
            ->withSum([
                'orderRewards as actual_commission' => function ($query) use ($user) {
                    $query->where('is_effective', true)
                        ->where('is_cashback', true)
                        ->where('reward_user_id', $user->id)
                        ->whereIn('reward_type', [RewardTypeEnum::FixedAmount, RewardTypeEnum::Percent]);
                }], 'reward_amount')
            // 创造的优惠券数量
            ->withCount([
                'orderRewards as coupon_count' => function ($query) use ($user) {
                    $query->where('is_effective', true)
                    ->where('reward_user_id', $user->id)
                    ->where('reward_type', RewardTypeEnum::Coupon);
                },
            ]);

        return $query->paginate($request->get('per_page'));
    }

    // 粉丝订单明细
    public function rewardDetail(Request $request, User $user, User $child)
    {
        $query = QueryBuilder::for(UserInviteReward::class)
            ->allowedFilters([
                AllowedFilter::callback('start_at', function ($query, $value) {
                    $query->where('order_created_at', '>=', $value);
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                    $query->where('order_created_at', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'order_created_at'
            ])
            ->where('reward_user_id', $user->id)
            ->where('order_user_id', $child->id)
            ->with([
                'rule:id,title',
                'orderUser:id,email',
            ]);

        return $query->paginate($request->get('per_page'));
    }
}
