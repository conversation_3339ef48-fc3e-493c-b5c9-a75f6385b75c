<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('associated_collections', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('collection_id')->index()->comment('分类ID');
            $table->bigInteger('associated_id')->index()->comment('关联分类ID');
            $table->unique(['collection_id', 'associated_id']);
            $table->engine('InnoDB');
            $table->comment('推荐分类关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('associated_collections');
    }
};
