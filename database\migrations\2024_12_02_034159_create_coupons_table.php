<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('优惠券名称');
            $table->string('desc')->default("")->comment('优惠券描述');
            $table->boolean('enabled')->default(false)->comment('启用状态');
            $table->string('code')->nullable()->comment('主动领取code');
            $table->tinyInteger('effective_date_type')->default(1)->comment('有效时间类型：1-周期范围，2-发放天数内');
            $table->bigInteger('effective_days')->nullable()->comment('有效时间(秒， 从发放开始)');
            $table->dateTime('effective_start_at')->nullable()->comment('有效期（固定开始时间）');
            $table->dateTime('effective_end_at')->nullable()->comment('有效期（固定过期时间）');
            $table->integer('total_count')->nullable()->comment('总发放数量（null 无限制）');
            $table->integer('user_count')->nullable()->comment('用户可领（用）数量 null无限制');
            $table->boolean('price_type')->default(1)->comment('折扣价格类型：1售价 2原价');
            $table->boolean('is_global')->default(1)->comment('全局优惠券(购物车优惠券)');
            $table->tinyInteger('type')->comment('折扣券类别');
            $table->json('rules')->comment('优惠券使用规则');
            // 新增标签
            $table->boolean('show_discount_tag')->default(false)->comment('是否显示折扣标签');
            $table->json('discount_tag_config')->nullable()->comment('折扣标签配置(JSON格式)');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
