<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Attribute;
use App\Models\AttributeValue;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class AttributeController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::AttributesUpdate)->except(['store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::AttributesIndex, Permissions::AttributesUpdate)->only(['index', 'show']);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Attribute $attribute
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function store(Request $request, Attribute $attribute): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:32']
        ]);
        DB::beginTransaction();
        try {
            // 验证唯一
            if (Attribute::query()->where('name', Arr::get($validated, 'name'))->exists()) {
                throw new DataException('attribute name already exists');
            }

            // 新增属性
            $attribute->fill($validated)->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($attribute);
    }

    /**
     * 修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Attribute $attribute
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function update(Request $request, Attribute $attribute): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:32']
        ]);

        DB::beginTransaction();
        try {
            // 验证唯一
            if (Attribute::query()->where('name', Arr::get($validated, 'name'))
            ->where('id', '<>', $attribute->id)
            ->exists()) {
                throw new DataException('attribute name already exists');
            }

            // 更新属性
            $attribute->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($attribute);
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return JsonResponse|mixed
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Attribute::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            // 删除属性
            Attribute::query()->whereIn('id', $ids)->delete();

            // 删除属性值
            AttributeValue::query()->whereIn('attribute_id', $ids)->delete();

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    /**
     * 列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {

        $attributes = QueryBuilder::for(Attribute::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('name'),
            ])
            ->defaultSort('created_at')
            ->allowedSorts('-created_at');
        $res = $attributes->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 详情
     * @param \App\Models\Attribute $attribute
     * @return JsonResource
     */
    public function show(Attribute $attribute): JsonResource
    {
        return JsonResource::make($attribute);
    }

    /**
     * 选项数据
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Attribute::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }
}
