<?php
/**
 * Author: raku <<EMAIL>>
 * Date: 2023/6/25
 */

namespace App\Apis\Bing;

use App\Traits\SingletonTrait;
use GuzzleHttp\Middleware;
use Illuminate\Http\Client\Factory;
use Illuminate\Http\Client\PendingRequest;
use Psr\Http\Message\RequestInterface;


class BingAddressClient extends PendingRequest
{
    use SingletonTrait;

    protected $baseUrl = 'https://api.radar.io';

    public function __construct(Factory $factory = null)
    {
        parent::__construct($factory);
        $this->timeout(30);
        // 添加Authorization header中间件
        $this->withMiddleware(Middleware::mapRequest(function (RequestInterface $request) {
            $apiKey = env('BING_SEARCH_ADDRESS_KEY');
            return $request->withHeader('Authorization', $apiKey);
        }));

    }

}
