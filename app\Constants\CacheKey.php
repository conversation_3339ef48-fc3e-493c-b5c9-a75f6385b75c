<?php

namespace App\Constants;

enum CacheKey: string
{
    case AuthLoginEmailErrorTimes = 'auth:login-error-times:email_%s';
    case PaymentPaypalAccessToken = 'payment:paypal-access-token';
    case PaymentAirwallexAccessToken = 'payment:airwallex-access-token';
    case SysConfigKey = 'sys-config:key_%s';
    case SysConfigAll = 'sys-config:all';
    case HelpFaqList = 'help-faq-list';
    case FooterList = 'footer-list';
    case DictEnums = 'dict-enums';
    case CurrencyDefault = 'currency-default';
    case ExternalClientKey = 'external-client:key:%s';
    case ProductSearchSuggestions = 'product:search-suggestions:key:%s';
    case ProductSearchList = 'product:search-list:key:%s';
    case ProductAttributes = 'product:attributes';
    case ProductInfoMatchList = 'product:info-match-list:key:%s';
    case PromotionBuilderList = 'promotion:builder-list:key:%s';
    case BannerBuilderList = 'banner:builder-list:key:%s';
    case CollectionSearchList = 'collection:search-list:key:%s';
    case Productnfo = 'product:info:key:%s';


    public function getKey(...$values): string
    {
        return sprintf($this->value, ...$values);
    }
}
