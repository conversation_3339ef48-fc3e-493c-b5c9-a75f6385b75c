<?php
return [

    'airwallex' => [
        'client_id' => env('PAYMENT_AIRWALLEX_CLIENT_ID', ''),
        'api_key' => env('PAYMENT_AIRWALLEX_API_KEY', ''),
        'sandbox' => env('PAYMENT_AIRWALLEX_SANDBOX', true),
        '3ds_callback_url' => env('PAYMENT_AIRWALLEX_3DS_CALLBACK_URL', ''),
        'error_page_url' => env('PAYMENT_AIRWALLEX_ERROR_PAGE_URL', ''),
        'success_page_url' => env('PAYMENT_AIRWALLEX_SUCCESS_PAGE_URL', ''),
    ],

    'paypal' => [
        'mode' => env('PAYPAL_MODE', 'sandbox'), // sandbox or live
        'sandbox' => [
            'client_id' => env('PAYPAL_SANDBOX_CLIENT_ID', ''),
            'client_secret' => env('PAYPAL_SANDBOX_CLIENT_SECRET', ''),
            'app_id' => env('PAYPAL_SANDBOX_APP_ID', ''),
        ],

        'live' => [
            'client_id' => env('PAYPAL_LIVE_CLIENT_ID', ''),
            'client_secret' => env('PAYPAL_LIVE_CLIENT_SECRET', ''),
            'app_id' => env('PAYPAL_LIVE_APP_ID', ''),
        ],
        'payment_action' => env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
        'currency' => env('PAYPAL_CURRENCY', 'USD'),
        'notify_url' => env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application.
        'locale' => env('PAYPAL_LOCALE', 'en_US'), // force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
        'validate_ssl' => env('PAYPAL_VALIDATE_SSL', false), // Validate SSL when creating api client.
    ]
];
