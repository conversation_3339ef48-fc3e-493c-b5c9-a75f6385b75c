<?php

namespace App\Apis\Google;

use Illuminate\Support\Facades\Log;

class GoogleRecaptchaService
{
    public static function siteVerify(string $response)
    {
        $param = [
            'secret' => env('GOOGLE_CAPTCHA_BACKEND_KEY'),
            'response' => trim($response),
        ];
        try {
            $response = GoogleClient::getInstance()
                ->asForm()
                ->post('/recaptcha/api/siteverify', $param);
            return $response->json('success');
        } catch (\Throwable $e) {
            Log::error('GoogleService siteVerify' . $e->getMessage());
        }
        return false;
    }
}
