<?php

namespace App\Models\Enums\Discount;

use Illuminate\Support\Arr;

enum DiscountEffectiveDateTypeEnum: int
{
    case  Cycle = 1;
    case  Day = 2;

    public function desc(): string
    {

        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Cycle->value => '周期范围',
            self::Day->value => '发放天数内',
        ];
    }

}
