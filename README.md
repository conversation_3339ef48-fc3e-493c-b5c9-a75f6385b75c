# 内衣商城

## 项目环境
- php 8.1.x
## 项目搭建
1. 拉取代码到本地
2. 项目文件夹内执行 `composer install`

## 首次拉取项目
1. 安装后台 `php artisan admin:install`
2. 运行数据库(独立数据库) `php artisan migrate`
3. 配置 `env` `FILESYSTEM_DISK=public` 图片软链接 `php artisan storage:link`

## 目录结构
- 按照[Laravel 9.x目录接口](https://learnku.com/docs/laravel/9.x/structure/12202)规范定义文件 其他补充如下
- `app/Apis/*`  定义所有与外部api交互内容
- `app/Constants` 定义所有常量
- `app/Traits` 定义所有特性模型
- `app/Models/Enums` 定义所有 **模型内**枚举属性
- `app/Services` 服务类， 处理复杂业务逻辑


## 开发规范
- 路由使用显式申明在 `routes/xxx.php` 中, 并且遵循 `restful API`规范

    - 路由不应该包含动词, 非要使用动词使用 `POST resources/_动词` 方式
    - 路由定义应该直接完整路由(方便检索),尽量避免嵌套式(group)

- `Controller` 中劲量统一返回 `Resource Response`, 特殊接口(无响应参数)情况除外

- 项目列表接口 筛选条件以 `filter`为前缀 例 `{filter:{name:"张三"}}` 为筛选姓名=张三

- **项目迁移**, 所有表结构变更均以`migrate` 为准， 调整数据库结构应该使用迁移文件，严格按照[文档](https://learnku.com/docs/laravel/9.x/migrations/12248)执行

- `Logic` 复杂代码部分， 应该封装在 `Service` 中， 参考`UserService` ， 不用过多定义 ， 一个模块一个`service`即可


## 全局
- 返回`json`数据的`code`会被重写,如果强行需要,请额外包装一层,底层`code`为与前端约定状态码, 全局返回`200`段,报错以为 `code` 为准

