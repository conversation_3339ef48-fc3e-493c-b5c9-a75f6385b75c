<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->decimal('point', 10, 2)->comment("积分")->default(0);
            $table->decimal('invite_amount', 12, 4)->comment("邀请奖励金额")->default(0);
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('用户钱包');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallets');
    }
};
