<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CouponGrantRecord extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];


    /**
     * 规则
     * @return BelongsTo
     */
    public function couponGrantRule(): BelongsTo
    {
        return $this->belongsTo(CouponGrantRule::class);
    }

    /**
     * 发放来源
     * @return MorphTo
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

}
