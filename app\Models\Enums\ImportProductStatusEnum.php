<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum ImportProductStatusEnum: int
{
    case PendingProcessing= 0;
    case ProcessingInProgress = 1;
    case ProcessingCompleted = 2;
    case ProcessingFailed = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::PendingProcessing->value => '待处理',
            self::ProcessingInProgress->value => '处理中',
            self::ProcessingCompleted->value => '处理完成',
            self::ProcessingFailed->value => '处理失败',
        ];
    }

}
