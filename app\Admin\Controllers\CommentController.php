<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Constants\QueueKey;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Imports\CommentsImport;
use App\Jobs\Product\ProductCommentStatic;
use App\Models\Comment;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Enums\ImportCommentStatusEnum;
use App\Models\ImportComment;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Excel;
use App\Models\Attachment;
use App\Models\AttributeValue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Support\Str;

class CommentController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CommentsUpdate)->only(['store', 'patchUpdate', 'destroy', 'update', 'batchUpdate', 'import']);
        $this->hasPermissionOr(Permissions::CommentsUpdate, Permissions::CommentsIndex)->only(['index', 'show']);
    }

    /**
     * 获取列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Comment::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('spu', 'product.spu'),
                AllowedFilter::exact('status'),
                AllowedFilter::exact('enabled'),
                AllowedFilter::callback('is_top', function (Builder $builder, $value) {
                    if ($value) {
                        $builder->whereNotNull('top_at');
                    } else {
                        $builder->whereNull('top_at');
                    }
                }),
                AllowedFilter::partial('email'),
                AllowedFilter::callback('user_name', function (Builder $builder, $value) {
                    $builder->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'LIKE', "%{$value}%");
                }),
            ])
            ->with([
                'product:id,spu',
                'user:id,first_name,last_name,email',
                'order:id,no',
                'images:id,path,disk,module'
            ])
            ->allowedSorts(['id', 'grade'])
            ->defaultSort('-id');
        // 分页
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 展示信息
     * @param \App\Models\Comment $comment
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(Comment $comment): JsonResource
    {
        $comment->loadMissing([
            'product',
            'productVariant',
            'productVariant.colorAttribute',
            'productVariant.sizeAttribute',
            'user',
            'images:id,path,disk,module'
        ]);
        return JsonResource::make($comment);
    }

    /**
     * 新增
     * @param Request $request
     * @param Comment $comment
     * @return JsonResource
     * @throws DataException
     * @throws \Throwable
     */
    public function store(Request $request, Comment $comment): JsonResource
    {
        $validated = $request->validate([
            'first_name' => ['required', 'string'],
            'last_name' => ['required', 'string'],
            'content' => ['required', 'string'],
            'grade' => ['required', 'numeric', 'min:0', 'max:100'],
            'sku' => ['required', new Exists(Product::class, 'spu')],
            'color' => ['nullable', new Exists(AttributeValue::class, 'value')],
            'size' => ['nullable', new Exists(AttributeValue::class, 'value')],
            'enabled' => ['required', 'bool'],
            'status' => ['required', new Enum(CommentStatusEnum::class)],
            'is_top' => ['required', 'bool'],
            'images' => ['nullable', 'array'],
            'images.*' => ['required', new Exists(Attachment::class, 'id')],
        ]);
        try {
            // 根据 sku(spu) 获取 product_id
            $product = Product::where('spu', trim($validated['sku']))->first();
            if (!$product) {
                throw new DataException('Product not found');
            }
            $validated['product_id'] = $product->id;

            // 根据 sku(spu) 获取 product_variant_id
            if(!empty(trim($validated['color'])) || !empty(trim($validated['size']))){
                $sku = trim($validated['sku']);
                $color = trim($validated['color']);
                $size = trim($validated['size']);
                $spu = Str::upper(join('_', [$sku, $color, $size]));
                $product_variant_id = ProductVariant::where('sku', $spu)->value('id');
                if(!$product_variant_id){
                    throw new DataException('该变体不存在');
                }
                $validated['product_variant_id'] = $product_variant_id;
            }
            
            DB::beginTransaction();
            // 这里处理的不够优雅,先这样随便烂写一下
            $isTop = Arr::pull($validated, 'is_top');
            if ($isTop) {
                $validated['top_at'] = now();
            }
            $images = Arr::get($validated, 'images', []);
            if (!empty($images)) {
                $validated['has_images'] = true;
            }

            // 评论信息
            $comment->fill(Arr::except($validated, ['images', 'sku','color', 'size']))->save();
            // 评论图片
            if (is_array($images)) {
                if (count($images) > 5) {
                    throw new DataException('The number of comment images cannot exceed 5');
                }
                $comment->images()->sync($images);
            }

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($comment);
    }

    /**
     * 批量更新
     * @param Request $request
     * @return JsonResource
     */
    public function batchUpdate(Request $request): JsonResource
    {
        $validated = $request->validate([
            'enabled' => ['bool'],
            'status' => [new Enum(CommentStatusEnum::class)],
            'is_top' => ['bool'],
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Comment::class, 'id'))],
        ]);
        $isTop = Arr::pull($validated, 'is_top');
        if (!is_null($isTop)) {
            $validated['top_at'] = $isTop ? now() : null;
        }
        $ids = Arr::pull($validated, 'ids');
        // 商品更新评论静态数据
        $productIds = Comment::query()->whereIn('id', $ids)->pluck('product_id')->unique()->toArray();
        Comment::query()
            ->whereIn('id', $ids)
            ->update($validated);
        // 刷新商品评论
        ProductCommentStatic::dispatch($productIds)->onQueue(QueueKey::Product->value);
        return JsonResource::make([]);
    }

    /**
     * 更新-增量
     * @param Request $request
     * @param Comment $comment
     * @return JsonResource
     */
    public function patchUpdate(Request $request, Comment $comment): JsonResource
    {
        $validated = $request->validate([
            'grade' => ['numeric'],
            'enabled' => ['bool'],
            'status' => [new Enum(CommentStatusEnum::class)],
            'is_top' => ['bool'],
        ]);
        $isTop = Arr::pull($validated, 'is_top');
        if ($isTop && !$comment->top_at) {
            $validated['top_at'] = $comment->top_at ?: now();
        }
        if (!is_null($isTop) && !$isTop) {
            $validated['top_at'] = null;
        }

        $comment->update($validated);
        return JsonResource::make($comment);
    }

    public function update(Request $request, Comment $comment): JsonResource
    {
        $validated = $request->validate([
            'first_name' => ['required', 'string'],
            'last_name' => ['required', 'string'],
            'content' => ['required', 'string'],
            'grade' => ['required', 'numeric', 'min:0', 'max:100'],
            'sku' => ['required', new Exists(Product::class, 'spu')],
            'color' => ['nullable', new Exists(AttributeValue::class, 'value')],
            'size' => ['nullable', new Exists(AttributeValue::class, 'value')],
            'enabled' => ['required', 'bool'],
            'status' => ['required', new Enum(CommentStatusEnum::class)],
            'is_top' => ['required', 'bool'],
            'images' => ['nullable', 'array'],
            'images.*' => ['required', new Exists(Attachment::class, 'id')],
        ]);

        try {
            DB::beginTransaction();
            $isTop = Arr::pull($validated, 'is_top');
            if ($isTop && !$comment->top_at) {
                $validated['top_at'] = $comment->top_at ?: now();
            }
            if (!$isTop) {
                $validated['top_at'] = null;
            }
            // 图片
            $images = Arr::get($validated, 'images', []);
            if (!empty($images)) {
                $validated['has_images'] = true;
            }

            // 根据 sku(spu) 获取 product_id
            $product = Product::where('spu', trim($validated['sku']))->first();
            if (!$product) {
                throw new DataException('Product not found');
            }
            $validated['product_id'] = $product->id;

            // 根据 sku(spu) 获取 product_variant_id
            if(!empty(trim($validated['color'])) || !empty(trim($validated['size']))){
                $sku = trim($validated['sku']);
                $color = trim($validated['color']);
                $size = trim($validated['size']);
                $spu = Str::upper(join('_', [$sku, $color, $size]));
                $product_variant_id = ProductVariant::where('sku', $spu)->value('id');
                if(!$product_variant_id){
                    throw new DataException('该变体不存在');
                }
                $validated['product_variant_id'] = $product_variant_id;
            }
            

            // 更新评论信息
            $comment->update(Arr::except($validated, ['images', 'sku','color', 'size']));

            // 评论图片
            if (is_array($images)) {
                if (count($images) > 5) {
                    throw new DataException('The number of comment images cannot exceed 5');
                }
                $comment->images()->sync($images);
            }
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($comment);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Comment::class, 'id'))],
        ]);
        $ids = Arr::pull($validated, 'ids');
        // 删除图片
        // CommentImage::query()->whereIn('comment_id', $ids)->delete();
        $productIds = Comment::query()->whereIn('id', $ids)->pluck('product_id')->unique()->toArray();
        // 删除评论
        Comment::query()->whereIn('id', $ids)->update(['deleted_at' => now()]);
        // 刷新商品评论
        ProductCommentStatic::dispatch($productIds)->onQueue(QueueKey::Product->value);

        return response()->json([]);
    }

    /**
     * 导入
     * @param \Illuminate\Http\Request $request
     * @return mixed|JsonResponse
     */
    public function import(Request $request)
    {
        $validated = $request->validate([
            'file' => ['required', 'file', 'mimes:xlsx,xls,csv'],
        ]);
        $file = Arr::get($validated, 'file');
        // 获取文件名
        $extension = $file->getClientOriginalExtension();
        // 本地目的文件夹
        $directory = 'storage' . DIRECTORY_SEPARATOR . 'import' . DIRECTORY_SEPARATOR . 'comments' . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR;
        // 使用 Laravel Storage 类创建文件夹（如果不存在）
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
        }
        // 生成文件路径
        $fileName = date('YmdHis') . '.' . $extension;
        $path = $directory . $fileName;
        // 将文件移动到指定目录中
        Storage::disk('public')->putFileAs($directory, $file, $fileName);

        // 创建导入对象
        $import = new ImportComment();
        $import->fill([
            'file' => $path,
            'admin_user_id' => Auth::id(),
        ])->save();

        return JsonResource::make($import);
    }

    /**
     * 导入商品列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function importIndex(Request $request)
    {
        $builder = QueryBuilder::for(ImportComment::class, $request)
            ->with([
                'adminUser:id,name'
            ])
            ->allowedFilters([
                AllowedFilter::callback('status', function (Builder $builder, $value) {
                    if ($value !== '') {
                        $builder->where('status', $value);
                    }
                }),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        // 分页
        $res = $builder->paginate($this->getPerPage());

        // 处理 status 数据
        $res->each(function ($item) {
            $item->status = ImportCommentStatusEnum::from($item->status)->desc();
        });

        return JsonResource::collection($res);
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed
     */
    public function importDestroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => ['required', 'integer', new Exists(ImportComment::class, 'id')],
        ]);
        $ids = Arr::get($validated, 'ids');
        ImportComment::query()->whereIn('id', $ids)->delete();
        return response()->json();
    }
}
