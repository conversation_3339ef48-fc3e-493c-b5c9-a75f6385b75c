<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invite_users', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('invited_user_id')->index()->comment('邀请用户ID');
            $table->tinyInteger('type')->comment('关系类型：1-专属粉丝，2-普通粉丝');
            $table->unique(['user_id', 'invited_user_id']);
            $table->comment('邀请用户表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invite_users');
    }
};
