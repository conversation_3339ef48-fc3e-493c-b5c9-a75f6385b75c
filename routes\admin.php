<?php

use App\Admin\Controllers\AdminUserController;
use App\Admin\Controllers\AttributeController;
use App\Admin\Controllers\AttributeValueController;
use App\Admin\Controllers\AuthController;
use App\Admin\Controllers\BannerController;
use App\Admin\Controllers\BrandController;
use App\Admin\Controllers\BroadcastColumnController;
use App\Admin\Controllers\CartController;
use App\Admin\Controllers\CategoryController;
use App\Admin\Controllers\CollectionController;
use App\Admin\Controllers\CommentController;
use App\Admin\Controllers\CouponController;
use App\Admin\Controllers\CouponGrantRuleController;
use App\Admin\Controllers\CurrencyController;
use App\Admin\Controllers\EmailController;
use App\Admin\Controllers\FooterController;
use App\Admin\Controllers\GroupController;
use App\Admin\Controllers\HelpFaqController;
use App\Admin\Controllers\InviteStatController;
use App\Admin\Controllers\MaterialController;
use App\Admin\Controllers\OrderController;
use App\Admin\Controllers\PaymentMethodController;
use App\Admin\Controllers\ProductController;
use App\Admin\Controllers\ProductVariantController;
use App\Admin\Controllers\PromotionController;
use App\Admin\Controllers\RoleController;
use App\Admin\Controllers\ShippingController;
use App\Admin\Controllers\StyleController;
use App\Admin\Controllers\SysConfigController;
use App\Admin\Controllers\UserController;
use App\Admin\Controllers\SharingRuleController;
use App\Admin\Controllers\BrandMasterProgramController;
use App\Admin\Controllers\ContactUsMessageController;
use App\Admin\Controllers\UserExperienceActivityController;
use App\Admin\Controllers\ActivityNavController;
use App\Admin\Controllers\ModeController;
use App\Admin\Controllers\CmsSettingController;
use App\Admin\Controllers\CmsArticleController;
use App\Admin\Controllers\CmsTagController;
use App\Admin\Controllers\InviteAmountWithdrawController;
use App\Admin\Controllers\CountryController;
use App\Admin\Controllers\MembershipController;
use App\Admin\Controllers\MembershipPageConfigController;
use App\Admin\Controllers\TaskPointConfigController;
use Illuminate\Support\Facades\Route;



Route::any('test', [AuthController::class, 'index']);
// admin-no-auth
Route::group([], function () {
    // auth
    Route::group([], function () {
        // 登录
        Route::post('auth/login', [AuthController::class, 'login']);
    });
    // 上传图片
    Route::post('upload', [UserController::class, 'upload']);
    // 上传分片视频
    Route::post('upload/chunk', [UserController::class, 'uploadChunk']);
    // 清理缓存
    Route::post('cache/_clear', [UserController::class, 'cacheClear']);
});


// need-auth
Route::group(['middleware' => ['auth:sanctum', 'admin.check']], function () {
    // 用户信息
    Route::get('admin-user', [AuthController::class, 'adminUser']);
    // 管理员管理
    Route::group([], function () {
        // 列表
        Route::post('admin-users/_search', [AdminUserController::class, 'index']);
        // 新增管理员
        Route::post('admin-users', [AdminUserController::class, 'store']);
        // 详情
        Route::get('admin-users/{user}', [AdminUserController::class, 'show']);
        // 修改
        Route::post('admin-users/{user}', [AdminUserController::class, 'update']);
        // 批量删除管理员
        Route::post('admin-users/_batch_destroy', [AdminUserController::class, 'batchDelete']);
        // 修改状态
        Route::put('admin-users/{user}/status', [AdminUserController::class, 'status']);
    });

    // 权限管理(角色)
    Route::group([], function () {
        // 列表
        Route::post('roles/_search', [RoleController::class, 'index']);
        // 新增角色
        Route::post('roles', [RoleController::class, 'store']);
        // 详情
        Route::get('roles/{role}', [RoleController::class, 'show']);
        // 修改
        Route::put('roles/{role}', [RoleController::class, 'update']);
        // 批量删除角色
        Route::post('roles/_batch_destroy', [RoleController::class, 'batchDelete']);
        // 权限列表
        Route::get('permissions', [RoleController::class, 'permissions']);
    });

    // 商品信息
    Route::group([], function () {
        // 导出
        Route::post('products/_export', [ProductController::class, 'export']);
        // 清理列表缓存
        Route::post('products/_refresh-cache', [ProductController::class, 'refreshCache']);
        // 选择
        Route::post('products/_select', [ProductController::class, 'select']);
        // 附带数据
        Route::get('products/attached_info', [ProductController::class, 'attachedInfo']);
        // 列表
        Route::post('products/_search', [ProductController::class, 'index']);
        // 不分页列表
        Route::post('products/_all', [ProductController::class, 'indexAll']);
        // options
        Route::post('products/_options', [ProductController::class, 'options']);
        // 新增
        Route::post('products', [ProductController::class, 'store']);
        // 导入
        Route::post('product/_import', [ProductController::class, 'import']);
        // 商品详情
        Route::get('products/{product}', [ProductController::class, 'show']);
        // 修改详情
        Route::put('products/{product}/detail', [ProductController::class, 'updateDetail']);
        // 修改SEO
        Route::put('products/{product}/seo', [ProductController::class, 'updateSeo']);
        // 部分修改
        Route::patch('products/{product}', [ProductController::class, 'patchUpdate']);
        // 删除
        Route::post('products/_batch_destroy', [ProductController::class, 'destroy']);
        // 同步erp
        Route::post('products/_sync_erp', [ProductController::class, 'syncErp']);
        // 产品颜色选择
        Route::get('products/{product}/colors/_options', [ProductController::class, 'colorsOptions']);
        // 产品颜色
        Route::post('products/{product}/colors/_search', [ProductController::class, 'colors']);
        // 产品颜色新增
        Route::post('products/{product}/colors', [ProductController::class, 'storeColors']);
        // 修改
        Route::put('products/colors/{color}', [ProductController::class, 'updateColors']);
        // 批量导入产品列表
        Route::post('products/_import/list', [ProductController::class, 'importIndex']);
        // 批量导入产品删除
        Route::post('products/_import/_batch_destroy', [ProductController::class, 'importDestroy']);
        // 产品推荐
        Route::get('products/{product}/recommend', [ProductController::class, 'recommend']);
        //新增推荐商品
        Route::post('products/{product}/recommend', [ProductController::class, 'storeRecommend']);
        //删除推荐商品
        Route::post('products/{product}/recommend/_batch_destroy', [ProductController::class, 'destroyRecommend']);
    });

    // 商品变体信息
    Route::group([], function () {
        // 新增变体
        Route::post('products/{product}/variants', [ProductVariantController::class, 'store']);
        // 列表
        Route::post('products/{product}/variants/_search', [ProductVariantController::class, 'index']);
        // 附带数据
        Route::get('variants/attached_info', [ProductVariantController::class, 'attachedInfo']);
        // 商品详情
        Route::get('variants/{variant}', [ProductVariantController::class, 'show']);
        // 修改详情
        Route::put('variants/{variant}', [ProductVariantController::class, 'update']);
        // 增量修改
        Route::patch('variants/{variant}', [ProductVariantController::class, 'patchUpdate']);
        // 批量更新同一产品的变体价格
        Route::post('products/{product}/variants/batch-update-price', [ProductVariantController::class, 'batchUpdateProductVariantsPrice']);
        // 删除
        Route::post('variants/_batch_destroy', [ProductVariantController::class, 'destroy']);
    });

    // 订单
    Route::group([], function () {
        // 列表统计
        Route::get('orders/stat', [OrderController::class, 'stat']);
        // 列表
        Route::post('orders/_search', [OrderController::class, 'index']);
        // 商品详情
        Route::get('orders/{order}', [OrderController::class, 'show']);
        // 部分修改 (发货、退款、等)
        Route::patch('orders/{order}', [OrderController::class, 'update']);
    });

    // 优惠券
    Route::group([], function () {
        // 优惠券管理
        Route::group([], function () {
            // 生成优惠劵码
            Route::get('coupons/code', [CouponController::class, 'code']);
            // 优惠券选项
            Route::get('coupons/_options', [CouponController::class, 'option']);
            // 列表
            Route::post('coupons/_search', [CouponController::class, 'index']);
            // 创建优惠券
            Route::post('coupons', [CouponController::class, 'store']);
            // 优惠券
            Route::get('coupons/{coupon}', [CouponController::class, 'show']);
            // 全量修改
            Route::put('coupons/{coupon}', [CouponController::class, 'update']);
            // 部分修改
            Route::patch('coupons/{coupon}', [CouponController::class, 'patchUpdate']);
            // 删除
            Route::post('coupons/_batch_destroy', [CouponController::class, 'destroy']);
        });

        // 优惠券发放管理
        Route::group([], function () {
            // 列表
            Route::post('coupon-grant-rules/_search', [CouponGrantRuleController::class, 'index']);
            // 创建优惠券
            Route::post('coupon-grant-rules', [CouponGrantRuleController::class, 'store']);
            // 优惠券
            Route::get('coupon-grant-rules/{rule}', [CouponGrantRuleController::class, 'show']);
            // 全量修改
            Route::put('coupon-grant-rules/{rule}', [CouponGrantRuleController::class, 'update']);
            // 增量修改
            Route::patch('coupon-grant-rules/{rule}', [CouponGrantRuleController::class, 'patchUpdate']);
            // 删除
            Route::post('coupon-grant-rules/_batch_destroy', [CouponGrantRuleController::class, 'destroy']);
        });
    });

    // 商品集合
    Route::group([], function () {
        // 选择
        Route::get('collections/_options', [CollectionController::class, 'options']);
        // 列表
        Route::post('collections/_search', [CollectionController::class, 'index']);
        // 树列表
        Route::get('collections/_tree', [CollectionController::class, 'treeIndex']);
        // 新增
        Route::post('collections', [CollectionController::class, 'store']);
        // 集合详情
        Route::get('collections/{collection}', [CollectionController::class, 'show']);
        // 部分修改
        Route::patch('collections/{collection}', [CollectionController::class, 'patchUpdate']);
        // 修改商品
        Route::post('collections/{collection}/products', [CollectionController::class, 'productUpdate']);
        // 删除分类
        Route::post('collections/_batch_destroy', [CollectionController::class, 'destroy']);
        // 修改分类状态
        Route::put('collections/{collection}/status', [CollectionController::class, 'status']);
    });

    // brands 品牌
    Route::group([], function () {
        // 列表
        Route::get('brands/_options', [BrandController::class, 'options']);
        // 获取列表
        Route::post('brands/_search', [BrandController::class, 'index']);
        // 创建
        Route::post('brands', [BrandController::class, 'store']);
        // 删除
        Route::post('brands/_batch_destroy', [BrandController::class, 'destroy']);
        // 修改
        Route::put('brands/{brand}', [BrandController::class, 'update']);
    });
    //brand_master_program 品牌大使计划
    Route::group([], function () {
        // 列表
        Route::get('brand_master_programs/_options', [BrandMasterProgramController::class, 'options']);
        // 获取列表
        Route::post('brand_master_programs/_search', [BrandMasterProgramController::class, 'index']);
        // 修改
        Route::put('brand_master_programs/{brand_master_program}', [BrandMasterProgramController::class, 'update']);
        // 详情
        Route::get('brand_master_programs/{brand_master_program}', [BrandMasterProgramController::class, 'show']);
        // 删除
        Route::post('brand_master_programs/_batch_destroy', [BrandMasterProgramController::class, 'destroy']);
    });
    //contact_us_message 联系我们
    Route::group([], function () {
        // 列表
        Route::get('contact_us_message/_options', [ContactUsMessageController::class, 'options']);
        // 获取列表
        Route::post('contact_us_message/_search', [ContactUsMessageController::class, 'index']);
        // 修改
        Route::put('contact_us_message/{contact_us_message}', [ContactUsMessageController::class, 'update']);
        // 详情
        Route::get('contact_us_message/{contact_us_message}', [ContactUsMessageController::class, 'show']);
        // 删除
        Route::post('contact_us_message/_batch_destroy', [ContactUsMessageController::class, 'destroy']);
    });
    //user_experience_activity 用户体验活动
    Route::group([], function () {
        //列表
        Route::get('user_experience_activities/_options', [UserExperienceActivityController::class, 'options']);
        // 获取列表
        Route::post('user_experience_activities/_search', [UserExperienceActivityController::class, 'index']);
        //添加
        Route::post('user_experience_activities', [UserExperienceActivityController::class, 'store']);
        //修改
        Route::put('user_experience_activities/{user_experience_activity}', [UserExperienceActivityController::class, 'update']);
        //详情
        Route::get('user_experience_activities/{user_experience_activity}', [UserExperienceActivityController::class, 'show']);
        //删除
        Route::post('user_experience_activities/_batch_destroy', [UserExperienceActivityController::class, 'destroy']);
        //上下架
        Route::put('user_experience_activities/{user_experience_activity}/status', [UserExperienceActivityController::class, 'status']);
    });
    //特价活动导航
    Route::group([], function () {
        //列表
        Route::get('activity-naves/_options', [ActivityNavController::class, 'options']);
        // 获取列表
        Route::post('activity-naves/_search', [ActivityNavController::class, 'index']);
        //添加
        Route::post('activity-naves', [ActivityNavController::class, 'store']);
        //修改
        Route::put('activity-naves/{activity_nav}', [ActivityNavController::class, 'update']);
        //详情
        Route::get('activity-naves/{activity_nav}', [ActivityNavController::class, 'show']);
        //删除
        Route::post('activity-naves/_batch_destroy', [ActivityNavController::class, 'destroy']);
        //启用禁用
        Route::put('activity-naves/{activity_nav}/status', [ActivityNavController::class, 'status']);
    });
    // category 品类
    Route::group([], function () {
        // 列表
        Route::get('categories/_options', [CategoryController::class, 'options']);
        // 获取列表
        Route::post('categories/_search', [CategoryController::class, 'index']);
        // 创建
        Route::post('categories', [CategoryController::class, 'store']);
        // 删除
        Route::post('categories/_batch_destroy', [CategoryController::class, 'destroy']);
        // 修改
        Route::put('categories/{category}', [CategoryController::class, 'update']);
    });

    // styles 款式
    Route::group([], function () {
        // 列表
        Route::get('styles/_options', [StyleController::class, 'options']);
        // 获取列表
        Route::post('styles/_search', [StyleController::class, 'index']);
        // 创建
        Route::post('styles', [StyleController::class, 'store']);
        // 删除
        Route::post('styles/_batch_destroy', [StyleController::class, 'destroy']);
        // 修改
        Route::put('styles/{style}', [StyleController::class, 'update']);
    });
    // materials 面料
    Route::group([], function () {
        // 列表
        Route::get('materials/_options', [MaterialController::class, 'options']);
        // 获取列表
        Route::post('materials/_search', [MaterialController::class, 'index']);
        // 创建
        Route::post('materials', [MaterialController::class, 'store']);
        // 删除
        Route::post('materials/_batch_destroy', [MaterialController::class, 'destroy']);
        // 修改
        Route::put('materials/{material}', [MaterialController::class, 'update']);
    });
    // 属性值
    Route::group([], function () {
        // 列表
        Route::post('attributes/{attribute}/values/_search', [AttributeValueController::class, 'index']);
        // 新增
        Route::post('values', [AttributeValueController::class, 'store']);
        // 修改
        Route::put('values/{value}', [AttributeValueController::class, 'update']);
        // 详情
        Route::get('values/{value}', [AttributeValueController::class, 'show']);
        // 删除
        Route::post('values/_batch_destroy', [AttributeValueController::class, 'destroy']);
    });
    // 属性
    Route::group([], function () {
        // 选择列表
        Route::get('attributes/_options', [AttributeController::class, 'options']);
        // 列表
        Route::post('attributes/_search', [AttributeController::class, 'index']);
        // 新增
        Route::post('attributes', [AttributeController::class, 'store']);
        // 修改
        Route::put('attributes/{attribute}', [AttributeController::class, 'update']);
        // 详情
        Route::get('attributes/{attribute}', [AttributeController::class, 'show']);
        // 删除
        Route::post('attributes/_batch_destroy', [AttributeController::class, 'destroy']);
    });
    // mode 风格
    Route::group([], function () {
        // 列表
        Route::get('modes/_options', [ModeController::class, 'options']);
        // 获取列表
        Route::post('modes/_search', [ModeController::class, 'index']);
        // 创建
        Route::post('modes', [ModeController::class, 'store']);
        // 删除
        Route::post('modes/_batch_destroy', [ModeController::class, 'destroy']);
        // 修改
        Route::put('modes/{material}', [ModeController::class, 'update']);
    });
    // Broadcast 广播栏
    Route::group([], function () {
        // 列表
        Route::post('broadcasts/_search', [BroadcastColumnController::class, 'index']);
        // 新增
        Route::post('broadcasts', [BroadcastColumnController::class, 'store']);
        // 修改
        Route::put('broadcasts/{broadcast}', [BroadcastColumnController::class, 'update']);
        // 删除
        Route::post('broadcasts/_batch_destroy', [BroadcastColumnController::class, 'destroy']);
        // 启用禁用
        Route::put('broadcasts/{broadcast}/status', [BroadcastColumnController::class, 'status']);
    });
    // Banner 设置
    Route::group([], function () {
        // 列表
        Route::post('banners/_search', [BannerController::class, 'index']);
        // 新增
        Route::post('banners', [BannerController::class, 'store']);
        // 修改
        Route::put('banners/{banner}', [BannerController::class, 'update']);
        // 详情
        Route::get('banners/{banner}', [BannerController::class, 'show']);
        // 删除
        Route::post('banners/_batch_destroy', [BannerController::class, 'destroy']);
        // 启用禁用
        Route::put('banners/{banner}/status', [BannerController::class, 'status']);
    });

    // footer 底部导航
    Route::group([], function () {
        // 列表
        Route::post('footers/_search', [FooterController::class, 'index']);
        // 详情
        Route::get('footers/{footer}', [FooterController::class, 'show']);
        // 创建
        Route::post('footers', [FooterController::class, 'store']);
        // 修改
        Route::put('footers/{footer}', [FooterController::class, 'update']);
        // 删除
        Route::post('footers/_batch_destroy', [FooterController::class, 'destroy']);
    });
    // 语言配置-货币
    Route::group([], function () {
        // 列表
        Route::post('currencies/_search', [CurrencyController::class, 'index']);
        // 详情
        Route::get('currencies/{currency}', [CurrencyController::class, 'show']);
        // 创建
        Route::post('currencies', [CurrencyController::class, 'store']);
        // 修改
        Route::put('currencies/{currency}', [CurrencyController::class, 'update']);
        // 删除
        Route::post('currencies/_batch_destroy', [CurrencyController::class, 'destroy']);
    });

    // promotions 广告推广
    Route::group([], function () {
        // 列表
        Route::post('promotions/_search', [PromotionController::class, 'index']);
        // 详情
        Route::get('promotions/{promotion}', [PromotionController::class, 'show']);
        // 创建
        Route::post('promotions', [PromotionController::class, 'store']);
        // 修改
        Route::put('promotions/{promotion}', [PromotionController::class, 'update']);
        // 删除
        Route::post('promotions/_batch_destroy', [PromotionController::class, 'destroy']);
        // 启用禁用
        Route::put('promotions/{promotion}/status', [PromotionController::class, 'status']);
    });

    // 评论
    Route::group([], function () {
        // 列表
        Route::post('comments/_search', [CommentController::class, 'index']);
        // 详情
        Route::get('comments/{comment}', [CommentController::class, 'show']);
        // 创建
        Route::post('comments', [CommentController::class, 'store']);
        // 修改
        Route::put('comments/{comment}', [CommentController::class, 'update']);
        // 更新/审核/置顶/状态
        Route::patch('comments/{comment}', [CommentController::class, 'patchUpdate']);
        // 删除
        Route::post('comments/_batch_destroy', [CommentController::class, 'destroy']);
        // 批量 审核/置顶/状态
        Route::post('comment/_batch_update', [CommentController::class, 'batchUpdate']);
        // 导入
        Route::post('comment/_import', [CommentController::class, 'import']);
        // 导入列表
        Route::post('comment/_import/list', [CommentController::class, 'importIndex']);
        // 导入删除
        Route::post('comment/_import/_batch_destroy', [CommentController::class, 'importDestroy']);
    });

    // 系统配置
    Route::group([], function () {
        // 获取
        Route::get('sys-configs/{key}', [SysConfigController::class, 'show']);
        // 更新
        Route::put('sys-configs/{key}', [SysConfigController::class, 'update']);
    });


    // 帮助常见问题解答
    Route::group([], function () {
        // 列表
        Route::post('help-faqs/_search', [HelpFaqController::class, 'index']);
        // 详情
        Route::get('help-faqs/{helpFaq}', [HelpFaqController::class, 'show']);
        // 更新
        Route::put('help-faqs/{helpFaq}', [HelpFaqController::class, 'update']);
        // 新增
        Route::post('help-faqs', [HelpFaqController::class, 'store']);
        // 删除
        Route::post('help-faqs/_batch_destroy', [HelpFaqController::class, 'destroy']);
    });


    // 邮件/订阅管理
    Route::group([], function () {
        // 模板
        Route::group([], function () {
            // 获取
            Route::post('email/templates/_search', [EmailController::class, 'templatesIndex']);
            // public 列表
            Route::post('email/templates/_options', [EmailController::class, 'templatesOptions']);
            // 详情
            Route::get('email/templates/{template}', [EmailController::class, 'templatesShow']);
            // 模板数据
            Route::get('email/template/base-data', [EmailController::class, 'templateBaseData']);
            // 新增
            Route::post('email/templates', [EmailController::class, 'templatesStore']);
            // 修改
            Route::put('email/templates/{template}', [EmailController::class, 'templatesUpdate']);
            // 删除
            Route::delete('email/templates/{template}', [EmailController::class, 'templatesDestroy']);
            // 增量修改
            Route::patch('email/templates/{template}', [EmailController::class, 'templatesPatchUpdate']);
        });
        // 邮件发送管理
        Route::group([], function () {
            // 获取
            Route::post('email/rules/_search', [EmailController::class, 'ruleIndex']);
            // 新增
            Route::post('email/rules', [EmailController::class, 'ruleStore']);
            // 发消息
            Route::post('email/rules/{rule}/send', [EmailController::class, 'ruleSend']);
            // 详情
            Route::get('email/rules/{rule}', [EmailController::class, 'ruleShow']);
            // 修改
            Route::put('email/rules/{rule}', [EmailController::class, 'ruleUpdate']);
            // 删除
            Route::delete('email/rules/{rule}', [EmailController::class, 'ruleDestroy']);
            // 增量修改
            Route::patch('email/rules/{rule}', [EmailController::class, 'rulePatchUpdate']);
        });
    });

    // 用户分裂规则
    Route::group([], function () {
        // 选择
        Route::get('sharing-rules/_options', [SharingRuleController::class, 'options']);
        // 列表
        Route::post('sharing-rules/_search', [SharingRuleController::class, 'index']);
        // 详情
        Route::get('sharing-rules/{rule}', [SharingRuleController::class, 'show']);
        // 创建
        Route::post('sharing-rules', [SharingRuleController::class, 'store']);
        // 更新
        Route::put('sharing-rules/{rule}', [SharingRuleController::class, 'update']);
        // 删除
        Route::post('sharing-rules/_batch_destroy', [SharingRuleController::class, 'destroy']);
        // 部分更新
        Route::patch('sharing-rules/{rule}', [SharingRuleController::class, 'patchUpdate']);
    });

    // 运输方式
    Route::group([], function () {
        // 选择项
        Route::get('shippings/_options', [ShippingController::class, 'options']);
        // 列表
        Route::post('shippings/_search', [ShippingController::class, 'index']);
        // 详情
        Route::get('shippings/{shipping}', [ShippingController::class, 'show']);
        // 更新
        Route::put('shippings/{shipping}', [ShippingController::class, 'update']);
        // 更新状态
        Route::put('shippings/{shipping}/default_status', [ShippingController::class, 'default_status']);
        // 运费导入
        Route::post('shipping-fee/_import', [ShippingController::class, 'import']);
    });

    // 支付方式
    Route::group([], function () {
        // 列表
        Route::post('payment-methods/_search', [PaymentMethodController::class, 'index']);
        // 详情
        Route::get('payment-methods/{method}', [PaymentMethodController::class, 'show']);
        // 新增
        Route::post('payment-methods', [PaymentMethodController::class, 'store']);
        // 修改
        Route::put('payment-methods/{method}', [PaymentMethodController::class, 'update']);
        // 删除
        Route::post('payment-methods/_batch_destroy', [PaymentMethodController::class, 'destroy']);
    });

    // 客户
    Route::group([], function () {
        // 选择
        Route::get('users/_options', [UserController::class, 'options']);
        // 列表
        Route::post('users/_search', [UserController::class, 'index']);
        // 详情
        Route::get('users/{user}', [UserController::class, 'show']);
        // 用户订单
        Route::get('users/{user}/order', [UserController::class, 'order']);
        // 购物车信息
        Route::get('users/{user}/cart', [UserController::class, 'cart']);
        // 购买过的产品
        Route::get('users/{user}/bought', [UserController::class, 'bought']);
        // 地址
        Route::get('users/{user}/address', [UserController::class, 'address']);
        // 优惠卷
        Route::get('users/{user}/coupon', [UserController::class, 'coupon']);
        // 用户积分记录
        Route::get('users/{user}/point-records', [UserController::class, 'pointRecords']);
        // 用户钱包记录
        Route::get('users/{user}/wallet-records', [UserController::class, 'walletRecords']);
        // 修改
        Route::put('users/{user}', [UserController::class, 'update']);
        // 增量修改
        Route::patch('users/{user}', [UserController::class, 'patchUpdate']);
        // 导出
        Route::post('users/_export', [UserController::class, 'export']);
        // 删除
        Route::post('users/_batch_destroy', [UserController::class, 'destroy']);
        // 批量移出拉黑
        Route::post('users/_remove_black_list', [UserController::class, 'removeBlackList']);
        // 修改用户状态
        Route::patch('/users/{user}/status', [UserController::class, 'changeStatus']);
    });

    // 购物车
    Route::group([], function () {
        // 列表
        Route::post('carts/_search', [CartController::class, 'index']);
        //统计
        Route::get('carts/stat', [CartController::class, 'stat']);
        // 详情
        Route::get('carts/{cart}', [CartController::class, 'show']);
    });

    Route::group([], function () {
        // 统计
        Route::post('invite_stat/_stat', [InviteStatController::class, 'stat']);
        // 列表
        Route::post('invite_stat/_search', [InviteStatController::class, 'index']);
        // 用户分裂统计(详情页的用户总统计数据)
        Route::get('invite_stat/{user}', [InviteStatController::class, 'showStat']);
        // 提现明细列表(详情页的用户提现列表)
        Route::post('invite_stat/{user}/withdraw/_search', [InviteStatController::class, 'withdraw']);
        // 分裂汇总列表(详情页的用户分裂奖励列表)
        Route::post('invite_stat/{user}/reward/_search', [InviteStatController::class, 'reward']);
        // 分裂明细列表(详情页的用户分裂奖励明细列表)
        Route::post('invite_stat/{user}/reward/{child}/detail/_search', [InviteStatController::class, 'rewardDetail']);
    });

    //cms分类配置
    Route::group([], function () {
        // 列表
        Route::post('cms_setting/_search', [CmsSettingController::class, 'index']);
        // 全列表
        Route::post('cms_setting/_all', [CmsSettingController::class, 'all']);
        //新增
        Route::post('cms_setting', [CmsSettingController::class, 'store']);
        // 详情
        Route::get('cms_setting/{cms_setting}', [CmsSettingController::class, 'show']);
        // 修改
        Route::put('cms_setting/{cms_setting}', [CmsSettingController::class, 'update']);
        // 删除
        Route::post('cms_setting/_batch_destroy', [CmsSettingController::class, 'destroy']);
        // 上下架
        Route::post('cms_setting/{cms_setting}/status', [CmsSettingController::class, 'status']);
        // 排序单独修改
        Route::post('cms_setting/{cms_setting}/sort', [CmsSettingController::class, 'sort']);
        // 获取指定分类下的文章
        Route::post('cms_setting/{cms_setting}/article/_search', [CmsSettingController::class, 'article']);
        // 获取指定分类下的文章
        Route::post('cms_setting/{cms_setting}/article/_all', [CmsSettingController::class, 'articleAll']);

        //列表
        Route::post('cms_article/_search', [CmsArticleController::class, 'index']);
        // 文章添加
        Route::post('cms_article', [CmsArticleController::class, 'store']);
        // 文章详情
        Route::get('cms_article/{cms_article}', [CmsArticleController::class, 'show']);
        // 文章修改
        Route::put('cms_article/{cms_article}', [CmsArticleController::class, 'update']);
        // 文章删除
        Route::post('cms_article/_batch_destroy', [CmsArticleController::class, 'destroy']);
        // 文章上架
        Route::post('cms_article/{cms_article}/status', [CmsArticleController::class, 'status']);
        // 文章下架
        Route::post('cms_article/{cms_article}/down', [CmsArticleController::class, 'down']);
        // 排序
        Route::post('cms_article/{cms_article}/sort', [CmsArticleController::class, 'sort']);

        // 标签
        Route::post('cms_tag/_search', [CmsTagController::class, 'index']);
        // 新增
        Route::post('cms_tag', [CmsTagController::class, 'store']);
        // 修改
        Route::put('cms_tag/{cms_tag}', [CmsTagController::class, 'update']);
        // 删除
        Route::post('cms_tag/_batch_destroy', [CmsTagController::class, 'destroy']);
    });

    // 用户组管理
    Route::group([], function () {
        // 选择
        Route::get('group/_options', [GroupController::class, 'options']);
        // 列表
        Route::post('group/_search', [GroupController::class, 'index']);
        // 新增
        Route::post('group', [GroupController::class, 'store']);
        // 详情
        Route::get('group/{group}', [GroupController::class, 'show']);
        // 修改
        Route::put('group/{group}', [GroupController::class, 'update']);
        // 删除
        Route::post('group/_batch_destroy', [GroupController::class, 'destroy']);
        // 邮箱审核
        Route::post('group/check_emails', [GroupController::class, 'checkEmails']);
    });

    // 提现记录
    Route::group([], function () {
        // 列表
        Route::post('withdraws/_search', [InviteAmountWithdrawController::class, 'index']);
        //修改提现状态
        Route::patch('withdraws/{withdraw}/status', [InviteAmountWithdrawController::class, 'updateStatus']);
        // 导出提现记录为CSV
        Route::post('withdraws/_export', [InviteAmountWithdrawController::class, 'export']);
    });

    // 国家管理
    Route::group([], function () {
        // 列表
        Route::post('countries/_search', [CountryController::class, 'index']);
        // 获取国家货币列表
        Route::post('countries/currencies/_search', [CountryController::class, 'currencies']);
        // 设置国家货币
        Route::post('countries/currency', [CountryController::class, 'setCurrency']);
        // 删除国家货币
        Route::post('countries/currency/_batch_destroy', [CountryController::class, 'destroy']);
        Route::patch('countries/currency/{countryCurrency}', [CountryController::class, 'patchUpdate']);

        Route::patch('countries/currency/{countryCurrency}', [CountryController::class, 'patchUpdate']);
    });

    // 会员等级管理
    Route::group(['prefix' => 'membership'], function () {
        // 会员等级管理
        Route::post('levels/_search', [MembershipController::class, 'levels']);
        Route::post('levels', [MembershipController::class, 'createLevel']);
        Route::put('levels/{level}', [MembershipController::class, 'updateLevel']);
        Route::delete('levels/{level}', [MembershipController::class, 'deleteLevel']);

        // 用户会员信息管理
        Route::post('user-memberships/_search', [MembershipController::class, 'userMemberships']);
        Route::get('users/{user}/membership', [MembershipController::class, 'userMembershipDetail']);
        Route::post('users/{user}/adjust-level', [MembershipController::class, 'adjustUserLevel']);

        // 积分记录管理
        Route::post('point-records/_search', [MembershipController::class, 'pointRecords']);
        Route::post('grant-points', [MembershipController::class, 'grantPoints']);

        // 统计信息
        Route::get('level-stats', [MembershipController::class, 'levelStats']);

        // 用户积分统计
        Route::post('user-point-stats/_search', [MembershipController::class, 'userPointStats']);

        // 用户积分变化记录
        Route::post('user-point-records/{userId}/_search', [MembershipController::class, 'userPointRecords']);

        // 批量操作
        Route::post('recalculate-user-levels', [MembershipController::class, 'recalculateUserLevels']);
    });

    // 会员页面配置管理
    Route::group(['prefix' => 'membership-page-config'], function () {
        // 获取配置
        Route::get('/', [MembershipPageConfigController::class, 'show']);
        // 更新配置（必须传4个图片）
        Route::put('/', [MembershipPageConfigController::class, 'update']);
    });

    // 任务积分配置管理
    Route::group(['prefix' => 'task-point-configs'], function () {
        // 任务积分配置列表
        Route::post('/_search', [TaskPointConfigController::class, 'index']);
        // 创建任务积分配置
        Route::post('/', [TaskPointConfigController::class, 'store']);
        // 删除任务积分配置
        Route::delete('/{taskPointConfig}', [TaskPointConfigController::class, 'destroy']);
        //批量修改可操作次数和每次操作获得积分
        Route::post('/batch-update', [TaskPointConfigController::class, 'batchUpdate']);       
    });
});
