<?php

namespace App\Http\Controllers;

use App\Models\BrandMasterProgram;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;


class BrandMasterProgramController extends Controller
{
    public function store(Request $request, BrandMasterProgram $BrandMasterProgram): JsonResource
    {
        $validated = $request->validate([
            'email' => ['required', 'string', 'email', 'max:255'],
            'Instagram' =>['nullable', 'string', 'max:255'],
            'Facebook' => ['nullable', 'string', 'max:255'],
            'TikTok' => ['nullable', 'string', 'max:255'],
            'YouTube' =>['nullable', 'string', 'max:255'],
        ], [
            'email.required' => 'Email is required!',
            'email.email' => 'Please provide a valid email address!',
        ]);
        DB::beginTransaction();
        try {
            $BrandMasterProgram->fill($validated)->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($BrandMasterProgram);
    }
}
