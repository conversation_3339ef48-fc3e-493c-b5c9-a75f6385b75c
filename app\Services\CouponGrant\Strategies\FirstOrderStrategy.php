<?php

namespace App\Services\CouponGrant\Strategies;

use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Order\Order;
use App\Models\User\User;

class FirstOrderStrategy extends RuleStrategy
{

    public function checkCanGrant(): bool
    {
        if (!$this->model instanceof Order) {
            return false;
        }
        // 当前订单是有效的
        if (!$this->model->valid) {
            return false;
        }
        // 非此订单之后, 无其他订单存在, 则是首单
        return !Order::query()
            ->where('id', '!=', $this->model->id)
            ->where('user_id', $this->model->user_id)
            ->where('valid', true)
            ->exists();
    }
}
