<?php

namespace App\Models\Enums;
use Illuminate\Support\Arr;

enum CmsTypeEnum: int
{
    case Cms = 1;
    case Faq = 2;
    case Blog = 3;

    public function label(): string
    {
        return match($this) {
            self::Cms => 'CMS',
            self::Faq => 'FAQ',
            self::Blog => 'Blog',
        };
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public function description(): string
    {
        return match($this) {
            self::Cms => '内容管理系统',
            self::Faq => '常见问题',
            self::Blog => '博客文章',
        };
    }
} 