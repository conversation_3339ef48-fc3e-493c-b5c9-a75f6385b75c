<?php

namespace App\Traits\Model;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response as ApiResponse;
use stdClass;

trait UpdateBatch
{
    public static function updateBatch(array $multipleData = [], string $field = 'id', int $num = 1000): bool|int
    {
        try {
            if (empty($multipleData)) {
                throw new \Exception("数据不能为空");
            }

            $tableName = (new self())->getTable(); // 表名
            $firstRow = current($multipleData);
            $updateColumn = array_keys($firstRow);
            // 默认以id为条件更新，如果没有则以第一个字段为条件
            if ($field) {
                $referenceColumn = $field;
                $k = array_search($field, $updateColumn);
                unset($updateColumn[$k]);
            } else {
                $referenceColumn = current($updateColumn);
                unset($updateColumn[0]);
            }

            //一次更新指定条数
            $onceData = array_chunk($multipleData, $num);
            $updateNum = 0;
            foreach ($onceData as $item) {
                // 拼接sql语句
                $updateSql = "UPDATE " . $tableName . " SET ";
                $sets = [];
                $bindings = [];
                foreach ($updateColumn as $uColumn) {
                    $setSql = "`" . $uColumn . "` = CASE ";
                    foreach ($item as $data) {
                        $setSql .= "WHEN `" . $referenceColumn . "` = ? THEN ? ";
                        $bindings[] = $data[$referenceColumn];
                        $bindings[] = $data[$uColumn];
                    }
                    $setSql .= "ELSE `" . $uColumn . "` END ";
                    $sets[] = $setSql;
                }

                $updateSql .= implode(', ', $sets);
                $whereIn = collect($multipleData)->pluck($referenceColumn)->values()->all();
                $bindings = array_merge($bindings, $whereIn);
                $whereIn = rtrim(str_repeat('?,', count($whereIn)), ',');
                $updateSql = rtrim($updateSql, ", ") . " WHERE `" . $referenceColumn . "` IN (" . $whereIn . ")";
                // 传入预处理sql语句和对应绑定数据
                $updateNum += DB::update($updateSql, $bindings);
            }
            return $updateNum;
        } catch (\Exception $e) {
            return false;
        }
    }

}
