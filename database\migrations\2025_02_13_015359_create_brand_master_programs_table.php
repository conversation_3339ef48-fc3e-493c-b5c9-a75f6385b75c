<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brand_master_programs', function (Blueprint $table) {
            $table->id();
            $table->string('email', 255)->comment('Email');
            $table->string('Instagram', 255)->nullable()->comment('Instagram');
            $table->string('Facebook', 255)->nullable()->comment('Facebook');
            $table->string('TikTok', 255)->nullable()->comment('TikTok');
            $table->string('YouTube', 255)->nullable()->comment('YouTube');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('品牌大师计划');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brand_master_programs');
    }
};
