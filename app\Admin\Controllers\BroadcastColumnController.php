<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Attachment;
use App\Models\BroadcastColumn;
use App\Models\Enums\Broadcast\BroadcastColumnContentTypeEnum;
use App\Models\Enums\Broadcast\BroadcastColumnPositionsEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class BroadcastColumnController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::BroadcastsUpdate)->only(['store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::BroadcastsUpdate, Permissions::BroadcastsIndex)->only(['index', 'show']);
    }

    /**
     * 创建
     */
    public function store(Request $request,BroadcastColumn $BroadcastColumn): JsonResource
    {
        $validated = $request->validate([
            'title'         => ['required', 'string', 'max:512',
                Rule::when($request->content_type == 1, [
                    Rule::in(['Facebook', 'Instagram', 'YouTube', 'Pinterest', 'TikTok', 'reddit', 'Twitter']),
                ]),
            ],
            // 'image_id'      => ['nullable', function ($value, $fail) {
            //         if ($value !== '0' && !Attachment::where('id', $value)->exists()) {
            //             $fail('The selected attachment is invalid.');
            //         }
            //     },
            // ],
            'position'      => ['required', 'integer',new Enum(BroadcastColumnPositionsEnum::class)],
            'url'           => ['nullable', 'string', 'max:512'],
            'sort'          => ['required', 'numeric'],
            'is_visible'    => ['nullable', 'boolean'],
            'content_type'  => ['required', 'numeric',new Enum(BroadcastColumnContentTypeEnum::class)],
            'text_title'    => ['nullable', 'string'],
        ], [
            'title.in' => 'The title must be one of: Facebook, Instagram, YouTube, Pinterest, TikTok, reddit, Twitter.',
        ]);
        // $validated['image_id'] = $validated['image_id'] ?: null;
        try {
            if ($BroadcastColumn->fill($validated)->save()) {
                return JsonResource::make($BroadcastColumn);
            } else {
                throw new DataException('Add failed');
            }
        } catch (\Exception $e) {
            throw new DataException($e->getMessage());
        }

    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(BroadcastColumn::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'),
            ])
            ->select([DB::raw('title as label'), DB::raw('id as value')])
            ->get()
            ->with('image:id,path,disk,module');
        return JsonResource::collection($res);
    }

    /**
     * 列表
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(BroadcastColumn::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id')
            ->with('image:id,path,disk,module');
        // \DB::enableQueryLog();
        $res = $builder->paginate($this->getPerPage());
        // dd(\DB::getQueryLog());

        return JsonResource::collection($res);
    }

    /**
     * 删除
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(BroadcastColumn::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        BroadcastColumn::query()->whereIn('id', $ids)
            // ->delete();
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

    /**
     * 修改
     */
    public function update(Request $request, BroadcastColumn $broadcast):JsonResource
    {
        $validated = $request->validate([
            'title'         => ['required', 'string', 'max:512',
                Rule::when($request->content_type == 1, [
                    Rule::in(['Facebook', 'Instagram', 'YouTube', 'Pinterest', 'TikTok', 'reddit', 'Twitter']),
                ]),
            ],
            // 'image_id'      => ['nullable', function ($value, $fail) {
            //         if ($value !== '0' && !Attachment::where('id', $value)->exists()) {
            //             $fail('The selected attachment is invalid.');
            //         }
            //     },
            // ],
            'position'      => ['required', 'numeric',new Enum(BroadcastColumnPositionsEnum::class)],
            'url'           => ['nullable', 'string', 'max:512'],
            'sort'          => ['required', 'numeric'],
            'is_visible'    => ['nullable', 'boolean'],
            'content_type'  => ['required', 'numeric',new Enum(BroadcastColumnContentTypeEnum::class)],
            'text_title'    => ['nullable', 'string'],
        ], [
            'title.in' => 'The title must be one of: Facebook, Instagram, YouTube, Pinterest, TikTok, reddit.',
        ]);
        try{
            // $validated['image_id'] = $validated['image_id'] ?: null;
            $broadcast->update($validated);
        }   catch (\Exception $e) {
            throw new DataException($e->getMessage());
        }
        return JsonResource::make($broadcast);
    }

    /**
     * 详情
     */
    public function show(BroadcastColumn $broadcast): JsonResource
    {
        $broadcast->loadMissing([
            'image:id,path,disk,module',
        ]);
        return JsonResource::make($broadcast);
    }

    public function status(Request $request, BroadcastColumn $broadcast){
        $validated = $request->validate([
            'is_visible' => ['required', 'boolean'],
        ]);
        $broadcast->update($validated);
        return response()->json();
    }

}
