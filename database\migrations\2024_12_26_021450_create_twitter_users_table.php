<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('twitter_users', function (Blueprint $table) {
            $table->id();
            $table->string('email', 128)->comment('邮箱')->index()->nullable();
            $table->string('avatar')->nullable();
            $table->string('twitter_id')->nullable();
            $table->string('name')->nullable();
            $table->string('nickname')->nullable();
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('推特用户');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('twitter_users');
    }
};
