<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_color_images', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_color_id')->index()->comment('商品颜色ID');
            $table->bigInteger('image_id')->index()->comment('图片ID');
            $table->integer('sort')->default(0)->comment('排序');
            $table->unique(['product_color_id', 'image_id']);
            $table->comment('商品颜色图片表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_color_images');
    }
};
