<?php


namespace App\Services;


use App\Constants\CacheKey;
use App\Models\Enums\SysConfigKeyEnum;
use App\Models\SysConfig;
use Illuminate\Support\Facades\Cache;

class SysConfigService
{
    /**
     * 获取值
     * @param SysConfigKeyEnum $key
     * @param $default
     * @return array|null
     */
    public function get(SysConfigKeyEnum $key, $default = null): ?array
    {
        return $this->getOrCreateKey($key, $default)->data;
    }

    /**
     * 获取全部
     *
     * @param array $keys
     *
     * @return SysConfig[]
     */
    public function getAll(array $keys = []): array
    {
        $all = Cache::remember(CacheKey::SysConfigAll->getKey(), now()->addMinutes(30), function () {
            return SysConfig::query()->get();
        });
        if (count($keys)) {
            $all->whereIn('key', $keys);
        }
        return $all->all();
    }

    /**
     * 设置值
     * @param SysConfigKeyEnum $key
     * @param $data
     * @return array|null
     */
    public function set(SysConfigKeyEnum $key, $data): ?array
    {
        $sys_config = $this->getOrCreateKey($key);
        $sys_config->{'data'} = $data;
        $sys_config->save();
        // 清除缓存
        Cache::forget(CacheKey::SysConfigKey->getKey($key->value));
        // 清除全部缓存
        Cache::forget(CacheKey::SysConfigAll->getKey());

        return $sys_config->data;
    }

    /**
     * 获取或创建Key
     *
     * @param SysConfigKeyEnum $key
     * @param $default
     *
     * @return SysConfig
     */
    public function getOrCreateKey(SysConfigKeyEnum $key, $default = null): SysConfig
    {
        return Cache::remember(CacheKey::SysConfigKey->getKey($key->value), now()->addMinutes(30), function () use ($default, $key) {
            $sys_config = SysConfig::query()->where('key', $key)->first();
            if (!$sys_config) {
                $sys_config = new SysConfig([]);
                $sys_config->fill([
                    'key' => $key,
                    'data' => $default,
                ])->save();
            }
            return $sys_config;
        });
    }

}
