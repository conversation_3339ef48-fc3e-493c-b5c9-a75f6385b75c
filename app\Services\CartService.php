<?php

namespace App\Services;

use App\Exceptions\DataException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Product\ProductVariant;
use App\Models\User\UserCoupon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\ShippingFee;
use App\Models\Shipping;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;


class CartService
{
    /**
     * 新增购物车产品
     * @param \App\Models\Cart $cart
     * @param array $item
     * @param mixed $user
     * @param mixed $session_uuid
     * @param mixed $from_activity
     * @return Cart
     */
    public function addCartItem(Cart $cart, array $item = [], $user = null, $session_uuid = null, $from_activity = false): Cart
    {
        if (!$item) {
            return $cart;
        }

        // 新增数量数量
        $num = Arr::get($item, 'num');
        if ($num <= 0) {
            return $cart;
        }

        // 获取产品价格
        if (!isset($item['original_price'])) {
            // 获取价格和原价
            $productVariantPrice = ProductVariant::query()
                ->where('id', Arr::get($item, 'product_variant_id'))
                ->select(['price', 'original_price', 'id', 'product_id', 'weight'])
                ->first();
            $item['price'] = $productVariantPrice->price;
            $item['original_price'] = $productVariantPrice->original_price;
            $item['weight'] = $productVariantPrice->weight;
        }

        // 体验活动产品
        if ($from_activity) {
            // 判断是否已经有相同产品的体验产品了
            $oldActiveItem = $cart->items()
                ->where('product_variant_id', Arr::get($item, 'product_variant_id'))
                ->where('is_activity', true)
                ->first();
            if (!$oldActiveItem) {
                if ($user) {
                    $active = activityService()->isProductOnSale(Arr::get($item, 'product_id'), $user);
                } else {
                    $active = activityService()->getSessionUuidProductPrice(Arr::get($item, 'product_id'), $session_uuid);
                }
                if ($active) {
                    $cart->items()->create([
                        'product_id' => Arr::get($item, 'product_id'),
                        'product_variant_id' => Arr::get($item, 'product_variant_id'),
                        'num' => 1,
                        'add_price' => Arr::get($item, 'price'),
                        'price' => Arr::get($active, 'price'),
                        'original_price' => Arr::get($item, 'original_price'),
                        'is_activity' => true,
                        'user_experience_activity_id' => Arr::get($active, 'user_experience_activity_id'),
                        'weight' => Arr::get($item, 'weight'),
                    ]);
                    $num--;
                    if ($num) {
                        $cart->items()->create([
                            'product_id' => Arr::get($item, 'product_id'),
                            'product_variant_id' => Arr::get($item, 'product_variant_id'),
                            'num' => $num,
                            'add_price' => Arr::get($item, 'price'),
                            'price' => Arr::get($item, 'price'),
                            'original_price' => Arr::get($item, 'original_price'),
                            'is_activity' => false,
                            'user_experience_activity_id' => NULL,
                            'weight' => Arr::get($item, 'weight')
                        ]);
                    }
                    return $cart;
                }
            } else {
                //已有相同的体验产品，需要把产品价格还原成现价
                $productVariantPrice = ProductVariant::query()
                    ->where('id', Arr::get($item, 'product_variant_id'))
                    ->select(['price', 'original_price', 'id', 'product_id'])
                    ->first();
                $item['price'] = $productVariantPrice->price;
            }
        }

        // 更新产品
        $oldItem = $cart->items()
            ->where('product_variant_id', Arr::get($item, 'product_variant_id'))
            ->where('is_activity', false)
            ->first();
        if ($oldItem) {
            $oldItem->update([
                'num' => DB::raw('num + ' . $num),
                'add_price' => Arr::get($item, 'price'),
                'price' => Arr::get($item, 'price'),
                'original_price' => Arr::get($item, 'original_price'),
                'is_activity' => false,
                'user_experience_activity_id' => NULL,
                'weight' => Arr::get($item, 'weight')
            ]);
            return $cart;
        }

        // 新增产品
        $cart->items()->create([
            'product_id' => Arr::get($item, 'product_id'),
            'product_variant_id' => Arr::get($item, 'product_variant_id'),
            'num' => $num,
            'add_price' => Arr::get($item, 'price'),
            'price' => Arr::get($item, 'price'),
            'original_price' => Arr::get($item, 'original_price'),
            'is_activity' => false,
            'user_experience_activity_id' => NULL,
            'weight' => Arr::get($item, 'weight')
        ]);
        return $cart;
    }

    /**
     * 更新购物车产品数量
     * @param \App\Models\Cart $cart
     * @param \App\Models\CartItem $cartItem
     * @param int $num
     * @return Cart
     */
    public function updateCartItem(Cart $cart, CartItem $cartItem, int $num): Cart
    {
        // 体验活动产品只能有一个, 多的新增
        if ($cartItem->is_activity) {
            $num--;
            if ($num) {
                $item = [
                    'product_id' => $cartItem->product_id,
                    'product_variant_id' => $cartItem->product_variant_id,
                    'num' => $num,
                    'price' => $cartItem->price,
                    'original_price' => $cartItem->original_price,
                    'weight' => $cartItem->weight,
                ];
                $this->addCartItem($cart, $item, $cart->user, $cart->session_uuid, $cartItem->is_activity);
            }
            return $cart;
        }
        //直接更新
        $cartItem->update([
            'num' => $num,
        ]);

        return $cart;
    }

    /**  -----------------------------------------↑↑↑↑修改逻辑↑↑↑↑--------------------------------------------------- */

    /**  -----------------------------------------↓↓↓↓刷新逻辑 合并逻辑↓↓↓↓--------------------------------------------------- */

    /**
     * 刷新购物车
     * @param Cart $cart
     * @param Coupon $coupon 手动指定优惠券
     * @param bool $force_refresh
     * @param string|null $country
     * @param UserCoupon|null $userCoupon 指定的用户优惠券
     * @return Cart
     */
    public function refreshCart(Cart $cart, Coupon $coupon = null, $force_refresh = false, $country = null, $userCoupon = null): Cart
    {
        // 使用事务和锁防止并发问题
        return DB::transaction(function () use ($cart, $coupon, $force_refresh, $country, $userCoupon) {
            // 重新获取并锁定购物车
            $cart = Cart::lockForUpdate()->find($cart->id);

            $cart = $this->resetCartItems($cart, $coupon);
            $cart = $this->refreshCartShipping($cart, $force_refresh, $country);
            $cart = $this->refreshCartDiscount($cart, $coupon, $userCoupon);
            return $cart;
        });
    }

    /**
     * 刷新购物车折扣信息
     * @param Cart $cart
     * @param Coupon $coupon 手动指定优惠券
     * @param UserCoupon|null $userCoupon 指定的用户优惠券
     * @return Cart
     */
    public function refreshCartDiscount(Cart $cart, Coupon $coupon = null, UserCoupon $userCoupon = null): Cart
    {
        // 刷新优惠券
        $cart = $this->refreshCartGlobalDiscount($cart, $coupon, $userCoupon);
        // 刷新运费折扣
        $cart = $this->refreshCartShippingDiscount($cart);
        // 更新总表数据
        $this->refreshCartAmount($cart);
        return $cart;
    }

    /**  -----------------------------------------↑↑↑↑修改逻辑 合并逻辑↑↑↑↑--------------------------------------------------- */
    /**  -----------------------------------------↓↓↓↓刷新逻辑↓↓↓↓--------------------------------------------------- */

    /**
     * 刷新购物车产品信息
     * @return bool
     */
    public function refreshCartInfo(Cart $cart): bool
    {
        //购物车信息是否有更新
        $is_refresh = false;
        //刷新购物车产品信息
        $items = $cart->items()->with(['productVariant', 'userExperienceActivity'])->get();
        foreach ($items as $item) {
            // 体验活动产品更新
            if ($item->is_activity) {
                // 如果购物车更新时间大于体验活动更新时间并且体验活动时间未到期则跳过
                if ($item->updated_at > $item->userExperienceActivity->updated_at && now()->lt($item->userExperienceActivity->end_at)) {
                    continue;
                }
                // 体验活动到期，直接去掉活动标识
                if (now()->gt($item->userExperienceActivity->end_at)) {
                    $item->update([
                        'add_price' => $item->productVariant->price,
                        'price' => $item->productVariant->price,
                        'original_price' => $item->productVariant->original_price,
                        'is_activity' => false,
                        'user_experience_activity_id' => NULL,
                    ]);
                    $is_refresh = true;
                    continue;
                }
                //体验活动更新
                $activity_price = activityService()->getProductPrice($item->product_id);
                if (!$activity_price) {
                    // 活动去掉了这个产品，还原原价
                    $item->update([
                        'add_price' => $item->productVariant->price,
                        'price' => $item->productVariant->price,
                        'original_price' => $item->productVariant->original_price,
                        'is_activity' => false,
                        'user_experience_activity_id' => NULL,
                    ]);
                } else {
                    // 活动没有去掉这个产品，更新新价格
                    $item->update([
                        'add_price' => $item->productVariant->price,
                        'price' => $activity_price['price'],
                        'original_price' => $item->productVariant->original_price,
                    ]);
                }
                $is_refresh = true;
                continue;
            }
            // 如果购物车更新时间小于产品更新时间则更新
            if ($item->updated_at < $item->productVariant->updated_at) {
                $item->update([
                    'add_price' => $item->productVariant->price,
                    'price' => $item->productVariant->price,
                    'original_price' => $item->productVariant->original_price,
                ]);
                $is_refresh = true;
            }
        }

        return $is_refresh;
    }

    /**
     * 刷新购物车统计数据
     * @param Cart $cart
     * @return Cart
     */
    public function refreshCartAmount(Cart $cart): Cart
    {
        // 原价
        $original_price = $cart->items()->sum(DB::raw('original_price * num'));
        // 实际现价
        $price = $cart->items()->sum(DB::raw('price * num'));
        // 优惠券折扣金额
        $discount = $cart->items()->sum('discount');
        // 运费
        $delivery_fee_real = $cart->delivery_fee - $cart->delivery_fee_discount;
        $cart->fill(attributes: [
            'subtotal' => round($original_price, 2),                            //商品原价总价
            'discount' => round($original_price - $price + $discount, 2),       //总优惠金额 = 原价 - 现价 + 优惠券折扣金额
            'coupon_discount' => round($discount, 2),                           //优惠券折扣金额
            'total' => round($price - $discount + $delivery_fee_real, 2),   //商品总价 = 实际现价 - 优惠金额 + 运费
        ])->save();
        return $cart;
    }

    /**
     * 重置优惠券，合并变体数量 --- 目前非运费优惠券都不叠加，并且优先手动使用的个人优惠券
     * @param Cart $cart
     * @param Coupon $coupon 手动指定优惠券
     * @return Cart
     */
    public function resetCartItems(Cart $cart, Coupon $coupon = null): Cart
    {
        // 锁定购物车记录（假设已经在事务中）
        $cart = Cart::lockForUpdate()->find($cart->id);

        // 获取所有购物车使用的优惠券
        $discountItems = $cart->items()->select(['num', 'product_variant_id', 'product_id', 'weight'])->whereNotNull('coupon_id')->get();
        // 目前一个购物车只会生效一个非运费优惠券，当这个优惠券是客户手动使用的时候，不需要默认重置
        $old_coupon = $discountItems->first()?->coupon;
        if (!$coupon && $old_coupon && !$old_coupon->is_auto) {
            return $cart;
        }

        // 重置自动使用的优惠券
        $cart->items()->whereNotNull('coupon_id')->delete();
        $items = [];
        foreach ($discountItems as $discountItem) {
            if (isset($items[$discountItem->product_variant_id])) {
                $items[$discountItem->product_variant_id]['num'] += $discountItem->num;
            } else {
                $items[$discountItem->product_variant_id] = [
                    'product_variant_id' => $discountItem->product_variant_id,
                    'product_id' => $discountItem->product_id,
                    'num' => $discountItem->num,
                    'weight' => $discountItem->weight,
                ];
            }
        }
        // 获取价格和原价
        $productVariantPrices = ProductVariant::query()
            ->whereIn('id', array_column($items, 'product_variant_id'))
            ->select(['price', 'original_price', 'id', 'product_id'])
            ->get()
            ->keyBy('id');
        foreach ($items as $item) {
            // 获取变体价格
            $variantPrice = $productVariantPrices->get(Arr::get($item, 'product_variant_id'));
            $item['original_price'] = $variantPrice->original_price;
            $item['price'] = $variantPrice->price;
            // 添加购物车
            $this->addCartItem($cart, $item);
        }

        return $cart;
    }

    /**
     * 刷新购物车非运费全局自动使用的优惠券
     * @param Cart $cart
     * @param Coupon $coupon 手动指定优惠券
     * @param UserCoupon|null $userCoupon 指定的用户优惠券
     * @return Cart
     */
    public function refreshCartGlobalDiscount(Cart $cart, Coupon $coupon = null, UserCoupon $userCoupon = null): Cart
    {
        // 目前一个购物车只会生效一个非运费优惠券，当这个优惠券是客户手动使用的时候，不需要刷新折扣
        $old_coupon = $cart->items()->whereNotNull('coupon_id')->first()?->coupon;
        if (!$coupon && $old_coupon && !$old_coupon->is_auto) {
            return $cart;
        }

        // 如果指定了优惠券，只需要验证能不能用
        if ($coupon) {
            // 绑定新的优惠券
            $processor = new CartDiscountProcessor($coupon, $cart, $userCoupon);
            if ($err = $this->validateCartDiscountProcessor($processor)) {
                throw $err;
            }
            $res = $processor->handle()->updateCart();
            if (!$res) {
                throw new DataException("Coupon Use Error.");
            }
            return $cart;
        }

        // 获取所有自动使用的优惠券规则 - 排除运费优惠券
        $cartDiscountRule = Coupon::query()
            ->where('is_global', true)  //目前只有全局的可以自动使用
            ->where('is_auto', true)
            ->where('enabled', true)
            ->where('type', '!=', DiscountTypeEnum::FreeShippingPromotion)
            ->get();
        // 最优惠的折扣金额
        $maxDiffOriginDiscount = 0;
        // 最优惠的折扣方案
        $maxDiffOriginProcessor = null;
        foreach ($cartDiscountRule as $discount) {
            $processor = new CartDiscountProcessor($discount, $cart);
            // 不满足条件的跳过
            if ($this->validateCartDiscountProcessor($processor)) {
                continue;
            }
            // 比正常折扣多出来的优惠金额
            $diffOriginDiscount = $processor->handle()->getDiffOriginDiscount();
            // 比当前的高。使用当前优惠
            if ($diffOriginDiscount > $maxDiffOriginDiscount) {
                $maxDiffOriginProcessor = $processor;
                $maxDiffOriginDiscount = $diffOriginDiscount;
            }
        }
        // 有最优选择
        if ($maxDiffOriginProcessor) {
            $maxDiffOriginProcessor->updateCart();
        }
        // 由于是clone的cart，所以需要将新数据刷新到当前的cart中
        $cart->refresh();
        return $cart;
    }

    /**
     * 验证折扣券处理器 - 排除活动产品放这边
     * @param \App\Services\CartDiscountProcessor $processor
     * @return DataException|null
     */
    public function validateCartDiscountProcessor(CartDiscountProcessor &$processor): ?DataException
    {
        // 保存原始购物车项
        $originalCartItems = $processor->getCartItems();
        // 过滤掉活动商品，只保留非活动商品
        $filteredCartItems = $originalCartItems->filter(function ($item) {
            return $item->is_activity === false;
        });
        // 设置过滤后的购物车项
        $processor->setCartItems($filteredCartItems);
        return $processor->canUseValidate();
    }

    /**
     * 刷新运费优惠规则
     * 目前规定，运费优惠卷同时只能有一张生效的，所以不需要比较折扣的金额
     * @param Cart $cart
     * @return Cart
     */
    public function refreshCartShippingDiscount(Cart $cart): Cart
    {
        //先重置运费优惠券
        $cart->update([
            'delivery_fee_discount' => 0,
            'delivery_fee_discount_id' => null,
        ]);
        // 获取运费优惠规则
        $cartDiscountRule = Coupon::query()
            ->where('is_global', true)
            ->where('is_auto', true)
            ->where('enabled', true)
            ->where('type', '=', DiscountTypeEnum::FreeShippingPromotion)
            ->get();
        foreach ($cartDiscountRule as $discount) {
            $processor = new CartDiscountProcessor($discount, $cart);

            // 修改：过滤掉活动商品，只让非活动商品参与优惠券计算
            // 保存原始购物车项
            $originalCartItems = $processor->getCartItems();
            // 过滤掉活动商品，只保留非活动商品
            // $filteredCartItems = $originalCartItems->filter(function ($item) {
            //     return $item->is_activity === false;
            // });
            // 设置过滤后的购物车项
            $processor->setCartItems($originalCartItems);

            // 不满足条件的跳过
            $ex = $processor->canUseValidate();
            if ($ex) {
                continue;
            }

            // 运费特殊处理, 直接附加结算
            $processor->handle()->updateCart();
            break;
        }
        // 由于是clone的cart，所以需要将新数据刷新到当前的cart中
        $cart->refresh();
        return $cart;
    }

    /**
     * 判断收费需要刷新购物车运费
     * @param Cart $cart
     * @return Cart
     */
    public function refreshCartShipping(Cart $cart, $force_refresh = false, $country = null): Cart
    {
        //刷新运费 - 如果购物车更新时间小于运费更新时间则需要更新
        if ($force_refresh || $cart->updated_at < $cart->shipping->updated_at || $cart->updated_at < $cart->country_update_at) {
            $cart = $this->doRefreshCartShipping($cart, $country);
        }
        return $cart;
    }

    /**
     * 刷新购物车运费
     * @param Cart $cart
     * @return Cart
     */
    public function doRefreshCartShipping(Cart $cart, $country): Cart
    {
        // 计算购物车商品总重量（num*weight之和）
        $totalWeight = $cart->items()->get()->sum(function ($item) {
            return floatval($item->num) * floatval($item->weight);
        });

        if ($totalWeight == 0) {
            $cart->update(['delivery_fee' => null]);
            return $cart;
        }

        // 获取购物车的shipping_id
        $shippingId = $cart->shipping_id;
        // 先获取购物车绑定国家，如果为空则取前端传递过来的国家
        if ($cart->country) {
            $country = $cart->country;
        }
        
        // 查询shipping_fees表
        $fee = ShippingFee::query()
            ->where('shipping_id', $shippingId)
            ->where('country', $country)
            ->where('min_weight', '<=', $totalWeight)
            ->where('max_weight', '>=', $totalWeight)
            ->value('shipping_fee');

        // 没查到就抛出异常
        if (is_null($fee)) {
            $cart->update(['delivery_fee' => null]);
            throw new DataException("Shipping Fee Not Found.");
        }
        $cart->update(['delivery_fee' => $fee]);
        return $cart;
    }

    /**
     * 根据购重量和国家获取两种运输方式对应的运费
     * @param Cart $cart
     * @return array
     */
    public function getShippingFee($cart): array
    {
        $country = $cart->country;
        // 计算购物车商品总重量（num*weight之和）
        $totalWeight = $cart->items()->get()->sum(function ($item) {
            return floatval($item->num) * floatval($item->weight);
        });
        $shippings = QueryBuilder::for(Shipping::class)
            ->allowedFilters([
                AllowedFilter::exact('type'),
                AllowedFilter::partial('name'),
            ])
            ->get();
        foreach ($shippings as &$shipping) {
            $fee = ShippingFee::query()
                ->where('shipping_id', $shipping->id)
                ->where('country', $country)
                ->where('min_weight', '<=', $totalWeight)
                ->where('max_weight', '>=', $totalWeight)
                ->value('shipping_fee');
            if (is_null($fee)) {
                $shipping->fee = null;
            } else {
                $shipping->fee = $fee;
            }
        }
        return $shippings->toArray();
    }
}
