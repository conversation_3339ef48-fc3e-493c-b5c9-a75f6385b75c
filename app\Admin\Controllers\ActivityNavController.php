<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\ActivityNav;
use App\Models\Attachment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class ActivityNavController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::ActivityNavUpdate)->except(['index', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::ActivityNavIndex, Permissions::ActivityNavUpdate)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $ActivityNav = QueryBuilder::for(ActivityNav::class, $request)
            ->with([
                'image:id,path,disk,module'
            ])
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $ActivityNav->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function store(Request $request, ActivityNav $ActivityNav): JsonResource
    {
        $validated = $request->validate([
            'title' =>['required', 'string', 'max:255'],
            'desc' => ['nullable', 'string', 'max:255'],
            'url' => ['nullable', 'string', 'max:255'],
            'link_name' => ['nullable', 'string', 'max:255'],
            'image_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'status' => ['nullable', 'boolean'],
            'sort' => ['nullable', 'numeric'],
        ]);

        DB::beginTransaction();
        try {
            $ActivityNav->fill($validated)->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($ActivityNav);
    }

    public function update(Request $request, ActivityNav $ActivityNav): JsonResource
    {
        $validated = $request->validate([
            'title' =>['required', 'string', 'max:255'],
            'desc' => ['nullable', 'string', 'max:255'],
            'url' => ['nullable', 'string', 'max:255'],
            'link_name' => ['nullable', 'string', 'max:255'],
            'image_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'status' => ['nullable', 'boolean'],
            'sort' => ['nullable', 'numeric'],
        ]);

        DB::beginTransaction();
        try {
            $ActivityNav->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($ActivityNav);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(ActivityNav::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            ActivityNav::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $ContactUsMessage = QueryBuilder::for(ActivityNav::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'),
            ])
            ->with([
                'image:id,path,disk,module'
            ])
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $ContactUsMessage->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(ActivityNav $ActivityNav): JsonResource
    {
        $ActivityNav->load('image:id,path,disk,module');
        return JsonResource::make($ActivityNav);
    }

    public function status(Request $request, ActivityNav $ActivityNav): JsonResource{
        $validated = $request->validate([
            'status' => ['required', 'boolean'],
        ]);
        $ActivityNav->update($validated);
        return JsonResource::make($ActivityNav);
    }
}
