<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_random_grant_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->bigInteger('coupon_grant_rule_id')->comment('活动id');
            $table->bigInteger('reward_coupon_id')->nullable()->comment('奖励优惠券id');

            $table->engine('InnoDB');
            $table->comment('优惠券随机奖励记录表');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_random_grant_records');
    }
};
