<?php

namespace App\Http\Controllers;

use App\Models\CmsArticle;
use App\Models\CmsSetting;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\Enums\CmsTypeEnum;

class CmsSettingController extends Controller
{
    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(CmsSetting::class, $request)
            ->with([
                'image:id,path,disk,module',
                'clickImage:id,path,disk,module',
                'cmsArticles' => function($query) {
                    $query->select('id', 'title', 'cms_setting_id', 'sort', 'content')
                        ->where('status', true)
                        ->where('type', CmsTypeEnum::Faq->value)
                        ->orderBy('sort', 'desc');
                },
                "adminUser:id,name"
            ])
            ->where('status', true)
            ->allowedSorts(['sort'])
            ->defaultSort('-sort');

        $res = $builder->get();
        return JsonResource::collection($res);
    }

    /**
     * 搜索内容
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function search(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(CmsArticle::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'),
            ])
            ->where('status', true)
            ->allowedSorts(['sort'])
            ->defaultSort('-sort');
        $res = $builder->get();
        return JsonResource::collection($res);
    }
}
