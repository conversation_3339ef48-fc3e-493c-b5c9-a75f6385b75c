<?php

namespace App\Apis\Paypal;

use App\Constants\CacheKey;
use App\Models\Enums\Order\Payment\PaymentMethodTypeEnum;
use App\Models\Order\Payment\PaymentMethod;
use App\Traits\SingletonTrait;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Srmklive\PayPal\Services\PayPal;

class PaypalClient extends PayPal
{
    use SingletonTrait;

    private function __construct()
    {
        parent::__construct($this->getConfig());
        $clientConfig = [
            'verify' => false,
            'timeout' => 30,
        ];
        if ($proxy = config('services.socialite_proxy')) {
            if ($proxy['enabled']) {
                $clientConfig['proxy'] = [
                    'http' => config('services.socialite_proxy.http'),
                    'https' => config('services.socialite_proxy.https'),
                ];
            }
        }
        $client = new Client($clientConfig);
        $this->setClient($client);
        // 加载accessToken
        $this->loadAccessToken();
    }

    private function loadAccessToken(): void
    {
        $response = Cache::remember(CacheKey::PaymentPaypalAccessToken->value, now()->addMinutes(55), function () {
            $res = $this->getAccessToken();
            if (Arr::has($res, ['access_token'])) {
                return $this->getAccessToken();
            }
            return null;
        });

        if(!is_array($response)){
            throw new \Exception( message:"账号配置异常");
        }
        $this->setAccessToken($response);
    }

    private function getConfig()
    {
        // 支付方式
        $payment = PaymentMethod::query()->where('type', PaymentMethodTypeEnum::PayPal->value)->first();
        // 获取配置
        $config = $payment->config;
        return [
            'mode' => Arr::get($config, 'sandbox', false) ? 'sandbox' : 'live', // sandbox or live
            'sandbox' => [
                'client_id' => Arr::get($config, 'sandbox_client_id', '') ?: env('PAYPAL_SANDBOX_CLIENT_ID', ''),
                'client_secret' => Arr::get($config, 'sandbox_client_secret', '') ?: env('PAYPAL_SANDBOX_CLIENT_SECRET', ''),
                'app_id' => Arr::get($config, 'sandbox_app_id', '') ?: env('PAYPAL_SANDBOX_APP_ID', ''),
            ],
            'live' => [
                'client_id' => Arr::get($config, 'client_id', '') ?: env('PAYPAL_LIVE_CLIENT_ID', ''),
                'client_secret' => Arr::get($config, 'client_secret', '') ?: env('PAYPAL_LIVE_CLIENT_SECRET', ''),
                'app_id' => Arr::get($config, 'app_id', '') ?: env('PAYPAL_LIVE_APP_ID', ''),
            ],
            'payment_action' => Arr::get($config, 'payment_action', 'Sale') ?: env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
            'currency' => Arr::get($config, 'currency', 'USD') ?: env('PAYPAL_CURRENCY', 'USD'),
            'notify_url' => Arr::get($config, 'notify_url', '') ?: env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application.
            'locale' => Arr::get($config, 'locale', 'en_US') ?: env('PAYPAL_LOCALE', 'en_US'), // force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
            'validate_ssl' => Arr::get($config, 'validate_ssl', false) ?: env('PAYPAL_VALIDATE_SSL', false), // Validate SSL when creating api client.
        ];
    }
}
