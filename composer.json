{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-fileinfo": "*", "alexkart/curl-builder": "^1.0", "barryvdh/laravel-ide-helper": "^3.1", "google/apiclient": "^2.18", "guzzlehttp/guzzle": "^7.2", "hashids/hashids": "^5.0", "laravel/framework": "^10.0", "laravel/sanctum": "^3.3", "laravel/socialite": "^5.14", "laravel/telescope": "^5.0", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.23", "maatwebsite/excel": "^3.1", "socialiteproviders/twitter": "^4.1", "spatie/laravel-permission": "^6.10", "spatie/laravel-query-builder": "^5.6", "srmklive/paypal": "^3.0", "ext-redis": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}