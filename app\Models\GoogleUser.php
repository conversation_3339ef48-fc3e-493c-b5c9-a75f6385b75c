<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $avatar
 * @property string $email
 * @property string $name
 * @property string $given_name
 * @property string $family_name
 * @property bool $email_verified
 * @property User $user
 */
class GoogleUser extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'email_verified' => 'bool'
    ];

    /**
     * 用户
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

}
