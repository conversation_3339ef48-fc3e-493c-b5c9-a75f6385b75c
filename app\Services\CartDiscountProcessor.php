<?php

namespace App\Services;

use App\Exceptions\DataException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Discount\DiscountAmountTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Product\ProductVariant;
use App\Models\User\UserCoupon;
use Illuminate\Database\Eloquent\Collection as ModelsCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Throwable;

class CartDiscountProcessor
{
    protected Cart $cart;
    protected Collection $rules;
    protected ModelsCollection $cartItems;
    /** @var Collection 临时保存需要新增的详情,只有在数量为准的优惠券时使用 */
    protected Collection $newCartItems;
    protected ?UserCoupon $userCoupon;

    /**
     * @desc 此类用于计算(预处理)购物车优惠价以及保存方法
     * @param Coupon $coupon
     * @param Cart $cart
     * @param UserCoupon|null $userCoupon 指定的用户优惠券
     */
    public function __construct(public Coupon $coupon, Cart $cart, UserCoupon $userCoupon = null)
    {
        $this->cart = clone $cart;
        $this->userCoupon = $userCoupon;
        $this->init();
    }

    protected function init(): void
    {
        // 加载默认规则和命中商品
        $this->cart->loadMissing(['items']);
        $this->coupon->loadMissing(['products:id']);
        $this->rules = collect($this->coupon->rules);
        //当数量优先时，需要新增和修改的详情
        $this->newCartItems = collect();
        // 存在条件需要过滤商品
        if ($canProductIds = $this->coupon->products->pluck('id')->toArray()) {
            $this->cartItems = $this->cart->items->filter(function ($item) use ($canProductIds) {
                if ($item->product instanceof ProductVariant) {
                    return in_array($item->product->product_id, $canProductIds);
                }
                return false;
            });
        } else {
            $this->cartItems = $this->cart->items;
        }
    }

    /**
     * 更新优惠券结果到购物车
     * @return bool
     */
    public function updateCart(): bool
    {
        try {
            DB::beginTransaction();
            // 更新运费需要保存cart
            if ($this->coupon->type == DiscountTypeEnum::FreeShippingPromotion) {
                $this->cart->save();
            }
            // 基于数量分割出来的购物车详情，需要加进来
            if ($this->coupon->type == DiscountTypeEnum::QuantityBasedDiscount) {
                $this->cart->items()->saveMany($this->newCartItems);
            }
            // 由于购物车详情ID是一样的，所以直接保存cartItems会直接更新当前的购物车详情
            $this->cartItems->map(function (CartItem $item) {
                $item->num > 0 ? $item->save() : $item->delete();
            });
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            return false;
        }
        return true;
    }


    /**
     * 获取差价
     * @return float
     */
    public function getDiffOriginDiscount(): float
    {
        // 原价没有折扣
        $originPrice = $this->cartItems->sum(function (CartItem $item) {
            return $item->getOriginal('num') * $item->getOriginal('price');
        });
        // 现价需要减掉折扣
        $nowPrice = $this->cartItems->sum(function (CartItem $item) {
            return $item->num * $item->price - $item->discount;
        });
        // 如果有基于数量的折扣拆分详情，需要另外加上
        if ($this->newCartItems->isNotEmpty()) {
            $nowPrice += $this->newCartItems->sum(function (CartItem $item) {
                return $item->num * $item->price - $item->discount;
            });
        }
        return $originPrice - $nowPrice;
    }

    /**
     * 处理数据
     * @return $this
     */
    public function handle(): static
    {
        if ($this->cartItems->isEmpty()) {
            return $this;
        }
        switch ($this->coupon->type) {
            case DiscountTypeEnum::QuantityBasedDiscount:
                // 基于数量享折扣 计算是否满足数量
                $priceKey = match ($this->coupon->price_type) {
                    DiscountPriceTypeEnum::Price => 'price',
                    DiscountPriceTypeEnum::OriginalPrice => 'original_price'
                };
                // 当前商品数量满足几个折扣
                $filterRules = $this->rules->where('quantity', '<=', $this->cartItems->sum('num'));
                // 一共多少个商品参与折扣
                $discountGoodsCount = $filterRules->max('quantity');
                // 按照价格降序
                $this->cartItems = $this->cartItems->sortBy($priceKey);

                // 处理商品数量，确保只有配置的数量享受折扣
                $processedCount = 0;
                foreach ($this->cartItems as &$item) {
                    // 如果已经处理完所有配置的折扣数量，跳出循环
                    if ($processedCount >= $discountGoodsCount) {
                        break;
                    }

                    // 处理当前商品的每一件
                    for ($item->num; $item->num > 0 && $processedCount < $discountGoodsCount;) {
                        // 复制一个新的商品 并绑定优惠券
                        $newItem = $item->replicate()->fill([
                            'price' => $item->{$priceKey},
                            'num' => 1,
                            'coupon_id' => $this->coupon->id,
                            'user_coupon_id' => $this->userCoupon?->id,
                            'discount' => 0,
                            'is_activity' => false,
                            'user_experience_activity_id' => null,
                        ]);
                        // 从最后拿折扣金额
                        if ($discount = $filterRules->pop()) {
                            $discountAmountType = DiscountAmountTypeEnum::tryFrom(Arr::get($discount, 'discount_amount_type'));
                            $newItem->discount = match ($discountAmountType) {
                                DiscountAmountTypeEnum::RandomAmount => 0,
                                DiscountAmountTypeEnum::PercentAmount => round(min(round($item->{$priceKey} * (float) Arr::get($discount, 'discount_rate', 0) / 100, 4), Arr::get($discount, 'discount_max_price', 0)), 2),
                                DiscountAmountTypeEnum::FixedAmount => Arr::get($discount, 'discount_price', 0),
                            };
                        }
                        // 保存需要新增的购物车详情
                        $this->newCartItems->push($newItem);
                        $item->num--;
                        $processedCount++;
                    }
                }
                unset($item);
                break;
            case DiscountTypeEnum::SpendBasedDiscount:
                // 基于价格享折扣 计算是否满足价格
                $priceKey = match ($this->coupon->price_type) {
                    DiscountPriceTypeEnum::Price => 'price',
                    DiscountPriceTypeEnum::OriginalPrice => 'original_price'
                };
                // 总价
                $totalPrice = $this->cartItems->sum(function (CartItem $item) use ($priceKey) {
                    return $item->{$priceKey} * $item->num;
                });
                // 命中的最后一条规则
                $maxRule = $this->rules->where('price', '<=', $totalPrice)->pop();
                if (!$maxRule) {
                    break; // 如果没有命中规则，直接退出
                }
                $discountAmountType = DiscountAmountTypeEnum::tryFrom(Arr::get($maxRule, 'discount_amount_type'));
                // 处理2种情况, 第一种
                switch ($discountAmountType) {
                    // 固定金额 平均分配给每个商品
                    case  DiscountAmountTypeEnum::FixedAmount:
                        // 总优惠金额
                        $discountPrice = Arr::get($maxRule, 'discount_price');
                        // 当前剩余未用优惠金额
                        $notUsedDiscountPrice = Arr::get($maxRule, 'discount_price');
                        $totalCount = $this->cartItems->count();
                        // 折扣
                        $this->cartItems->each(function (CartItem $item, $index) use (&$notUsedDiscountPrice, $discountPrice, $totalPrice, $priceKey, $totalCount) {
                            if ($notUsedDiscountPrice) {
                                // 等比例分布优惠
                                $discount = bcmul(bcdiv($item->{$priceKey} * $item->num, $totalPrice, 8), $discountPrice, 2);
                                $discount = round($discount, 2);
                                $discount = min($discount, $notUsedDiscountPrice);
                                if ($index + 1 == $totalCount) {
                                    $discount = $notUsedDiscountPrice;
                                }
                                $item->discount = $discount;
                                // 计算剩余
                                $notUsedDiscountPrice -= $discount;
                            }
                            // 绑定优惠券
                            $item->price = $item->{$priceKey};
                            $item->coupon_id = $this->coupon->id;
                        });
                        break;
                    // 固定折扣,每个商品折扣
                    case  DiscountAmountTypeEnum::PercentAmount:
                        $discountRate = (float) Arr::get($maxRule, 'discount_rate');
                        // 获取规则中的最高优惠金额限制
                        $discountMaxPrice = (float) Arr::get($maxRule, 'discount_max_price', 0);
                        // 如果购物车只有一件商品，直接计算并应用折扣
                        if ($this->cartItems->count() === 1) {
                            $item = $this->cartItems->first();
                            $discount = bcdiv(bcmul(bcmul($item->{$priceKey}, $item->num, 8), $discountRate, 8), 100, 2);

                            // 如果设置了最高优惠金额限制且大于0，则取折扣金额和最高限制中的较小值
                            if ($discountMaxPrice > 0) {
                                $discount = min($discount, $discountMaxPrice);
                            }

                            $item->discount = round($discount, 2);
                            $item->price = $item->{$priceKey};
                            $item->coupon_id = $this->coupon->id;
                            $item->user_coupon_id = $this->userCoupon?->id;
                        } else {

                            // 先计算所有商品的原始折扣总额
                            $totalDiscount = 0;
                            $itemDiscounts = [];

                            // 第一次循环：计算每个商品的原始折扣和总折扣
                            $this->cartItems->each(function (CartItem $item, $index) use ($discountRate, $priceKey, &$totalDiscount, &$itemDiscounts) {
                                // 计算商品折扣
                                $itemDiscount = bcdiv(bcmul(bcmul($item->{$priceKey}, $item->num, 8), $discountRate, 8), 100, 2);
                                $itemDiscounts[$index] =  round($itemDiscount, 2);
                                $totalDiscount = bcadd($totalDiscount, $itemDiscount, 2);
                            });

                            // 如果设置了最高优惠金额限制且总折扣超过了限制，则按比例缩减
                            if ($discountMaxPrice > 0 && $totalDiscount > $discountMaxPrice) {
                                $ratio = bcdiv($discountMaxPrice, $totalDiscount, 8); // 计算缩减比例

                                // 第二次循环：按比例分配折扣
                                $this->cartItems->each(function (CartItem $item, $index) use ($priceKey, &$itemDiscounts, $ratio) {
                                    // 按比例缩减折扣
                                    $adjustedDiscount = bcmul($itemDiscounts[$index], $ratio, 2);
                                    $item->discount = round($adjustedDiscount, 2);;
                                    $item->price = $item->{$priceKey};
                                    $item->coupon_id = $this->coupon->id;
                                    $item->user_coupon_id = $this->userCoupon?->id;
                                });
                            } else {
                                // 如果没有超过限制，直接使用原始折扣
                                $this->cartItems->each(function (CartItem $item, $index) use ($priceKey, &$itemDiscounts) {
                                    $item->discount = $itemDiscounts[$index];
                                    $item->price = $item->{$priceKey};
                                    $item->coupon_id = $this->coupon->id;
                                    $item->user_coupon_id = $this->userCoupon?->id;
                                });
                            }
                        }
                        break;
                }
                break;
            case DiscountTypeEnum::FreeShippingPromotion:
                // 运费优惠券
                // 总价
                $totalPrice = $this->cartItems->sum(function (CartItem $item) {
                    return $item->price * $item->num;
                });
                //总折扣
                $totalDiscount = $this->cartItems->sum('discount');
                // 命中的最后一条规则
                $maxRule = $this->rules->where('price', '<=', $totalPrice - $totalDiscount)
                    ->where('shipping_id', $this->cart->shipping_id)->sortByDesc('price')->first();
                if ($maxRule) {
                    // 直接免运费
                    $this->cart->fill([
                        'delivery_fee_discount' => $this->cart->delivery_fee,
                        'delivery_fee_discount_id' => $this->coupon->id,
                    ]);
                }
                break;
        }
        return $this;
    }


    /**
     * 是否可用验证
     * @return DataException|null
     */
    public function canUseValidate(): ?DataException
    {
        if (!$this->coupon->enabled) {
            return new DataException('Coupon not enabled.');
        }
        // 如果存在固定截止时间 并且有时间小于当前时间
        if ($this->coupon->effective_end_at && $this->coupon->effective_end_at->lte(now())) {
            return new DataException('This coupon has expired.');
        }

        $user = Auth::user();
        if($user && ($this->coupon->type != DiscountTypeEnum::FreeShippingPromotion->value)){
            //判断优惠券使用次数
            if($this->coupon->user_count > 0){
                $usedCount = $user->userCoupons()->where('coupon_id', $this->coupon->id)->where('used_at', '!=', null)->count();
                if($usedCount >= $this->coupon->user_count){
                    return new DataException('This coupon has already been used.');
                }
            }
        }

        if($this->coupon->total_count != null && $this->coupon->total_count <= 0){
            return new DataException('This coupon has been sent out.');
        }

        switch ($this->coupon->type) {
            case DiscountTypeEnum::QuantityBasedDiscount:
                // 数量不满足使用条件
                if ($this->rules->min('quantity') > $this->cartItems->sum('num')) {
                    return new DataException('Coupon usage failed, did not reach the specified quantity.');
                }
                break;
            case DiscountTypeEnum::SpendBasedDiscount:
                $totalPrice = $this->cartItems->sum(function ($item) {
                    // 无条件约束
                    return match ($this->coupon->price_type) {
                        DiscountPriceTypeEnum::Price => $item->price * $item->num,
                        DiscountPriceTypeEnum::OriginalPrice => $item->original_price * $item->num,
                    };
                });
                // 价格不满足使用条件
                if ($this->rules->min('price') > $totalPrice) {
                    return new DataException('Coupon usage failed, did not reach the specified price.');
                }
                break;
            case DiscountTypeEnum::FreeShippingPromotion:
                $totalPrice = $this->cartItems->sum(function ($item) {
                    // 无条件约束
                    return match ($this->coupon->price_type) {
                        DiscountPriceTypeEnum::Price => $item->price * $item->num,
                        DiscountPriceTypeEnum::OriginalPrice => $item->original_price * $item->num,
                    };
                });
                // 价格以及运输方式不满足使用条件
                if (
                    $this->rules->min('price') > $totalPrice ||
                    $this->rules->where('shipping_id', $this->cart->shipping_id)->isEmpty()
                ) {
                    return new DataException('Coupon usage failed, did not reach the specified price.');
                }

                break;
        }
        return null;
    }

    /**
     * 获取当前处理的购物车项
     * @return \Illuminate\Support\Collection
     */
    public function getCartItems()
    {
        return $this->cartItems;
    }

    /**
     * 设置要处理的购物车项
     * @param \Illuminate\Support\Collection $cartItems
     * @return $this
     */
    public function setCartItems($cartItems)
    {
        $this->cartItems = $cartItems;
        return $this;
    }
}
