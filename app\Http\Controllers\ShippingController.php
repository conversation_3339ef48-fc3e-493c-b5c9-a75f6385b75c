<?php

namespace App\Http\Controllers;

use App\Models\Shipping;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spa<PERSON>\QueryBuilder\QueryBuilder;

class ShippingController extends Controller
{
    /**
     * 选择
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function option(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Shipping::class, $request)
        ->allowedFilters([
            AllowedFilter::exact('type'),
            AllowedFilter::partial('name'),
        ])
        ->get();

        return JsonResource::collection($res);
    }
}
