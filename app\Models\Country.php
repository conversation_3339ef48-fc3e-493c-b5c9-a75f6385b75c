<?php

namespace App\Models;

use App\Traits\Model\UpdateBatch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Country extends MiddleModel
{
    use HasFactory;
    use UpdateBatch;

    protected $guarded = [];

    public function states(): HasMany
    {
        return $this->hasMany(State::class);

    }

    /**
     * 获取该国家的货币
     */
    public function currency()
    {
        return $this->hasOne(CountryCurrency::class, 'country_code', 'iso_code')
            ->where('active', true);
    }
}
