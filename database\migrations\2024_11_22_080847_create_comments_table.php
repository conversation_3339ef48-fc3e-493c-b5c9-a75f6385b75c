<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->nullable()->default(NULL)->index()->comment('订单ID');
            $table->bigInteger('product_id')->index()->comment('商品ID');
            $table->bigInteger('product_variant_id')->nullable()->default(NULL)->index()->comment('商品变体ID');
            $table->bigInteger('user_id')->nullable()->index()->comment('用户ID');
            $table->string('first_name')->nullable()->comment('用户名');
            $table->string('last_name')->nullable()->comment('用户姓');
            $table->string('email')->nullable()->comment('用户邮箱');
            $table->text('content')->comment('内容');
            $table->float('grade')->comment('评分');
            $table->tinyInteger('status')->default(0)->comment('是否通过审核:0:未审核,1:审核通过,2:审核不通过');
            $table->tinyInteger('has_images')->default(0)->comment('是否有图片');
            $table->boolean('enabled')->default(0)->comment('发布的');
            $table->timestamp('top_at')->nullable()->comment('置顶时间');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('评论');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
