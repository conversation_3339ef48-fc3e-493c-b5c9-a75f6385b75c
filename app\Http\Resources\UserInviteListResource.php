<?php

namespace App\Http\Resources;

use App\Models\User\User;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property User $resource
 */
class UserInviteListResource extends JsonResource
{
    /**
     * 将资源转换为数组。
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'email' => $this->formatEmail($this->email), // 调用格式化邮箱的方法
            'created_at' => $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : null,
            'estimated_commission' => $this->estimated_commission,
            'estimated_commission_agent' => convertPrice($this->estimated_commission, currentCurrency()),
        ];
    }
    /*
     * 格式化邮箱地址。
     *
     * @param  string  $email
     * @return string
     */
    protected function formatEmail($email)
    {
        // 找到 @ 的位置
        $atPosition = strpos($email, '@');

        // 如果没有 @，则返回原始邮箱
        if ($atPosition === false) {
            return $email;
        }
        // 获取 @ 前的部分
        $namePart = substr($email, 0, $atPosition);
        // 获取 @ 后的部分
        $domainPart = substr($email, $atPosition);
        $formattedEmail = str_repeat('*', max(0, strlen($namePart) - 3)) . substr($namePart, -3) . $domainPart;
        return $formattedEmail;
    }
}
