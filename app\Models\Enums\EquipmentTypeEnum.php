<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum EquipmentTypeEnum: int
{
    case WebEquipment = 1;
    case MobileEquipment = 2;
    case AllEquipment = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::WebEquipment->value => 'PC', // PC
            self::MobileEquipment->value => 'Mobile', // 手机
            self::AllEquipment->value => 'All', // 所有
        ];
    }

}
