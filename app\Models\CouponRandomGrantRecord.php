<?php

namespace App\Models;

use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Models\Coupon\Coupon;
use App\Models\Enums\CouponGrantType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property Coupon $rewardCoupon
 * @property User $user
 */
class CouponRandomGrantRecord extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected static function booted()
    {
        parent::booted();
        static::created(function ($coupon) {
            CouponGrantCheck::dispatch(CouponGrantType::RandomGrant, $coupon)->onQueue(QueueKey::Coupon->value);
        });

    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    public function rewardCoupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }
}
