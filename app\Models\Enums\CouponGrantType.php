<?php

namespace App\Models\Enums;

use App\Models\Coupon\Coupon;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;

enum CouponGrantType: int
{
    case FirstOrder = 1;
    case RegisterUser = 2;
    case ShareInvite = 3;
    case Subscribed = 4;
    case Comment = 5;
    case RandomGrant = 6;
    case PointExchange = 7;


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::FirstOrder->value => '首单奖励',
            self::RegisterUser->value => '注册奖励',
            self::ShareInvite->value => '分享奖励',
            self::Subscribed->value => '订阅奖励',
            self::Comment->value => '评论奖励',
            self::RandomGrant->value => '随机奖励',
            self::PointExchange->value => '积分兑换',
        ];
    }

    public function validateRules(): array
    {
        return match ($this) {
            self::ShareInvite => [
                'rebate_order_price' => ['required', 'numeric', 'min:0'],
                'rebate_coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ],
            self::Comment => [
                'picture_coupon_id' => ['required', new Exists(Coupon::class, 'id')],
                'video_coupon_id' => ['required', new Exists(Coupon::class, 'id')],
                'picture_video_coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ],
            self::RandomGrant => [
                'user_lottery_limit' => ['required', 'numeric'],
                'coupons' => ['required', 'array', function ($attribute, $value, $fail) {
                    if (array_sum(Arr::pluck($value, 'weight')) > 100) {
                        return $fail("The probability of winning the lottery must be less than or equal to 100%");
                    }
                }],
                'coupons.*.coupon_id' => ['required', new Exists(Coupon::class, 'id')],
                'coupons.*.weight' => ['required', 'numeric'],
            ],
            self::PointExchange => [
                'coupons' => ['required', 'array', 'min:1', 'max:4'],
                'coupons.*.coupon_id' => ['required', new Exists(Coupon::class, 'id')],
                'coupons.*.required_points' => ['required', 'integer', 'min:1'],
            ],
            default => []
        };
    }
}
