<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_point_change_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->decimal('change_amount', 10, 2)->comment('积分变化数量（正数为增加，负数为减少）');
            $table->decimal('balance_before', 10, 2)->comment('变化前积分余额');
            $table->decimal('balance_after', 10, 2)->comment('变化后积分余额');
            $table->string('change_type', 50)->comment('变化类型');
            $table->text('description')->nullable()->comment('变化描述');
            $table->string('source_type', 100)->nullable()->comment('来源模型类型');
            $table->bigInteger('source_id')->nullable()->comment('来源模型ID');
            
            // 积分发放状态相关字段
            $table->enum('status', ['pending', 'completed', 'failed'])->default('completed')->comment('状态：待发放/已完成/失败');
            $table->timestamp('scheduled_at')->nullable()->comment('计划执行时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            
            // 兑换优惠券相关字段
            $table->string('coupon_code', 100)->nullable()->comment('兑换的优惠券码');
            $table->string('coupon_name', 200)->nullable()->comment('兑换的优惠券名称');
            
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'created_at']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['change_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_point_change_records');
    }
};
