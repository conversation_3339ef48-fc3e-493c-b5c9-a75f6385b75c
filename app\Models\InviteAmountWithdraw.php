<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

class InviteAmountWithdraw extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    // 不可以声明, 包内有用
    protected $casts = [
        'payment_account' => 'json',
    ];
    protected $appends = [
        'amount_agent',
        'commission_amount_agent',
        'withdraw_amount_agent'
    ];

    /**************** 金额币种转换 ****************/
    public function amountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->amount, currentCurrency())
        );
    }

    public function commissionAmountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->commission_amount, currentCurrency())
        );
    }

    public function withdrawAmountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->withdraw_amount, currentCurrency())
        );
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
