<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use phpDocumentor\Reflection\Types\Nullable;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_invite_rewards', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('订单ID');
            $table->bigInteger('order_user_id')->index()->comment('下单用户ID');
            $table->timestamp('order_created_at')->comment('订单成功支付时间');
            $table->float('order_total', 10, 2)->comment('订单总金额');
            $table->bigInteger('reward_user_id')->index()->comment('用户ID');
            $table->bigInteger('rule_id')->index()->comment('返现规则ID');
            $table->tinyInteger('reward_type', 1)->comment('返现奖励类型');
            $table->float('reward_amount', 10, 2)->comment('返现金额');
            $table->bigInteger('reward_coupon_id')->index()->comment('返现优惠券ID');
            $table->boolean('is_effective')->default(true)->comment('是否有效');
            $table->boolean('is_cashback')->default(false)->comment('是否已返现');
            $table->tinyInteger('remark_type')->nullable()->comment('备注类型');
            $table->timestamp('cashback_at')->comment('返现时间');

            $table->engine('InnoDB');
            $table->comment('订单返现奖励表');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_invite_rewards');
    }
};
