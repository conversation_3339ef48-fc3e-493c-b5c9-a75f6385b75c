<?php

namespace App\Models\Product;

use App\Constants\QueueKey;
use App\Jobs\Product\ProductSyncErp;
use App\Models\Attachment;
use App\Models\AttributeValue;
use App\Models\Order\OrderItemProduct;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property string $sku
 * @property Product $product
 * @property Attachment $image
 * @property float $price
 * @property float $original_price
 * @property AttributeValue|null $colorAttribute
 * @property AttributeValue|null $sizeAttribute
 * @property int $stock
 * @property int $product_id
 */
class ProductVariant extends MiddleModel implements OrderItemProduct
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'price' => 'float',
        'original_price' => 'float',
        'is_publish' => 'boolean',
    ];
    protected $appends = [
        'image_ids',
        'discount_rate',
        'price_agent',
        'original_price_agent',
        'is_publish_current',
    ];

    public static function booted(): void
    {
        static::creating(function (self $variant) {
            $variant->loadMissing(['colorAttribute', 'sizeAttribute']);
            $variant->sku ??= $variant->generateSku();
        });
        // 保存时执行
        static::updating(function (self $variant) {
            if ($variant->isDirty('color_attribute_value_id') ||
                $variant->isDirty('size_attribute_value_id')) {
                $variant->sku = $variant->generateSku();
            }
        });
    }

    public function generateSku(): string
    {
        $this->loadMissing(['colorAttribute', 'sizeAttribute', 'product:id,spu']);
        return Str::upper(join('_', [trim($this->product->spu), trim($this?->colorAttribute?->value), trim($this?->sizeAttribute?->value)]));
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function images(): BelongsToMany
    {
        return $this->belongsToMany(
            Attachment::class,
            ProductVariantImage::class,
            'product_variant_id',
            'attachment_id',
            'id',
            'id'
        )->withPivot('sort')->orderByPivot('sort', 'desc');
    }

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    public function colorAttribute(): BelongsTo
    {
        return $this->belongsTo(AttributeValue::class, 'color_attribute_value_id', 'id');
    }

    public function sizeAttribute(): BelongsTo
    {
        return $this->belongsTo(AttributeValue::class, 'size_attribute_value_id', 'id');
    }

    public function imageIds(): Attribute
    {
        return Attribute::get(function () {
            if ($this->relationLoaded('images')) {
                return $this->images->pluck('id')->toArray();
            }
            return [];
        });
    }

    public function discountRate(): Attribute
    {
        return Attribute::get(function () {
            if ($this->original_price && $this->original_price) {
                return getRate($this->price, $this->original_price, 2);
            }
            return 0;
        });
    }

    /**************** 金额币种转换 ****************/
    public function priceAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->price, currentCurrency())
        );
    }

    public function originalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->original_price, currentCurrency())
        );
    }

    /**
     * 获取商品当前的发布状态（考虑关联的Product状态）
     */
    public function isPublishCurrent(): Attribute
    {
        return Attribute::get(function () {
            // 如果变体本身未发布，直接返回false
            if (!$this->is_publish) {
                return false;
            }

            // 检查关联的Product是否发布
            if ($this->relationLoaded('product')) {
                return $this->product->is_publish ?? false;
            }

            // 如果Product关系未加载，查询数据库
            return $this->product()->value('is_publish') ?? false;
        });
    }

    // 商品订单属性-名称
    public function getName(): string
    {
        return $this->product->title;
    }

    // 商品订单属性-价格
    public function getPrice(): float
    {
        return $this->price;
    }

    // 商品订单属性-价格
    public function getOriginalPrice(): float
    {
        return $this->original_price;
    }

    // 商品订单属性-主图
    public function getImageUrl(): string
    {
        return $this->image->url;
    }

    // 商品订单属性-商品信息
    public function getProductInfo(): array
    {
        return [
            'sku' => $this->product->spu,
            'spu' => $this->sku,
            'title' => $this->product->title,
            'slug_title' => $this->product->slug_title,
            'desc' => $this->product->desc,
            'short_desc' => $this->product->short_desc,
            'model_desc' => $this->product->model_desc,
            'brand_name' => $this->product->brand_name,
            'category_name' => $this->product->category_name,
            'style_name' => $this->product->style_name,
            'material_name' => $this->product->material_name,
            'color' => $this->colorAttribute->value,
            'size' => $this->sizeAttribute->value,
        ];
    }
}
