<?php

namespace Tests\Unit;

use App\Models\Enums\Discount\DiscountAmountTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Enums\Discount\QuantityDiscountModeEnum;
use App\Models\Coupon\Coupon;
use App\Models\CartItem;
use App\Services\CartDiscountProcessor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Tests\TestCase;

class QuantityDiscountTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 测试区间折扣
     * 第1-3件9折，第4件以上8折
     */
    public function test_interval_discount()
    {
        // 创建优惠券
        $coupon = new Coupon([
            'type' => DiscountTypeEnum::QuantityBasedDiscount,
            'price_type' => DiscountPriceTypeEnum::Price,
            'enabled' => true,
            'rules' => [
                'discount_mode' => QuantityDiscountModeEnum::IntervalDiscount->value,
                'rules' => [
                    [
                        'quantity' => 1,
                        'quantity_end' => 3,
                        'discount_amount_type' => DiscountAmountTypeEnum::PercentAmount->value,
                        'discount_rate' => 10, // 9折
                        'discount_max_price' => 100,
                    ],
                    [
                        'quantity' => 4,
                        'quantity_end' => 999,
                        'discount_amount_type' => DiscountAmountTypeEnum::PercentAmount->value,
                        'discount_rate' => 20, // 8折
                        'discount_max_price' => 200,
                    ],
                ],
            ],
        ]);

        // 创建购物车项（5件商品，每件100元）
        $cartItems = collect([
            new CartItem(['price' => 100, 'num' => 5]),
        ]);

        // 处理折扣
        $processor = new CartDiscountProcessor($coupon);
        $processor->setCartItems($cartItems);
        $processor->handle();

        $newItems = $processor->getNewCartItems();

        // 验证结果
        $this->assertCount(5, $newItems);
        
        // 前3件应该是9折（10元折扣）
        for ($i = 0; $i < 3; $i++) {
            $this->assertEquals(10, $newItems[$i]->discount);
        }
        
        // 后2件应该是8折（20元折扣）
        for ($i = 3; $i < 5; $i++) {
            $this->assertEquals(20, $newItems[$i]->discount);
        }
    }

    /**
     * 测试循环折扣
     * 循环件数3件：第1件不打折，第2件9折，第3件8折
     */
    public function test_cyclic_discount()
    {
        // 创建优惠券
        $coupon = new Coupon([
            'type' => DiscountTypeEnum::QuantityBasedDiscount,
            'price_type' => DiscountPriceTypeEnum::Price,
            'enabled' => true,
            'rules' => [
                'discount_mode' => QuantityDiscountModeEnum::CyclicDiscount->value,
                'cycle_quantity' => 3,
                'rules' => [
                    [
                        'quantity' => 1,
                        'discount_amount_type' => DiscountAmountTypeEnum::PercentAmount->value,
                        'discount_rate' => 0, // 不打折
                        'discount_max_price' => 0,
                    ],
                    [
                        'quantity' => 2,
                        'discount_amount_type' => DiscountAmountTypeEnum::PercentAmount->value,
                        'discount_rate' => 10, // 9折
                        'discount_max_price' => 50,
                    ],
                    [
                        'quantity' => 3,
                        'discount_amount_type' => DiscountAmountTypeEnum::PercentAmount->value,
                        'discount_rate' => 20, // 8折
                        'discount_max_price' => 100,
                    ],
                ],
            ],
        ]);

        // 创建购物车项（7件商品，每件100元）
        $cartItems = collect([
            new CartItem(['price' => 100, 'num' => 7]),
        ]);

        // 处理折扣
        $processor = new CartDiscountProcessor($coupon);
        $processor->setCartItems($cartItems);
        $processor->handle();

        $newItems = $processor->getNewCartItems();

        // 验证结果
        $this->assertCount(7, $newItems);
        
        // 验证循环模式：0, 10, 20, 0, 10, 20, 0
        $expectedDiscounts = [0, 10, 20, 0, 10, 20, 0];
        
        for ($i = 0; $i < 7; $i++) {
            $this->assertEquals($expectedDiscounts[$i], $newItems[$i]->discount, "Item $i discount mismatch");
        }
    }
}
