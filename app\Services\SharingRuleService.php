<?php

namespace App\Services;

use App\Models\Coupon\Coupon;
use App\Models\Enums\Reward\RewardFansTypeEnum;
use App\Models\Enums\Reward\RewardPercentMaxPriceTypeEnum;
use App\Models\Order\Order;
use App\Models\SharingRule;
use Arr;

class SharingRuleService
{
    /**
     * 是否有重复的生效时间
     * @param mixed $effective_start_at
     * @param mixed $effective_end_at
     * @return bool
     */
    public function HasEffectivePeriodOverlaps($effective_start_at, $effective_end_at, ?SharingRule $rule = null)
    {
        return SharingRule::query()
            ->where('is_publish', true)
            ->where(function ($query) use ($effective_start_at, $effective_end_at) {
                $query->where(function ($subQuery) use ($effective_start_at) {
                    $subQuery->where(function ($subSubQuery) use ($effective_start_at) {
                        if ($effective_start_at) {
                            $subSubQuery->whereDate('effective_start_at', '>=', $effective_start_at);
                        }
                        $subSubQuery->orWhereNull('effective_start_at');
                    });
                })
                    ->orWhere(function ($subQuery) use ($effective_end_at) {
                        $subQuery->where(function ($subSubQuery) use ($effective_end_at) {
                            if ($effective_end_at) {
                                $subSubQuery->whereDate('effective_end_at', '<=', $effective_end_at);
                            }
                            $subSubQuery->orWhereNull('effective_end_at');
                        });
                    });
            })
            ->when($rule, function ($query) use ($rule) {
                $query->where('id', '<>', $rule->id);
            })
            ->exists();
    }

    /**
     * 获取固定金额奖励
     * @param \App\Models\SharingRule $rule
     * @param \App\Models\Order\Order $order
     * @param \App\Models\Enums\Reward\RewardFansTypeEnum $fans_type
     * @return float|null
     */
    public function getFixedAmountReward(SharingRule $rule, Order $order, RewardFansTypeEnum $fans_type)
    {
        // 获取规则内容
        $rule_content = Arr::get($rule->rules, $fans_type->value, []);
        if ($rule_content) {
            if ($order->is_first_order) {
                // 首单
                $order_rule = Arr::get($rule_content, 'first_order', []);
            } else {
                // 其它单
                $order_rule = Arr::get($rule_content, 'other_order', []);
            }
            $amount = Arr::get($order_rule, 'price', 0);

            return $amount;
        }

        return null;
    }

    /**
     * 获取百分比奖励
     * @param \App\Models\SharingRule $rule
     * @param \App\Models\Order\Order $order
     * @param \App\Models\Enums\Reward\RewardFansTypeEnum $fans_type
     * @return float|null
     */
    public function getPercentReward(SharingRule $rule, Order $order, RewardFansTypeEnum $fans_type)
    {
        // 获取规则内容
        $rule_content = Arr::get($rule->rules, $fans_type->value, []);
        if ($rule_content) {
            if ($order->is_first_order) {
                // 首单
                $order_rule = Arr::get($rule_content, 'first_order', []);
            } else {
                // 非首单
                $order_rule = Arr::get($rule_content, 'other_order', []);
            }

            $percent = Arr::get($order_rule, 'percent', 0);
            $amount = $order->paid_amount * $percent / 100;
            $max_price_type = Arr::get($order_rule, 'max_price_type', RewardPercentMaxPriceTypeEnum::None);
            if ($max_price_type == RewardPercentMaxPriceTypeEnum::FixedAmount->value) {
                // 有最大值
                $max_price = Arr::get($order_rule, 'max_price', 0);
                // 取最小值作为奖励金额
                $amount = min($amount, $max_price);
            }

            return $amount;
        }

        return null;
    }

    /**
     * 获取优惠券奖励
     * @param \App\Models\SharingRule $rule
     * @param \App\Models\Order\Order $order
     * @param \App\Models\Enums\Reward\RewardFansTypeEnum $fans_type
     * @return int|null
     */
    public function getCouponReward(SharingRule $rule, Order $order, RewardFansTypeEnum $fans_type)
    {
        // 获取规则内容
        $rule_content = Arr::get($rule->rules, $fans_type->value, []);
        if ($rule_content) {
            if ($order->is_first_order) {
                // 首单
                $order_rule = Arr::get($rule_content, 'first_order', []);
            } else {
                // 非首单
                $order_rule = Arr::get($rule_content, 'other_order', []);
            }

            $coupon_id = Arr::get($order_rule, 'coupon_id');
            return $coupon_id;
        }

        return null;
    }
}
