<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('product_recommends', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->comment('主商品ID');
            $table->unsignedBigInteger('recommend_product_id')->comment('推荐商品ID');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('recommend_product_id')->references('id')->on('products')->onDelete('cascade');
            $table->unique(['product_id', 'recommend_product_id']);
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('product_recommends');
    }
}; 