<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_coupons', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->dateTime('used_at')->nullable()->comment('使用时间');
            $table->bigInteger('coupon_grant_rule_id')->nullable()->comment('发放规则id');
            $table->bigInteger('coupon_id')->comment('优惠券id');
            $table->string('code')->comment('优惠券code');
            $table->dateTime('effective_start_at')->nullable()->comment('有效期（固定开始时间）');
            $table->dateTime('effective_end_at')->nullable()->comment('有效期（固定过期时间）');
            $table->decimal('random_price', 8, 2)->nullable()->comment('随机优惠券的价格');
            $table->boolean('is_read')->default(false)->comment('是否已读');
            $table->boolean('is_framed')->default(false)->comment('是否已效');
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('用户持有优惠券列表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_coupons');
    }
};
