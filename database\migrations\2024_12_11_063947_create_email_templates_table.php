<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('标题');
            $table->string('title', 64)->nullable()->comment('邮件标题');
            $table->tinyInteger('type')->comment('类型');
            $table->boolean('enabled')->default(false)->comment('开启状态');
            $table->text('content')->comment('内容');
            $table->json('data')->comment('支持绑定模板数据');
            $table->string('desc')->nullable()->comment('描述');
            $table->bigInteger('admin_user_id')->nullable()->index()->comment('更新人');
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('邮件发送模板');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
