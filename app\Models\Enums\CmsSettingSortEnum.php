<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum CmsSettingSortEnum: int
{
    case sortType = 1;
    case sortPublished = 2;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::sortType->value => '按发布时间',
            self::sortPublished->value => '按指定位置',
        ];
    }

}
