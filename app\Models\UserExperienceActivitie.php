<?php

namespace App\Models;

use App\Models\ActivitieProduct;
use App\Models\Product\Product;
use App\Models\UserExperienceActivitieCoupon;
use App\Models\Coupon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserExperienceActivitie extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    public $timestamps = true;
    protected $guarded = [];

    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

    // Accessor for is_listed
    public function getIsListedAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_coupon
    public function getIsCouponAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_first
    public function getIsFirstAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_automatic_listed
    public function getIsAutomaticListedAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_register
    public function getIsRegisterAttribute($value)
    {
        return (bool) $value;
    }

    //关联体验活动产品信息
    public function activitieProducts()
    {
        return $this->hasMany(ActivitieProduct::class);
    }
    //关联体验活动优惠券信息
    public function UserExperienceActivitieCoupons()
    {
        return $this->hasMany(UserExperienceActivitieCoupon::class);
    }

    // 定义与产品关系
    public function products()
    {
        return $this->belongsToMany(Product::class, ActivitieProduct::class, 'user_experience_activitie_id', 'product_id')
            ->withPivot('type', 'price', 'percentage', 'currency_id');
    }

    // 定义与优惠券关系
    public function coupons()
    {
        return $this->belongsToMany(Coupon::class, UserExperienceActivitieCoupon::class, 'user_experience_activitie_id', 'coupon_id');
    }

    //更新人信息
    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class);
    }

}
