<?php

namespace App\Console\Commands\Order;

use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use Illuminate\Console\Command;

class OrderTimeoutCancel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:timeout-cancel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '超时付款订单自动取消';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 超过12小时自动取消
        $lastAt = now()->subHours(12);
        Order::query()
            ->where('created_at', '<=', $lastAt)
            ->where('status', OrderStatusEnum::Unpaid)
            ->chunkMap(function (Order $order) {
                $order->update([
                    'status' => OrderStatusEnum::Cancel
                ]);
            });

    }
}
