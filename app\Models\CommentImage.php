<?php

namespace App\Models;

// use App\Constants\QueueKey;
// use App\Jobs\UpdateCommentHasImages;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CommentImage extends MiddleModel
{
    use HasFactory;
    protected $guarded = [];

    // public static function booted()
    // {
    //     parent::booted();
    //     static::created(function (CommentImage $comment) {
    //         UpdateCommentHasImages::dispatch($comment->comment_id)->onQueue(QueueKey::Product->value);
    //     });
    // }
}
