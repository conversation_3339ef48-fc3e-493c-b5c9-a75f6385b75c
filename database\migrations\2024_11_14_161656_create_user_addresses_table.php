<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_addresses', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->string('first_name', 64);
            $table->string('last_name', 64);
            $table->bigInteger('country_id');
            $table->string('address', 512);
            $table->string('address1', 512);
            $table->string('state');
            $table->string('city');
            $table->string('zip');
            $table->boolean('is_billing_default')->default(false);
            $table->boolean('is_shipping_default')->default(false);
            $table->boolean('is_both_default')->default(false);
            $table->string('phone', 32)->comment('customer phone number');

            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('用户地址');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_addresses');
    }
};
