<?php

namespace Database\Seeders;

use App\Models\Enums\AttachmentModuleEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BannerSeeder extends Seeder
{
    public function run()
    {
        $imageIds = [];
        for ($i = 1; $i <= 3; $i++) {
            $imageIds[] = DB::table('attachments')->insertGetId([
                'file_name' => '838614b5d764b4b3206cedf9376700b66348ae69-600.jpeg',
                'file_type' => 'image/jpeg',
                'file_size' => '123345',
                'upload_time' => now(),
                'module' => AttachmentModuleEnum::Ad,
                'file_hash' => '',
                'path' => '202411/files/' . time() . $i . '.jpeg',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $groupIds = [];
        for ($i = 1; $i <= 5; $i++) {
            $groupIds[] = DB::table('banner_groups')->insertGetId([
                'name' => 'Group ' . $i,
                'slug' => Str::slug('Group ' . $i),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        for ($i = 1; $i <= 5; $i++) {
            $bannerId = DB::table('banners')->insertGetId([
                'title' => 'Banner ' . $i,
                'url' => 'https://www.baidu.com/' . $i,
                'sort' => $i,
                'is_visible' => 1,//$i % 2 === 0,
                'image_id' => $imageIds[array_rand($imageIds)],
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $randomGroups = array_rand($groupIds, rand(1, 3));
            $randomGroups = is_array($randomGroups) ? $randomGroups : [$randomGroups];

            foreach ($randomGroups as $groupIndex) {
                DB::table('banner_group_relations')->insert([
                    'banner_id' => $bannerId,
                    'group_id' => $groupIds[$groupIndex],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        $this->command->info('Seeded: 5 banners, 5 groups, and their relations.');
    }
}
