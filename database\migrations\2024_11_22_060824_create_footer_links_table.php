<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('footer_links', function (Blueprint $table) {
            $table->id();
            $table->foreignId('footer_id')->constrained('footers')->cascadeOnDelete();
            $table->string('label')->comment('链接名称');
            $table->string('url')->comment('链接地址');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('footer明细配置');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('footer_links');
    }
};
