<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Http\Resources\OrderItemResource;
use App\Models\Attachment;
use App\Models\Cart;
use App\Models\Enums\AttachmentModuleEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use App\Models\SharingRule;
use App\Models\User\User;
use App\Models\User\UserAddress;
use App\Models\User\UserCoupon;
use App\Models\User\UserWalletInviteAmountRecord;
use App\Models\User\UserWalletPointRecord;
use Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use ZipArchive;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Unique;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Database\Eloquent\Builder;


class UserController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::UsersUpdate)->only(['update']);
        $this->hasPermissionOr(Permissions::UsersUpdate, Permissions::UsersIndex)->only(['index', 'show', 'export']);
    }


    /**
     * 用户列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(User::class, $request)
            ->with([
                'invitersRule:id,title'
            ])
            ->whereNull('deleted_at')
            ->allowedFilters([
                // AllowedFilter::partial('first_name'),
                // AllowedFilter::partial('last_name'),

                // 新增：合并搜索 first_name + last_name
                AllowedFilter::callback('name', function (Builder $query, $value) {
                    $query->where(function (Builder $query) use ($value) {
                        $query->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'LIKE', "%{$value}%")
                            ->orWhere(DB::raw("CONCAT(last_name, ' ', first_name)"), 'LIKE', "%{$value}%");
                    });
                }),
                AllowedFilter::partial('status'),
                AllowedFilter::partial('phone'),
                AllowedFilter::partial('email'),
                AllowedFilter::scope('created_from'),
                AllowedFilter::scope('created_to'),
                AllowedFilter::partial('created_at'),
            ])
            ->allowedSorts(['id', 'created_at', 'email', 'first_name', 'last_name'])
            ->defaultSort('-created_at');//正序渠道-
        $res = $builder->paginate($this->getPerPage());

        array_map(function (User $user) {
            //            $user->created_at_prc = $user->created_at->toDateTimeString();
            $user->is_guest = $user->isGuest();
            $user->invite_code = hashids()->encode($user->id);
        }, $res->items());

        return JsonResource::collection($res);
    }

    /**
     * 用户导出
     * @param Request $request
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request): Response|BinaryFileResponse
    {
        $builder = QueryBuilder::for(User::class, $request)
            ->whereNull('deleted_at')
            ->allowedFilters([
                AllowedFilter::partial('first_name'),
                AllowedFilter::partial('last_name'),
                AllowedFilter::partial('status'),
                AllowedFilter::partial('phone'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');

        $excel = new \App\Exports\UserExport($builder->getEloquentBuilder());
        return $excel->download('users.xlsx');
    }

    public function show(User $user): JsonResource
    {
        // 加载关联的分享规则数据
        $user->load('invitersRule:id,title');
        // 年龄
        $user->age = Carbon::parse($user->birth_date)->age;
        // 生成邀请码
        $user->invite_code = hashids()->encode($user->id);
        // 用户类型
        $user->is_guest = $user->isGuest();
        return JsonResource::make($user);
    }

    /**
     * 修改
     * @param Request $request
     * @param User $user
     * @return JsonResource
     */
    public function update(Request $request, User $user): JsonResource
    {
        $validated = $request->validate([
            'avatar' => ['nullable', 'string', 'max:512'],
            'first_name' => ['required', 'string'],
            'last_name' => ['required', 'string'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                (new Unique(User::class))->ignore($user->id)->where(function ($query) {
                    $query->whereNull('deleted_at');
                })
            ],
            'email_preferences' => ['required', new Enum(UserEmailPreferencesEnum::class)],
            'password' => ['nullable', 'string', 'max:32', 'min:6'],
            'birth_date' => ['nullable', 'date', 'before:today'],
            'status' => [new Enum(UserStatusEnum::class)],
            'inviters_rule_id' => ['nullable', new Exists(SharingRule::class, 'id')],
        ]);

        // 密码
        if (!Arr::get($validated, 'password')) {
            // 移除
            Arr::forget($validated, 'password');
        }
        $user->update($validated);

        return JsonResource::make($user);
    }


    /**
     * 上传图片
     * @param Request $request
     * @return JsonResource
     */
    public function upload(Request $request): JsonResource
    {
        $data = $request->validate([
            'file' => ['required', 'file', 'max:40960000', 'mimes:jpeg,png,jpg,gif,mp4'],
            'module' => ['string', new Enum(AttachmentModuleEnum::class)],
            'disk' => ['string', 'max:255'],
            'file_name' => 'nullable|string|max:255',
        ], [
            'file.max' => '文件最大支持4G'
        ]);

        // 图片
        $file = Arr::get($data, 'file');
        $model = Arr::get($data, 'module', AttachmentModuleEnum::Other->value);
        /**
         * @var UploadedFile $file
         */
        $imagePath = AttachmentModuleEnum::tryFrom($model)->getPath();
        $datePath = Carbon::today()->format("Ym");
        $fileHash = md5_file($file->getRealPath());  // 获取文件的哈希值
        $disk = config('filesystems.default');
        if (Arr::get($data, 'disk')) {
            $disk = Arr::get($data, 'disk');
        }
        $existingFile = Attachment::query()
            ->where('file_hash', $fileHash)
            ->where('module', $model)
            ->where('disk', $disk)
            ->where('path', '!=', "0")
            ->first();
        if ($existingFile) {
            return JsonResource::make($existingFile);
        }
        try {
            $src = $file->store($imagePath . $datePath, $disk);
            if (!$src) {
                throw new DataException('Upload failed.');
            }
            $attachment = new Attachment();
            $attachment->fill([
                'file_name' => Arr::get($data, 'file_name', $file->getClientOriginalName()),
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'upload_time' => now(),
                'file_hash' => $fileHash,
                'module' => $model,
                'disk' => $disk,
                'path' => Str::replace($imagePath, '', $src),
                'user_id' => Auth::id(),
            ])->save();
        } catch (\Exception $e) {
            throw new DataException($e->getMessage());
        }

        return JsonResource::make($attachment);
    }

    //上传分片视频
    public function uploadChunk(Request $request): JsonResource
    {
        $data = $request->validate([
            'file' => ['required', 'file', 'max:40960000', 'mimes:zip'],
            'module' => ['string', new Enum(AttachmentModuleEnum::class)],
            'disk' => ['string', 'max:255'],
            'file_name' => 'nullable|string|max:255',
        ], [
            'file.max' => '文件最大支持4G',
        ]);

        /** @var \Illuminate\Http\UploadedFile $file */
        $file = Arr::get($data, 'file');
        $fileName = $file->getClientOriginalName(); // 先获取原始文件名
        $fileSize = $file->getSize(); // 先获取文件大小
        $model = Arr::get($data, 'module', AttachmentModuleEnum::Video->value);
        $disk = Arr::get($data, 'disk', config('filesystems.default'));

        // 计算文件哈希，防止重复上传
        $fileHash = md5_file($file->getRealPath());
        $existingFile = Attachment::query()
            ->where('file_hash', $fileHash)
            ->where('module', $model)
            ->where('disk', $disk)
            ->where('path', '!=', "0")
            ->first();

        if ($existingFile) {
            return JsonResource::make($existingFile);
        }

        // **先把 ZIP 文件存到本地**
        $localZipPath = storage_path('app/temp_zips/' . uniqid() . '.zip');
        $file->move(dirname($localZipPath), basename($localZipPath));

        // **解压 ZIP**
        $extractPath = storage_path('app/temp_extracted/' . uniqid());
        mkdir($extractPath, 0777, true);

        $zip = new ZipArchive;
        if ($zip->open($localZipPath) === true) {
            $zip->extractTo($extractPath);
            $zip->close();
        } else {
            throw new DataException('Failed to extract video files.');
        }

        // **查找 .m3u8 文件**
        $m3u8File = collect(scandir($extractPath))->first(fn($f) => str_ends_with($f, '.m3u8'));
        if (!$m3u8File) {
            throw new DataException('M3U8 file not found in the ZIP.');
        }

        // **上传 .m3u8 和 .ts 文件到 S3**
        $s3VideoPath = AttachmentModuleEnum::tryFrom($model)->getPath();
        $strPath = uniqid();
        foreach (scandir($extractPath) as $file) {
            if (str_ends_with($file, '.ts') || str_ends_with($file, '.m3u8')) {
                Storage::disk($disk)->put("{$s3VideoPath}{$strPath}/{$file}", file_get_contents("{$extractPath}/{$file}"));
            }
        }

        // 计算 S3 上的 .m3u8 文件路径
        $m3u8S3Path = "{$strPath}/{$m3u8File}";

        // **保存记录到数据库，仅存储 m3u8 路径**
        $attachment = new Attachment();
        $attachment->fill([
            'file_name' => Arr::get($data, 'file_name', $fileName),
            'file_type' => 'video/m3u8',
            'file_size' => $fileSize,
            'upload_time' => now(),
            'file_hash' => $fileHash,
            'module' => $model,
            'disk' => $disk,
            'path' => $m3u8S3Path, // 仅存 m3u8 路径
            'user_id' => null,
        ])->save();

        // **删除本地临时文件**
        unlink($localZipPath);
        array_map('unlink', glob("{$extractPath}/*"));
        rmdir($extractPath);

        return JsonResource::make($attachment);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(User::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        User::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now(),
            ]);
        return response()->json();
    }

    /**
     * 移出黑名单
     * @param Request $request
     * @return JsonResponse
     */
    public function removeBlackList(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(User::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 移出黑名单
        User::query()->whereIn('id', $ids)
            ->where('status', '=', UserStatusEnum::StatusDisabled)
            ->update([
                'status' => UserStatusEnum::StatusEnable,
            ]);
        return response()->json();
    }

    /**
     * 订单
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function order(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(Order::class)
            ->with([
                'shipping:id,name,type',
                'items'
            ])
            ->select([
                '*',
                DB::raw('ROUND(total, 2) as total'),
            ])
            ->where('user_id', $user->id)
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        // 执行查询并获取分页结果
        $paginator = $builder->paginate($this->getPerPage());

        // 转换每个订单项，添加总商品数
        $paginator->getCollection()->transform(function ($order) {
            // 手动计算商品总数
            $totalItems = 0;
            if ($order->items) {
                foreach ($order->items as $item) {
                    $totalItems += $item->num;
                }
            }

            // 添加总商品数字段
            $order->items_sum_num = $totalItems;

            return $order;
        });

        return JsonResource::make($paginator);
    }

    /**
     * 购物车
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function cart(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(Cart::class)
            ->with([
                'shipping:id,name,type'
            ])
            ->where('user_id', $user->id)
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        return JsonResource::make($builder->paginate($this->getPerPage()));
    }

    /**
     * 购买过的产品
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function bought(Request $request, User $user): JsonResource
    {
        // 外层查询：预加载 product 关联数据
        $builder = QueryBuilder::for(OrderItem::class)
            ->whereHas('order', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with([
                'product',
                'product.colorAttribute:id,value',
                'product.sizeAttribute:id,value'
            ])
            ->defaultSort('-created_at');

        return JsonResource::make($builder->paginate($this->getPerPage()));
    }

    /**
     * 地址
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function address(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(UserAddress::class)
            ->with([
                'country:id,name',
            ])
            ->where('user_id', $user->id)
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        return JsonResource::make($builder->paginate($this->getPerPage()));
    }

    /**
     * 优惠劵
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function coupon(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(UserCoupon::class)
            ->with([
                'coupon'
            ])
            ->where('user_id', $user->id)
            ->where('used_at', null)
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        return JsonResource::make($builder->paginate($this->getPerPage()));
    }
    /**
     * 用户积分变更记录
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function pointRecords(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(UserWalletPointRecord::class)
            ->where('user_id', $user->id)
            ->allowedFilters([
                'change_type',
                AllowedFilter::scope('created_from'),
                AllowedFilter::scope('created_to'),
            ])
            ->allowedSorts(['created_at', 'change_point'])
            ->defaultSort('-created_at');

        return JsonResource::make($builder->paginate($this->getPerPage()));
    }

    /**
     * 用户钱包金额变更记录
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function walletRecords(Request $request, User $user): JsonResource
    {
        $builder = QueryBuilder::for(UserWalletInviteAmountRecord::class)
            ->where('user_id', $user->id)
            ->allowedFilters([
                'change_type',
                AllowedFilter::scope('created_from'),
                AllowedFilter::scope('created_to'),
            ])
            ->allowedSorts(['created_at', 'change_amount'])
            ->defaultSort('-created_at')
            ->with([
                'withdraw' => function ($query) {
                    $query->select('id', 'user_id', 'status', );
                }
            ])
            ->select([
                'user_id',
                'user_wallet_id',
                'change_type',
                DB::raw('ROUND(change_amount, 2) as change_amount'),
                DB::raw('ROUND(current_amount, 2) as current_amount'),
                DB::raw('ROUND(before_amount, 2) as before_amount'),
                'source_type',
                'source_id',
                'desc',
                'created_at',
                'updated_at'
            ]);

        return JsonResource::make($builder->paginate($this->getPerPage()))
            ->additional([
                'message' => 'Success',
                'code' => 200
            ]);
    }
    /**
     * 部分修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function patchUpdate(Request $request, User $user): JsonResource
    {
        $validated = $request->validate([
            'status' => [''],
            'email_preferences' => ['nullable', new Enum(UserEmailPreferencesEnum::class)],
        ]);

        $validated && $user->update($validated);
        return JsonResource::make($user);
    }

    /**
     * 选择地址
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User\User $user
     * @return JsonResource
     */
    public function options(Request $request): JsonResource
    {
        $res = QueryBuilder::for(User::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('email'),
            ])
            ->select([DB::raw('email as label'), DB::raw('id as value')])
            ->get();

        return JsonResource::make($res);
    }

    /**
     * 清理缓存
     * @return JsonResponse|mixed
     */
    public function cacheClear()
    {
        if (cachePartialDelete("*") === false) {
            throw new DataException('清理缓存失败');
        }
        return response()->json();
    }
    /**
     * 修改用户状态
     * @param Request $request
     * @param User $user
     * @return JsonResource
     */
    public function changeStatus(Request $request, User $user): JsonResource
    {
        $validated = $request->validate([
            'status' => ['required', new Enum(UserStatusEnum::class)],
        ]);

        $user->update(['status' => $validated['status']]);

        return JsonResource::make($user);
    }
}
