<?php

namespace App\Jobs\Order;

use App\Models\Country;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Order\Order;
use App\Models\State;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Throwable;

class OrderSyncPaypalAddress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 更新订单地址 从paypal
     */
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->order->loadMissing(['paypalOrder']);
            $paypalOrder = $this->order->paypalOrder;
            $address = Arr::get($paypalOrder->detail, 'purchase_units.0.shipping');
            $countryId = Country::query()->where('iso_code', Arr::get($address, 'address.country_code'))->value('id');
            $state = State::query()->where('country_id', $countryId)->where('iso_code', Arr::get($address, 'address.admin_area_1'))->value('name');
            $baseAddress = [
                'first_name' => Str::before(Arr::get($address, 'name.full_name'), ' '),
                'last_name' => Str::after(Arr::get($address, 'name.full_name'), ' '),
                'zip' => Arr::get($address, 'address.postal_code'),
                'address' => Arr::get($address, 'address.address_line_1'),
                'country_id' => $countryId ?: 21,
                'state' => $state,
                'city' => Arr::get($address, 'address.admin_area_2'),
            ];
            $this->order->billingAddress()->create([
                'type' => OrderAddressTypeEnum::Billing,
                ...$baseAddress
            ]);
            $this->order->shippingAddress()->create([
                'type' => OrderAddressTypeEnum::Shipping,
                ...$baseAddress
            ]);
            $this->order->refresh();
            
        } catch (Throwable $exception) {
            logger()->error('OrderSyncPaypalAddress Error:' . $exception->getMessage());
            logger()->error('OrderSyncPaypalAddress ID:' . $this->order->id);
        }
    }
}
