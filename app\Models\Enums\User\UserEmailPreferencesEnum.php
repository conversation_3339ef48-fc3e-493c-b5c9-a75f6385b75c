<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum UserEmailPreferencesEnum: int
{
    case  None = 0;
    case  TypicallyEveryday = 1;
    case  FewTimesWeek = 2;
    case  FewTimesMonth = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::None->value => 'Not subscribed',
            self::TypicallyEveryday->value => 'Typically everyday',
            self::FewTimesWeek->value => 'A few times a week',
            self::FewTimesMonth->value => 'A few times a month',
        ];
    }

}
