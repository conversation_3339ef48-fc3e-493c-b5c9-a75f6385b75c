<?php

namespace App\Models\Order;

use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Jobs\CreateUserInviteReward;
use App\Jobs\EmailRuleNotice;
use App\Jobs\Order\OrderHistoryRecord;
use App\Jobs\Order\OrderReleaseProductStock;
use App\Jobs\OrderSaveUserAddress;
use App\Jobs\Order\OrderIsFirstOrder;
use App\Jobs\Order\ProcessMembershipRewards;
use App\Jobs\Product\ProductSyncSaleNum;
use App\Models\Currency;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Payment\Payment;
use App\Models\Order\Payment\PaypalOrder;
use App\Models\Shipping;
use App\Models\User\UserInviteReward;
use App\Models\User\User;
use App\Utils\PrecisionMath;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use DB;

/**
 * @property int $user_id
 * @property OrderStatusEnum $status
 * @property OrderPaidTypeEnum $paid_type
 * @property Carbon $paid_at
 * @property bool $closed
 * @property OrderPaidStatusEnum $paid_status
 * @property ?PaypalOrder $paypalOrder
 * @property Payment $payment
 * @property int $id
 * @property float $shipping_fee
 * @property string $no
 * @property string $shipping_number
 * @property string $email
 * @property OrderAddress $billingAddress
 * @property OrderAddress $shippingAddress
 * @property bool $subscribed
 * @property UserInviteReward $inviteRewards
 */
class Order extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'paid_at' => 'datetime',
        'status' => OrderStatusEnum::class,
        'paid_type' => OrderPaidTypeEnum::class,
        'paid_status' => OrderPaidStatusEnum::class,
        'shipping_fee' => 'decimal:2',
        'shipping_fee_discount' => 'decimal:2',
        'shipping_fee_currency' => 'decimal:2',
        'subtotal_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'subscribed' => 'bool',
        'valid' => 'bool',
        'paid_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'coupon_discount_price' => 'decimal:2',
        'product_info' => 'json',
        'total_price_currency' => 'decimal:2',
        'coupon_discount_price_currency' => 'decimal:2',
        'shipping_fee_currency' => 'decimal:2',
        'shipping_fee_discount_currency' => 'decimal:2',
    ];

    protected $appends = [
        'delivery_fee_real',
        'delivery_fee_real_agent',
        'shipping_fee_agent',
        'shipping_fee_discount_agent',
        'subtotal_price_agent',
        'total_price_agent',
        'discount_price_agent',
        'total_agent',
        'payment_method',
    ];

    public static function booted()
    {
        static::creating(function (self $model) {
            // 生成订单号 ARD+时间戳到秒+随机数字3位
            $model->no ??= 'ARD' . date('YmdHis') . mt_rand(100, 999);
            $model->user_type = $model->user?->isGuest() ? 1 : 0;

            // 计算当前币种的运费
            if ($model->shipping_fee > 0) {
                $model->updateShippingFeeCurrency();
            }
            if ($model->shipping_fee_discount > 0) {
                $model->updateShippingFeeDiscountCurrency();
            }
            if ($model->total > 0) {
                $model->updatePaidAmountCurrency();
            }
        });
        static::saving(function (self $model) {
            if ($model->isDirty(['status'])) {
                // 是否为有效订单
                $model->valid = $model->status->valid();
                // 订单状态变更
                OrderHistoryRecord::dispatch($model->status, $model)->onQueue(QueueKey::Order->value);
            }

            // 如果运费发生变化，重新计算当前币种的运费
            if ($model->isDirty(['shipping_fee'])) {
                $model->updateShippingFeeCurrency();
            }
            // 如果运费折扣发生变化，重新计算当前币种的运费折扣
            if ($model->isDirty(['shipping_fee_discount'])) {
                $model->updateShippingFeeDiscountCurrency();
            }
            if ($model->isDirty(['total'])) {
                $model->updatePaidAmountCurrency();
            }
        });
        static::saved(function (self $model) {
            if ($model->isDirty(['status'])) {
                // 是否首单
                OrderIsFirstOrder::dispatch($model)->onQueue(QueueKey::Order->value);
                // 取消或者退款
                if (in_array($model->status, [OrderStatusEnum::Refund, OrderStatusEnum::Cancel])) {
                    // 释放库存
                    OrderReleaseProductStock::dispatch($model)->onQueue(QueueKey::Order->value);
                }

                // 已付款
                if ($model->status == OrderStatusEnum::Paid) {
                    // 加载订单详情和相关数据
                    $model->load([
                        'items.product',
                        'billingAddress',
                        'shippingAddress',
                        'currency',
                        'user',
                        'items.userCoupon'
                    ]);
                    // 扣掉个人优惠券
                    DB::afterCommit(function () use ($model) {
                        // 只有登录用户才需要处理优惠券相关操作
                        if ($model->user_id) {
                            $model->items()->with(['userCoupon'])->each(function (OrderItem $orderItem) {
                                $orderItem?->userCoupon?->update([
                                    'used_at' => now()
                                ]);
                            });
                            $model->items()->with(['coupon'])->each(function (OrderItem $orderItem) use ($model) {
                                // 更新全局优惠券使用次数
                                if ($orderItem->coupon && $orderItem->coupon->is_global && !$orderItem->coupon->is_auto) {

                                    // 检查用户是否已有此优惠券记录
                                    $existingUserCoupon = DB::table('user_coupons')->where('user_id', $model->user_id)
                                        ->where('coupon_id', $orderItem->coupon->id)
                                        ->first();

                                    // 如果不存在，则创建一条记录
                                    if (!$existingUserCoupon) {
                                        $userCouponId = DB::table('user_coupons')->insertGetId([
                                            'user_id' => $model->user_id,
                                            'coupon_id' => $orderItem->coupon->id,
                                            'code' => $orderItem->coupon->code,
                                            'effective_start_at' => $orderItem->coupon->effective_start_at,
                                            'effective_end_at' => $orderItem->coupon->effective_end_at,
                                            'used_at' => now(), // 标记为已使用
                                            'created_at' => now(),
                                            'updated_at' => now()
                                        ]);

                                        // 更新订单项，关联新创建的用户优惠券
                                        $orderItem->update(['user_coupon_id' => $userCouponId]);
                                        $orderItem->coupon->decrement('total_count');
                                    }
                                }
                            });
                        }
                    });
                    // 同步产品数量
                    ProductSyncSaleNum::dispatch($model)->onQueue(QueueKey::Product->value);
                    // 同步订单地址  不用列队，他们需要同步账单地址的时候直接同步user
                    // OrderSaveUserAddress::dispatch($model)->onQueue(QueueKey::Order->value);
                    // 首单奖励优惠券发放埋点 不用列队，他们需要直接发放优惠劵
                    // CouponGrantCheck::dispatch(CouponGrantType::FirstOrder, $model)->onQueue(QueueKey::Coupon->value);
                    if ($model->user && $model->user->parent_id) {
                        // 创建订单返现奖励
                        CreateUserInviteReward::dispatch(OrderStatusEnum::Paid, $model)->onQueue(QueueKey::Order->value);
                    }
                    // 处理会员等级升级和积分奖励
                    ProcessMembershipRewards::dispatch($model)->onQueue(QueueKey::Order->value);
                    // 分享邀请返佣埋点
                    CouponGrantCheck::dispatch(CouponGrantType::ShareInvite, $model)->onQueue(QueueKey::Coupon->value);
                    // 分享邀请返佣埋点 - 只对登录用户执行
                    if ($model->user) {
                        CouponGrantCheck::dispatch(CouponGrantType::ShareInvite, $model)->onQueue(QueueKey::Coupon->value);
                    }


                    // 邮件规则推送 - 支付成功
                    $user = Auth::user(); 
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::OrderPaidSuccess, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                }
                if ($model->status == OrderStatusEnum::Received) {
                    $user = Auth::user();
                    // 邮件规则推送 - 物流更新
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::LogisticsInspection, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                    // 邮件规则推送 - 邀请评论
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::InviteComment, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                }
                if ($model->status == OrderStatusEnum::Cancel) {
                    // 取消订单返现奖励
                    CreateUserInviteReward::dispatch(OrderStatusEnum::Cancel, $model)->onQueue(QueueKey::Order->value);
                }
                // 订单状态变更
                OrderHistoryRecord::dispatch($model->status, $model)->onQueue(QueueKey::Order->value);
            }
        });
    }

    /**************** 关联模型 ****************/
    // 支付信息
    public function payment(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function paypalOrder(): HasOne
    {
        return $this->hasOne(PaypalOrder::class);
    }

    public function billingAddress(): HasOne
    {
        return $this->hasOne(OrderAddress::class)->where('type', OrderAddressTypeEnum::Billing);
    }

    public function shippingAddress(): HasOne
    {
        return $this->hasOne(OrderAddress::class)->where('type', OrderAddressTypeEnum::Shipping);
    }

    public function shipping()
    {
        return $this->belongsTo(Shipping::class);
    }

    public function inviteRewards()
    {
        return $this->hasMany(UserInviteReward::class);
    }

    public function histories(): HasMany
    {
        return $this->hasMany(OrderHistory::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**************** 金额币种转换 ****************/
    public function deliveryFeeRealAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice(
                PrecisionMath::sub($this->shipping_fee, $this->shipping_fee_discount),
                $this->currency
            )
        );
    }

    public function shippingFeeAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->shipping_fee, $this->currency)
        );
    }

    public function shippingFeeDiscountAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->shipping_fee_discount, $this->currency)
        );
    }

    public function subtotalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->subtotal_price, $this->currency)
        );
    }

    public function totalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->total_price, $this->currency)
        );
    }

    public function discountPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->discount_price, $this->currency)
        );
    }

    public function totalAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->total, $this->currency)
        );
    }

    public function paymentMethod(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->paid_type?->desc() ?? '未支付',
        );
    }

    /**
     * 更新当前币种对应的运费
     */
    private function updateShippingFeeCurrency(): void
    {
            $currentCurrency = currentCurrency();
            $this->shipping_fee_currency = convertPrice($this->shipping_fee, $currentCurrency);

    }

    private function updatePaidAmountCurrency(): void
    {
            $currentCurrency = currentCurrency();
            $this->paid_amount = convertPrice($this->total, $currentCurrency);
            $this->total_price_currency = convertPrice($this->total_price, $currentCurrency);
            $this->coupon_discount_price_currency = convertPrice($this->coupon_discount_price, $currentCurrency);
            
    }

    private function updateShippingFeeDiscountCurrency(): void
    {
            $currentCurrency = currentCurrency();
            $this->shipping_fee_discount_currency = convertPrice($this->shipping_fee_discount, $currentCurrency);

    }

    // 运费实际金额
    public function deliveryFeeReal(): Attribute
    {
        return Attribute::get(
            fn() => PrecisionMath::toFloat(
                PrecisionMath::sub($this->shipping_fee, $this->shipping_fee_discount)
            )
        );
    }

    public function syncAddressToUser($address, $isBilling = true)
    {
        $user = $address->order->user;
        if (!$user) {
            return;
        }

        $addressData = Arr::only($address->toArray(), ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip', 'phone']);

        // 检查是否已存在相同的地址
        $existingAddress = $user->addresses()
            ->where('first_name', $addressData['first_name'])
            ->where('last_name', $addressData['last_name'])
            ->where('country_id', $addressData['country_id'])
            ->where('address', $addressData['address'])
            ->where('city', $addressData['city'])
            ->where('zip', $addressData['zip'])
            ->where('phone', $addressData['phone'])
            ->first();

        if (!$existingAddress) {
            // 地址不存在，创建新地址
            if ($isBilling) {
                $addressData['is_billing_default'] = true;
                $addressData['is_shipping_default'] = false;
                $user->addresses()->update(['is_billing_default' => false, 'is_both_default' => false]);
            } else {
                $addressData['is_shipping_default'] = true;
                $addressData['is_billing_default'] = false;
                $user->addresses()->update(['is_shipping_default' => false, 'is_both_default' => false]);
            }

            $addressData['is_both_default'] = false;
            $user->addresses()->create($addressData);
        }
    }
}
