<?php

namespace App\Http\Controllers;

use App\Exceptions\DataException;
use App\Models\Enums\SysConfigKeyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Attachment;

class SysConfigController extends Controller
{

    /**
     * 获取配置详情
     * @param $key
     * @return JsonResource
     * @throws DataException
     */
    public function show($key): JsonResource
    {
        $key = SysConfigKeyEnum::tryFrom($key);
        if (empty($key)) {
            throw new DataException("参数错误");
        }
        if($key == SysConfigKeyEnum::ModalConfig){
            $value = sysConfigService()->get($key);
            // 获取图片id,path,disk,module
            $value['modal1']['image'] = Attachment::where('id', $value['modal1']['image_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['image'] = Attachment::where('id', $value['modal2']['image_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['image_app'] = Attachment::where('id', $value['modal2']['image_app_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['logo_image_first'] = Attachment::where('id', $value['modal2']['logo_image_first_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['logo_image_second'] = Attachment::where('id', $value['modal2']['logo_image_second_id'])->select('id', 'path', 'disk', 'module')->first();
        }else{
            $value = sysConfigService()->get($key);
        }
        return JsonResource::make($value);
    }
}
