<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderPaidTypeEnum: int
{
    case  PayPal = 1;
    case  Bank = 2;

    public function desc(): string
    {

        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::PayPal->value => 'payPal',
            self::Bank->value => '信用卡',
        ];
    }

}
