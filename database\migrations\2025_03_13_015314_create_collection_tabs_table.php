<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collection_tabs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('collection_id')->index()->comment('分类ID');
            $table->bigInteger('tab_id')->index()->comment('标签分类ID');
            $table->unique(['collection_id', 'tab_id']);
            $table->engine('InnoDB');
            $table->comment('标签分类表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collection_tabs');
    }
};
