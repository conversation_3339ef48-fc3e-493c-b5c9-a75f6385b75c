<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum PointChangeTypeEnum: string
{
    // 积分增加类型
    case OrderReward = 'order_reward';           // 订单奖励
    case TaskReward = 'task_reward';             // 任务奖励
    case BirthdayReward = 'birthday_reward';     // 生日奖励
    case PromotionReward = 'promotion_reward';   // 活动奖励
    case ManualAdd = 'manual_add';               // 手动添加
    case RefundReward = 'refund_reward';         // 退款奖励
    case InviteReward = 'invite_reward';         // 邀请奖励
    
    // 积分减少类型
    case CouponExchange = 'coupon_exchange';     // 兑换优惠券
    case ProductExchange = 'product_exchange';   // 兑换商品
    case ManualDeduct = 'manual_deduct';         // 手动扣除
    case OrderCancel = 'order_cancel';           // 订单取消扣除
    case Expire = 'expire';                      // 积分过期

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            // 积分增加
            self::OrderReward->value => '订单奖励',
            self::TaskReward->value => '任务奖励',
            self::BirthdayReward->value => '生日奖励',
            self::PromotionReward->value => '活动奖励',
            self::ManualAdd->value => '手动添加',
            self::RefundReward->value => '退款奖励',
            self::InviteReward->value => '邀请奖励',
            
            // 积分减少
            self::CouponExchange->value => '兑换优惠券',
            self::ProductExchange->value => '兑换商品',
            self::ManualDeduct->value => '手动扣除',
            self::OrderCancel->value => '订单取消扣除',
            self::Expire->value => '积分过期',
        ];
    }

    /**
     * 获取积分增加类型
     */
    public static function getIncreaseTypes(): array
    {
        return [
            self::OrderReward,
            self::TaskReward,
            self::BirthdayReward,
            self::PromotionReward,
            self::ManualAdd,
            self::RefundReward,
            self::InviteReward,
        ];
    }

    /**
     * 获取积分减少类型
     */
    public static function getDecreaseTypes(): array
    {
        return [
            self::CouponExchange,
            self::ProductExchange,
            self::ManualDeduct,
            self::OrderCancel,
            self::Expire,
        ];
    }

    /**
     * 是否为积分增加类型
     */
    public function isIncrease(): bool
    {
        return in_array($this, self::getIncreaseTypes());
    }

    /**
     * 是否为积分减少类型
     */
    public function isDecrease(): bool
    {
        return in_array($this, self::getDecreaseTypes());
    }
}
