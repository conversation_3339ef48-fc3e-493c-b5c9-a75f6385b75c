<?php

namespace App\Console\Commands;

use App\Imports\CommentsImport;
use App\Models\Enums\ImportCommentStatusEnum;
use App\Models\ImportComment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class CommentImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:comment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入商品数据';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        un_limit();
        // 获取待处理的数据
        $imports = ImportComment::query()->where('status', ImportCommentStatusEnum::PendingProcessing->value)->select('id', 'file')->get();
        // 更新状态为处理中
        ImportComment::query()->whereIn('id', $imports->pluck('id')->toArray())->update(['status' => ImportCommentStatusEnum::ProcessingInProgress->value]);
        // 循环处理数据
        foreach ($imports as $import) {
            try {
                $commentsImport = new CommentsImport();
                $comments = Excel::toCollection($commentsImport, Storage::disk('public')->path($import->file))->first();
                $errors = $commentsImport->array($comments->toArray());
                // 保存错误日志
                if ($errors) {
                    $this->saveErrorLog($import, $errors);
                } else {
                    // 更新状态为处理完成
                    $import->update([
                        'status' => ImportCommentStatusEnum::ProcessingCompleted->value,
                    ]);
                }
            } catch (\Exception $e) {
                // 更新状态为处理失败并记录错误日志
                $this->saveErrorLog($import, [$e->getMessage()]);
            }
        }

        $this->info('商品数据导入完成');
        return self::SUCCESS;
    }

    /**
     * 保存错误日志
     * @param \App\Models\ImportComment $import
     * @param mixed $errors
     * @return void
     */
    protected function saveErrorLog(ImportComment $import, $errors)
    {
        // 将错误数组转换为字符串并换行
        $errorString = is_array($errors) ? implode("\n", $errors) : $errors;

        // 存日志文件夹
        $logDir = 'storage' . DIRECTORY_SEPARATOR . 'import' . DIRECTORY_SEPARATOR . 'comments' . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR;
        if (!Storage::disk('public')->exists($logDir)) {
            Storage::disk('public')->makeDirectory($logDir);
        }

        // 文件
        $logFile = $logDir . 'error_' . $import->id . '.log';

        // 写入错误日志
        Storage::disk('public')->put($logFile, $errorString);

        // 更新状态为处理失败
        $import->update([
            'status' => ImportCommentStatusEnum::ProcessingFailed->value,
            'error_log' => $logFile,
        ]);
    }
}
