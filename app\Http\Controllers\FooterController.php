<?php

namespace App\Http\Controllers;

use App\Constants\CacheKey;
use App\Models\Footer\Footer;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Spatie\QueryBuilder\QueryBuilder;


class FooterController extends Controller
{

    /**
     * 列表
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $list = Cache::remember(CacheKey::FooterList->value, now()->addMinutes(30), function () {
            $builder = QueryBuilder::for(Footer::class)
                ->with('links')
                ->allowedSorts(['id'])
                ->defaultSort('-id');
            return $builder->get();
        });
        return JsonResource::collection($list);
    }

}
