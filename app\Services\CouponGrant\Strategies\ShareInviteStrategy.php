<?php

namespace App\Services\CouponGrant\Strategies;

use App\Models\Coupon\Coupon;
use App\Models\Order\Order;
use App\Models\User\User;
use Illuminate\Support\Arr;

class ShareInviteStrategy extends RuleStrategy
{

    public function getUser(): ?User
    {
        if ($this->model instanceof User) {
            return $this->model;
        }
        // 邀请的用户
        if ($this->model instanceof Order) {
            $this->model->loadMissing(['user.parent']);
            if (!$this->model->user->parent instanceof User) {
                return null;
            }
            return $this->model->user->parent;
        }
        return null;
    }

    /**
     * 获取邀请奖励的优惠券
     */
    public function getCoupon(): ?Coupon
    {
        if ($this->model instanceof User) {
            return $this->rule->coupon;
        }
        if ($this->model instanceof Order) {
            // 金额未达到
            if (Arr::get($this->rule->config, 'rebate_order_price', 0) > $this->model->paid_amount) {
                return null;
            }
            return Coupon::query()->find(Arr::get($this->rule->config, 'rebate_coupon_id'));
        }
        return null;
    }

    public function checkCanGrant(): bool
    {
        if ($this->model instanceof User) {
            return true;
        }
        if ($this->model instanceof Order) {
            return true;
        }
        return false;
    }

}
