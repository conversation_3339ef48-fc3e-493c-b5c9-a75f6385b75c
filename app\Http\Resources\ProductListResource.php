<?php

namespace App\Http\Resources;

use App\Models\Product\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property  Product $resource
 */
class ProductListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $sizes = [];
        $images = [];
        $this->resource->variants->each(function ($variant) use (&$sizes, &$colors, &$images, &$stock) {
            $sizes[] = $variant?->sizeAttribute?->only(['value', 'sort']);
            $images[] = $variant?->image?->url;
            $stock += $variant->stock;
        });
        // 防止原价是0
        $originPrice = $this->resource->origin_price ?: 1;
        // 颜色
        $colors = [];
        $uniqueColors = [];
        $this->resource->colors->each(function ($color) use (&$colors,&$uniqueColors) {
            $colors[] = [
                'value' => $color->color?->value,
                'name' => $color->color?->extra,
                'images' => $color->images
            ];
            $uniqueColors[] = $color->color?->value;
        });
        return [
            'id' => $this->resource->id,
            'first_publish_at' => $this->resource->first_publish_at,
            'new_publish_at' => $this->resource->new_publish_at,
            'is_public' => $this->resource->is_public,
            'is_featured' => $this->resource->is_featured,
            'is_new' => $this->resource->is_new,
            'spu' => $this->resource->spu,
            'sku' => $this->resource->sku,
            'title' => $this->resource->title,
            'slug_title' => $this->resource->slug_title,
            'desc' => $this->resource->desc,
            'short_desc' => $this->resource->short_desc,
            'model_desc' => $this->resource->model_desc,
            'meta_title' => $this->resource->meta_title,
            'meta_description' => $this->resource->meta_description,
            'meta_keywords' => $this->resource->meta_keywords,
            'keywords' => $this->resource->keywords,
            'image_id' => $this->resource->image_id,
            'video_id' => $this->resource->video_id,
            'image' => $this->resource->image,
            'brand_name' => $this->resource->brand_name,
            'category_name' => $this->resource->category_name,
            'style_name' => $this->resource->style_name,
            'material_name' => $this->resource->material_name,
            'make_at' => $this->resource->make_at,
            'sale_num' => $this->resource->sale_num,
            'origin_price' => $this->resource->origin_price,
            'max_price' => $this->resource->max_price,
            'min_price' => $this->resource->min_price,
            'max_off' => getRate($this->resource->min_price, $originPrice, 4, ''),
            'stock' => $stock,
            'colors' => collect($colors)->unique('value')->toArray(),
            'sizes' => collect($sizes)->sortBy('sort')->unique('value')->pluck('value')->toArray(),
            'images' => array_unique(array_filter($images)),
            'variants' => $this->whenLoaded('variants', fn($c) => $c),
            'collected' => $this->resource->collected ?? !!$this->resource->collect_user_ids_exists,
            'comment_static' => $this->whenLoaded('commentStatic', fn($c) => $c),
            'is_activity' => $this->resource->is_activity,
            'end_at' => $this->resource->end_at,
        ];
    }
}
