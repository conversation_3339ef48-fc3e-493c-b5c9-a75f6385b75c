<?php

namespace App\Http\Resources;

use App\Models\User\UserCoupon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property UserCoupon $resource
 */
class CouponFrameListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'coupon' => $this->resource->coupon,
            'coupon_grant_rule' => $this->resource->couponGrantRule,
        ];
    }
}
