<?php

namespace App\Models;

use App\Models\Enums\AttachmentModuleEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @property string $url
 * @property string $disk
 */
class Attachment extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];
    protected $hidden = [
        'disk',
    ];
    protected $casts = [
        'upload_time' => 'datetime',
        'module' => AttachmentModuleEnum::class,
    ];

    protected $appends = [
        'url', // 原图
        'cache_urls', // 缓存图
    ];

    public function url(): Attribute
    {
        return Attribute::make(function () {
            if ($this->module) {
                return Storage::disk($this->disk)->url($this->module->getPath() . $this->getAttribute('path'));
            } else {
                return Storage::disk($this->disk)->url($this->getAttribute('path'));
            }
        });
    }

    // 缓存图
    public function cacheUrls(): Attribute
    {
        return Attribute::make(function () {
            if ($this->module && $this->module->value == AttachmentModuleEnum::Product->value) {
                $cachePaths = $this->module->getCachePaths();
                $path = Str::replace('.jpg', '.' . Arr::get($cachePaths, 'suffix'), $this->getAttribute('path'));
                return [
                    'small_url' => Storage::disk($this->disk)->url(Arr::get($cachePaths, 'small') . $path),
                    'medium_url' => Storage::disk($this->disk)->url(Arr::get($cachePaths, 'medium') . $path),
                    'large_url' => Storage::disk($this->disk)->url(Arr::get($cachePaths, 'large') . $path),
                ];
            } else {
                return [];
            }
        });
    }

    public function disk(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->attributes['disk'] ?? config('filesystems.default')
        );
    }

    public function setModuleAttribute($value)
    {
        $this->attributes['module'] = AttachmentModuleEnum::tryFrom($value) ?? AttachmentModuleEnum::Default; // 设置一个默认值
    }

}
