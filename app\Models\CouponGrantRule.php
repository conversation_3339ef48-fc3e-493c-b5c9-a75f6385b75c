<?php

namespace App\Models;

use App\Models\Coupon\Coupon;
use App\Models\Enums\CouponGrantType;
use App\Models\User\User;
use App\Services\CouponGrant\CouponGrantService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property Coupon $coupon
 * @property CouponGrantType $type
 * @property int $id
 * @property bool $enabled
 * @property array $config
 */
class CouponGrantRule extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'type' => CouponGrantType::class,
        'config' => 'json',
        'enabled' => 'boolean',
        'send_email' => 'boolean',
    ];

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * 发放记录
     * @return HasMany
     */
    public function records(): HasMany
    {
        return $this->hasMany(CouponGrantRecord::class);
    }

    /**
     *
     * @param \App\Models\Enums\CouponGrantType $type
     * @param \Illuminate\Database\Eloquent\Model $model
     * @return array[CouponGrantRule, Coupon]
     */
    public static function grantCouponByRuleType(CouponGrantType $type, Model $model, $is_framed = false)
    {
        // 规则：订单支付成功发放优惠卷
        $coupon_grant_rule = CouponGrantRule::query()
            ->where('type', $type->value)
            ->with(['coupon'])
            ->where('enabled', true)
            ->where(function($query) {
                $query->whereNull('total_count')
                      ->orWhereRaw('total_count > grant_count');
            })
            ->first();
        // 发放优惠卷
        if (!$coupon_grant_rule) {
            return [null, null];
        }
        $couponGrantService = CouponGrantService::getInstance($coupon_grant_rule, $model);
        // 获取优惠卷
        $coupon = $couponGrantService->grant($is_framed);

        return [$coupon_grant_rule, $coupon];
    }
}
