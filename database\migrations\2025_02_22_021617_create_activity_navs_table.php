<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_navs', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('导航名称');
            $table->string('desc')->nullable()->comment('导航描述');
            $table->string('url')->comment('导航链接');
            $table->string('link_name')->nullable()->comment('连接名称');
            $table->bigInteger('image_id')->nullable()->comment('图片id');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('status')->default(1)->comment('状态');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('活动导航');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_navs');
    }
};
