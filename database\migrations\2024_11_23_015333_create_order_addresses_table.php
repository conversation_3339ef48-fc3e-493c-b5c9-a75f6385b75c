<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_addresses', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index();
            $table->string('first_name', 64);
            $table->string('last_name', 64);
            $table->bigInteger('country_id');
            $table->string('address', 512);
            $table->string('address1', 512);
            $table->string('state');
            $table->string('city');
            $table->string('zip');
            $table->boolean('is_save')->default(false);
            $table->tinyInteger('type')->comment('类型 ');
            $table->string('phone', 32)->nullable()->comment('customer phone number');

            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('订单地址');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_addresses');
    }
};
