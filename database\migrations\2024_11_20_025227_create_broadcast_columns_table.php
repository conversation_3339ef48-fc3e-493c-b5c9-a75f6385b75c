<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('broadcast_columns', function (Blueprint $table) {
            $table->id();
            $table->string('title', 191);
            $table->text('text_title')->nullable()->comment('标题内容');
            $table->bigInteger('image_id')->nullable()->comment('图片');
            $table->unsignedTinyInteger('position')->default(0)->comment('位置：1靠左，2靠右，3居中');
            $table->string('url', 512)->nullable()->comment('链接地址');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->boolean('is_visible')->default(1)->comment('显示状态，1为显示，0为隐藏');
            $table->unsignedTinyInteger('content_type')->default(0)->comment('1图片 2富文本 3超链接');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('广播栏');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('broadcast_columns');
    }
};
