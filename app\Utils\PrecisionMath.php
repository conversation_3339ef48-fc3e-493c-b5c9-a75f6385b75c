<?php

namespace App\Utils;

/**
 * 高精度数学计算工具类
 * 解决浮点数精度问题
 */
class PrecisionMath
{
    /**
     * 默认精度
     */
    const DEFAULT_SCALE = 2;

    /**
     * 高精度加法
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function add($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        return bcadd((string)$left, (string)$right, $scale);
    }

    /**
     * 高精度减法
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function sub($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        return bcsub((string)$left, (string)$right, $scale);
    }

    /**
     * 高精度乘法
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function mul($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        return bcmul((string)$left, (string)$right, $scale);
    }

    /**
     * 高精度除法
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function div($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        if ((string)$right === '0') {
            throw new \InvalidArgumentException('Division by zero');
        }
        return bcdiv((string)$left, (string)$right, $scale);
    }

    /**
     * 高精度比较
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return int 返回 -1, 0, 1
     */
    public static function comp($left, $right, int $scale = self::DEFAULT_SCALE): int
    {
        return bccomp((string)$left, (string)$right, $scale);
    }

    /**
     * 转换为浮点数（用于显示）
     * @param string|float|int $value
     * @return float
     */
    public static function toFloat($value): float
    {
        return (float)$value;
    }

    /**
     * 格式化为指定小数位数的字符串
     * @param string|float|int $value
     * @param int $decimals
     * @return string
     */
    public static function format($value, int $decimals = self::DEFAULT_SCALE): string
    {
        return number_format((float)$value, $decimals, '.', '');
    }

    /**
     * 确保值为正数
     * @param string|float|int $value
     * @return string
     */
    public static function abs($value): string
    {
        $value = (string)$value;
        return bccomp($value, '0', self::DEFAULT_SCALE) >= 0 ? $value : bcmul($value, '-1', self::DEFAULT_SCALE);
    }

    /**
     * 取最大值
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function max($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        return bccomp((string)$left, (string)$right, $scale) >= 0 ? (string)$left : (string)$right;
    }

    /**
     * 取最小值
     * @param string|float|int $left
     * @param string|float|int $right
     * @param int $scale
     * @return string
     */
    public static function min($left, $right, int $scale = self::DEFAULT_SCALE): string
    {
        return bccomp((string)$left, (string)$right, $scale) <= 0 ? (string)$left : (string)$right;
    }
}
