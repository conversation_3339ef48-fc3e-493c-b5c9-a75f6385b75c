<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_colors', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_id')->index()->comment('商品ID');
            $table->bigInteger('color_id')->index()->comment('颜色ID');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->unique(['product_id', 'color_id']);
            $table->comment('商品颜色表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_colors');
    }
};
