<?php

namespace App\Http\Controllers;

use App\Apis\Airwallex\AirwallexService;
use App\Apis\Paypal\PaypalClient;
use App\Apis\Paypal\PaypalService;
use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Jobs\Order\OrderPaid;
use App\Models\Order\FastPaypalOrder;
use App\Models\Order\Order;
use App\Models\Order\Payment\AirwallexOrder;
use DB;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use App\Models\Cart;
use App\Models\Order\Payment\PaypalOrder;

class PaymentController extends Controller
{
    public function payment3dsCheck(Request $request)
    {
        $validated = $request->validate([
            'paymentIntentId' => 'required|string',
        ]);
        $threeDsResponse = AirwallexService::paymentIntentsConfirmContinue($validated['paymentIntentId']);
        if (Arr::get($threeDsResponse, 'status') == 'SUCCEEDED') {
            $orderId = Arr::get($threeDsResponse, 'merchant_order_id');
            $order = Order::query()->where('no', $orderId)->first();
            if (!$order instanceof Order) {
                throw new DataException('Order not found.');
            }
            // 付款订单
            $paymentOrder = new AirwallexOrder([
                'detail' => $threeDsResponse,
                'intent_id' => $validated['paymentIntentId'],
            ]);
            $paymentOrder->save();
            // 支付绑定
            OrderPaid::dispatchSync($order, $paymentOrder);
            // 跳支付成功
            return response()->json(['threeDsResponse'=> Arr::only($threeDsResponse, ['status']), 'order'=>$order]);
        } elseif (Arr::get($threeDsResponse, 'status') == 'REQUIRES_CUSTOMER_ACTION') {
            return Arr::only($threeDsResponse, ['next_action', 'status']);
        }else{
            return Arr::only($threeDsResponse, ['code', 'message']);
        }
    }

    /**
     * 快速支付生成订单
     * @param \App\Models\Order\FastPaypalOrder $fastPaypalOrder
     * @return JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function paypalFastConfirm(FastPaypalOrder $fastPaypalOrder): JsonResource
    {
        $paypalOrderId = $fastPaypalOrder->paypalOrder->paypal_order_id;
        try {
            DB::beginTransaction();
            $orderResponse = PaypalService::showOrderDetails($paypalOrderId);
            switch (Arr::get($orderResponse, 'status')) {
                case 'COMPLETED':
                    orderService()->paypalFastCreateOrder($fastPaypalOrder, $orderResponse);
                    break;
                case 'APPROVED':
                    $captureResponse = PaypalService::paymentIntentsConfirm($paypalOrderId);
                    if (Arr::get($captureResponse, 'status') === 'COMPLETED') {
                        orderService()->paypalFastCreateOrder($fastPaypalOrder, $captureResponse);
                    }
                    break;
                default:
                    break;
            }
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException('paypal payment failed.', ErrorCode::HttpNotFound);
        }

        return JsonResource::make($fastPaypalOrder->paypalOrder?->order);
    }

    /**
     * PayPal支付确认回调
     * @param \App\Models\Order\Payment\PaypalOrder $paypalOrder
     * @return JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function paypalConfirm(PaypalOrder $paypalOrder): JsonResource
    {
        // 检查关联的订单
        $order = $paypalOrder->order;
        // 如果已有订单，检查订单状态
        if ($order) {
            // 订单已支付或者已关闭
            if ($order->paid_at || $order->closed) {
                throw new DataException('The order status is incorrect.');
            }
        }
        try {
            DB::beginTransaction();
            $paypalOrderId = $paypalOrder->paypal_order_id;
            $orderResponse = PaypalService::showOrderDetails($paypalOrderId);
            $res = null;
            switch (Arr::get($orderResponse, 'status')) {
                case 'COMPLETED':
                    $res = orderService()->paypalCreateOrder($paypalOrder, $orderResponse);
                    break;
                case 'APPROVED':
                    $captureResponse = PaypalService::paymentIntentsConfirm($paypalOrderId);
                    if (Arr::get($captureResponse, 'status') === 'COMPLETED') {
                        $res = orderService()->paypalCreateOrder($paypalOrder, $captureResponse);
                    }
                    break;
                default:
                    break;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // 恢复购物车状态
            Cart::where('is_checkout_processing', true)
                ->whereNotNull('checkout_data')
                ->where('order_id', null)
                ->update(['is_checkout_processing' => false]);
            throw new DataException('Please make a new payment.', ErrorCode::HttpNotFound);
        }

        // 返回订单信息
        if ($order) {
            $order->refresh();
            return JsonResource::make($order);
        }
        // 如果没有订单，返回PayPal订单信息
        return JsonResource::make($res);
    }

    public function paypalNotify(Request $request): void
    {
        // 非debug跳过
        if ($request->get('__TEST__', '') != 'eXxK$yJ7B*YZ') {
            if (!PaypalService::notify($request)) {
                return;
            }
        }
        $result = $request->all();

        // 订单id
        switch (Arr::get($result, 'event_type')) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                $orderId = Arr::get($result, 'resource.invoice_id');
                $order = Order::query()->with(['paypalOrder'])->where('no', $orderId)->first();
                if ($order) {
                    $orderResponse = PaypalService::showOrderDetails($order->paypalOrder->paypal_order_id);
                    $order->paypalOrder->update(['detail' => $orderResponse]);
                    OrderPaid::dispatch($order, $order->paypalOrder);
                }
                break;
            default:
                break;
        }
    }
}
