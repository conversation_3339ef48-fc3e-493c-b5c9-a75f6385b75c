<?php

/**
 * Author: raku <<EMAIL>>
 * Date: 2023/4/4
 */

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Constants\Roles;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Validation\Rule;

class AdminUserController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::BannersIndex, Permissions::BannersUpdate)->only('index');
        $this->hasPermissionOr(Permissions::BannersUpdate)->except('index');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $query = QueryBuilder::for(AdminUser::class)
            ->with([
                'permissions',
                'roles.permissions'
            ])
            ->allowedFilters([
                AllowedFilter::partial('account'),
                AllowedFilter::partial('name'),
                AllowedFilter::exact('enable'),
                
            ])
            ->defaultSort('-id');
        $list = $query->paginate($this->getPerPage());
        /**
         * @var AdminUser $user
         */
        foreach ($list->items() as $user) {
            $user->{'all_permissions'} = $user->getAllPermissions()->select(['id', 'name', 'desc']);
            $user->{'all_roles'} = $user->{'roles'}->select(['id', 'name', 'desc']);
            $user->makeHidden(['permissions', 'roles']);
        }

        return JsonResource::collection($list);
    }

    public function show(AdminUser $user): JsonResource
    {
        $user->loadMissing(['permissions', 'roles']);
        return JsonResource::make($user);
    }

    public function update(Request $request, AdminUser $user)
    {
        $validated = $request->validate([
            'account' => ['required', Rule::unique('admin_users')->ignore($user), 'max:100'],
            'name' => 'required|max:100',
            'remark' => 'nullable|string',
            'enable' => 'bool',
            'password' => 'nullable',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
     
        ]);
        $permissions = Arr::pull($validated, 'permissions', []);
        $roleIds = Arr::pull($validated, 'roles', []);

        // 如果没有传入密码，则从验证数据中移除 password 字段
        if (empty($validated['password'])) {
            Arr::forget($validated, 'password');
        }

        /**
         * @var AdminUser $admin
         */
        $admin = Auth::user();
        // 修改基础
        $user->update($validated);

        // 修改角色, 不能改成管理员和超管
        if (!is_null($roleIds)) {
            // 获取角色名称
            $roles = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
            
            // if ($admin->hasAnyRole(Roles::SuperAdmin)) {
            //     $roles = array_diff($roles, [Roles::SuperAdmin->value]);
            // } else {
            //     $roles = array_diff($roles, [Roles::SuperAdmin->value, Roles::Admin->value]);
            // }
            $user->syncRoles($roles);
        }

        return JsonResource::make($user);
    }

    /**
     * 创建用户
     * @param Request $request
     * @param AdminUser $user
     * @return JsonResource
     */
    public function store(Request $request, AdminUser $user): JsonResource
    {
        $validated = $request->validate([
            'account' => 'required|unique:admin_users|max:100',
            'name' => 'required|max:100',
            'remark' => 'nullable|string',
            'enable' => 'bool',
            'password' => 'required',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);
        
        // 本阶段可以无条件修改
        $roleIds = Arr::pull($validated, 'roles', []);
        
        // 创建用户
        $user->fill($validated)->save();
        
        $admin = Auth::user();
        
        // 修改角色, 不能改成管理员和超管
        if (!is_null($roleIds)) {
            // 获取角色名称
            $roles = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
            
            // if ($admin->hasAnyRole(Roles::SuperAdmin)) {
            //     $roles = array_diff($roles, [Roles::SuperAdmin->value]);
            // } else {
            //     $roles = array_diff($roles, [Roles::SuperAdmin->value, Roles::Admin->value]);
            // }
            // 绑定角色
            $user->syncRoles($roles);
        }
        
        return JsonResource::make($user);
    }


    /**
     * 批量删除管理员
     * @param Request $request
     * @return JsonResource
     */
    public function batchDelete(Request $request): JsonResource
    {
        $ids = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:admin_users,id',
        ], ['ids.*.exists' => "不存在的用户id::input"])['ids'];
        AdminUser::query()->find($ids)->each(function (AdminUser $user) {
            $user->delete();
        });
        return JsonResource::make([]);
    }

    // 启用禁用
    public function status(Request $request, AdminUser $user): JsonResource
    {
        $validated = $request->validate([
            'enable' => ['required', 'boolean'],
        ]);
        $user->update($validated);
        return JsonResource::make($user);
    }
}
