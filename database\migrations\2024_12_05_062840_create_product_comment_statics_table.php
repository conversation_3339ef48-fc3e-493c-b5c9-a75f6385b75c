<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_comment_statics', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_id')->index();
            $table->integer('avg_grade')->default(0)->comment('平均分');
            $table->integer('number')->default(0)->comment('人数');

            $table->engine('InnoDB');
            $table->timestamps();
            $table->comment('商品评论静态统计表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_comment_statics');
    }
};
