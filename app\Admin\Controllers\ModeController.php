<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Mode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class ModeController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::ModesUpdate)->only(['store', 'update', 'destroy',]);
        $this->hasPermissionOr(Permissions::ModesUpdate, Permissions::ModesIndex)->only(['index', 'show']);
    }

    /**
     * 风格选择
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Mode::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Mode::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Mode $Mode): JsonResource
    {
        return JsonResource::make($Mode);
    }

    /**
     * 修改
     * @param Request $request
     * @param Mode $Mode
     * @return JsonResource
     */
    public function update(Request $request, Mode $Mode): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $Mode->update($validated);

        return JsonResource::make($Mode);
    }


    /**
     * 创建
     * @param Request $request
     * @param Mode $Mode
     * @return JsonResource
     */
    public function store(Request $request, Mode $Mode): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $Mode->update($validated);

        return JsonResource::make($Mode);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Mode::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Mode::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

}
