<?php

namespace App\Models;

use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CollectionTab extends MiddleModel
{
    use HasFactory;

    public $timestamps = false;

    /**
     * 关联到 Collection
     */
    public function collection()
    {
        return $this->belongsToMany(
            Collection::class,
            'collection_tabs',
            'tab_id',
            'collection_id',
            'id',
            'id'
        );
    }
}
