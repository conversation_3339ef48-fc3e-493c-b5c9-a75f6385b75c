<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderPaidFromEnum: int
{
    case  Cart = 0;
    case  ProductDetail = 1;
    case  Reorder = 2;

    public function desc(): string
    {

        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Cart->value => '购物车',
            self::ProductDetail->value => '商品详情页',
            self::Reorder->value => '重新支付',
        ];
    }

}
