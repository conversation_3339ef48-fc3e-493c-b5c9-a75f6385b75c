<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('associated_modes', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('mode_id')->index()->comment('风格ID');
            $table->bigInteger('associated_id')->index()->comment('关联风格ID');
            $table->engine('InnoDB');
            $table->comment('推荐风格关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('associated_modes');
    }
};
