<?php

namespace App\Models;

use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ActivitieProduct extends MiddleModel
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = [];

    //关联产品信息
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    //关联活动主表
    public function UserExperienceActivities()
    {
        return $this->belongsTo(UserExperienceActivitie::class);
    }
}
