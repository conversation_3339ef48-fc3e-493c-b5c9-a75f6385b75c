<?php

namespace App\Models;

use App\Constants\CacheKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $value
 * @property array $extra
 */
class AttributeValue extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'extra' => 'json',
    ];

    public function attachment(): BelongsTo
    {
        return $this->belongsTo(Attachment::class);
    }

    protected static function booted()
    {
        parent::booted();
        static::creating(function ($value) {
            cachePartialDelete(CacheKey::ProductAttributes->getKey('*'));
        });
        static::saved(function ($value) {
            cachePartialDelete(CacheKey::ProductAttributes->getKey('*'));
        });
    }

}
