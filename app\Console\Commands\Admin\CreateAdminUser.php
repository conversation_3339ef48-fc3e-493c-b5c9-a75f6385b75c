<?php

namespace App\Console\Commands\Admin;

use App\Models\AdminUser;
use App\Models\Role;
use Illuminate\Console\Command;

class CreateAdminUser extends Command
{

    protected $signature = 'admin:create-user';

    protected $description = '创建admin用户';

    /**
     * @return int
     */
    public function handle()
    {
        do {
            $account = $this->ask('请输入账号');
            if ($user = AdminUser::query()->where('account', $account)->first()) {
                $resetPassword = $this->ask('账号已存在,是否直接修改密码Y/n', 'Y');
                if (in_array($resetPassword, ['Y', 'y'])) {
                    return $this->resetPassword($user);
                } else {
                    $account = null;
                }
            }
        } while (!$account);
        // 姓名手机号
        $name = $this->ask('输入姓名', $account);
        $phone = $this->ask('输入手机号');
        $password = $this->ask('请输入密码(默认账号名):', $account);
        // 角色
        $roles = Role::query()->pluck('desc', 'id');
        foreach ($roles as $id => $role) {
            $this->info(" ID:{$id} - {$role}");
        }
        $askRoles = explode(',', $this->ask('选择角色ID(多个用,分割)'));

        $user = new AdminUser();
        $user->fill([
            'account' => $account,
            'name' => $name,
            'phone' => $phone,
            'password' => $password,
        ])->save();
        // 绑定角色
        if ($askRoles) {
            $user->roles()->sync($askRoles);
        }


        $this->info('创建成功');
        return self::SUCCESS;
    }

    public function resetPassword(AdminUser $user): int
    {
        $password = $this->ask('请输入密码(默认账号名):', $user->account);
        $user->update(['password' => $password]);

        $this->info('创建成功');
        return self::SUCCESS;
    }
}
