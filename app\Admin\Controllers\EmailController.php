<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Jobs\EmailRuleNotice;
use App\Models\Coupon\Coupon;
use App\Models\Email\EmailRule;
use App\Models\Email\EmailTemplate;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\EmailRuleSendTypeEnum;
use App\Models\Enums\EmailRuleTypeEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Support\Facades\Auth;

class EmailController extends Controller
{
    //
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::EmailsUpdate)
            ->only(['templatesStore', 'templatesUpdate', 'templatesDestroy']);
        //  查看权限
        $this->hasPermissionOr(Permissions::EmailsUpdate, Permissions::EmailsIndex)
            ->only(['templatesIndex', 'templatesShow']);
    }

    /**
     * 模板选项
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function templatesOptions(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(EmailTemplate::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     * 规则列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function ruleIndex(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(EmailRule::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('event'),
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function ruleSend(Request $request, EmailRule $rule): JsonResource
    {
        EmailRuleNotice::dispatchSync($rule->event,null,null, $rule->data);
        return JsonResource::make($rule);
    }

    /**
     * 创建规则
     * @param Request $request
     * @param EmailRule $rule
     * @return JsonResource
     */
    public function ruleStore(Request $request, EmailRule $rule): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'desc' => 'string',
            'enabled' => 'bool',
            'type' => ['required', new Enum(EmailRuleTypeEnum::class)],
            'send_type' => ['required', new Enum(EmailRuleSendTypeEnum::class)],
            'event' => ['required', new Enum(EmailRuleEventEnum::class)],
            'email_template_id' => ['required', new Exists(EmailTemplate::class, 'id')],
            'is_delay' => 'bool',
            'data' => 'array',
            'delay_second' => 'int',
            'delay_unit' => 'string|nullable',
        ]);
        $validated['admin_user_id'] = Auth::user()->id;
        // 订阅消息需要特殊处理
        if ($validated['event'] == EmailRuleEventEnum::SubscribeMessage->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.email_preferences' => ['required', new Enum(UserEmailPreferencesEnum::class)],
                'data.list' => ['array', 'required'],
                'data.list.*.name' => ['required', 'string'],
                'data.list.*.imageId' => ['required', 'string'],
                'data.list.*.linkSrc' => ['required', 'string'],
            ]);
        }
        // 优惠券特殊处理
        if ($validated['event'] == EmailRuleEventEnum::Coupon->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ]);
        }
        // 优惠券过期
        if ($validated['event'] == EmailRuleEventEnum::CouponExpire->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.advance_minute' => ['required', 'int', 'min:1'],
                'data.advance_unit' => ['required', 'string', 'in:minute,hour,day'],
                'data.send_time' => ['required','date_format:H:i:s'],
            ]);

            // 转换为分钟
            $validated['data']['advance_minute'] = $this->convertToMinutes(
                $validated['data']['advance_minute'],
                $validated['data']['advance_unit']
            );
        }
        $rule->fill($validated)->save();
        return JsonResource::make($rule);
    }

    public function ruleShow(Request $request, EmailRule $rule): JsonResource
    {
        $rule->loadMissing(['template']);

        // 处理优惠券过期事件的时间单位转换
        if ($rule->event->value == 18) {
            $data = $rule->data;
            $minutes = (int) $data['advance_minute'];
            $unit = $data['advance_unit'];

            $convertedValue = $this->convertFromMinutes($minutes, $unit);

            // 直接修改数组
            $rule->data = array_merge($rule->data, ['advance_minute' => $convertedValue]);
        }

        return JsonResource::make($rule);
    }

    public function ruleUpdate(Request $request, EmailRule $rule): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'desc' => 'string',
            'enabled' => 'bool',
            'type' => ['required', new Enum(EmailRuleTypeEnum::class)],
            'event' => ['required', new Enum(EmailRuleEventEnum::class)],
            'email_template_id' => ['required', new Exists(EmailTemplate::class, 'id')],
            'is_delay' => 'bool',
            'delay_second' => 'int',
            'delay_unit' => 'string|nullable',
            'data' => 'array',
            'send_type' => ['nullable', new Enum(EmailRuleSendTypeEnum::class)],
        ]);
        $validated['admin_user_id'] = Auth::user()->id;
        // 订阅消息需要特殊处理
        if ($validated['event'] == EmailRuleEventEnum::SubscribeMessage->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.email_preferences' => ['required', new Enum(UserEmailPreferencesEnum::class)],
                'data.list' => ['array', 'required'],
                'data.list.*.name' => ['required', 'string'],
                'data.list.*.imageId' => ['required', 'string'],
                'data.list.*.linkSrc' => ['required', 'string'],
            ]);
        }
        // 优惠券特殊处理
        if ($validated['event'] == EmailRuleEventEnum::Coupon->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ]);
        }
        // 优惠券过期
        if ($validated['event'] == EmailRuleEventEnum::CouponExpire->value) {
            $request->validate([
                'data' => ['required', 'array'],
                'data.advance_minute' => ['required', 'int', 'min:1'],
                'data.advance_unit' => ['required', 'string', 'in:minute,hour,day'],
                'data.send_time' => ['required','date_format:H:i:s'],
            ]);

            // 转换为分钟
            $validated['data']['advance_minute'] = $this->convertToMinutes(
                $validated['data']['advance_minute'],
                $validated['data']['advance_unit']
            );
        }
        $rule->update($validated);
        return JsonResource::make($rule);
    }

    public function ruleDestroy(Request $request, EmailRule $rule): JsonResource
    {
        $rule->delete();
        return JsonResource::make([]);
    }

    /**
     * 规则列表，增量修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Email\EmailRule $rule
     * @return JsonResource
     */
    public function rulePatchUpdate(Request $request, EmailRule $rule): JsonResource
    {
        $validated = $request->validate([
            'enabled' => ['bool'],
        ]);

        $validated && $rule->update($validated);
        return JsonResource::make($rule);
    }

    /**
     * 模板列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function templatesIndex(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(EmailTemplate::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('enabled')
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 新增模板
     * @param Request $request
     * @param EmailTemplate $template
     * @return JsonResource
     */
    public function templatesStore(Request $request, EmailTemplate $template): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:50',
            'title' => 'string|max:64',
            'desc' => 'string|max:255',
            'enabled' => ['bool'],
            'type' => ['required', new Enum(EmailRuleTypeEnum::class)],
            'data' => ['array'],
            'content' => 'required|string'
        ]);
        $content = Arr::get($validated, 'content');
        try {
            $validated['admin_user_id'] = Auth::user()->id;
            $html = Blade::render($content, EmailTemplate::BASE_DATA);
        } catch (\Throwable $exception) {
            throw new DataException("内容存在异常变量, 请修改后重新提交");
        }
        $template->fill($validated)->save();
        return JsonResource::make($template);
    }

    /**
     * 删除
     * @param Request $request
     * @param EmailTemplate $template
     * @return JsonResource
     */
    public function templatesDestroy(Request $request, EmailTemplate $template): JsonResource
    {
        $template->delete();
        return JsonResource::make([]);
    }

    /**
     * 新增模板
     * @param Request $request
     * @param EmailTemplate $template
     * @return JsonResource
     */
    public function templatesUpdate(Request $request, EmailTemplate $template): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'string|max:64',
            'desc' => 'string|max:255',
            'type' => ['required', new Enum(EmailRuleTypeEnum::class)],
            'data' => ['array'],
            'enabled' => ['bool'],
            'content' => 'required|string'
        ]);
        $content = Arr::get($validated, 'content');
        try {
            $validated['admin_user_id'] = Auth::user()->id;
            $html = Blade::render($content, EmailTemplate::BASE_DATA);
        } catch (\Throwable $exception) {
            throw new DataException("内容存在异常变量, 请修改后重新提交");
        }
        $template->update($validated);
        return JsonResource::make($template);
    }

    public function templatesShow(EmailTemplate $template): JsonResource
    {
        return JsonResource::make($template);
    }

    /**
     * 基础数据
     * @return JsonResource
     */
    public function templateBaseData(): JsonResource
    {
        $res = EmailTemplate::BASE_DATA;
        return JsonResource::make($res);
    }

    /**
     * 增量更新
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Email\EmailTemplate $template
     * @return JsonResource
     */
    public function templatesPatchUpdate(Request $request, EmailTemplate $template)
    {
        $validated = $request->validate([
            'enabled' => ['bool'],
        ]);

        $validated && $template->update($validated);
        return JsonResource::make($template);
    }

    /**
     * 将时间单位转换为分钟
     *
     * @param int $value 时间值
     * @param string $unit 时间单位 (minute, hour, day)
     * @return int 转换后的分钟数
     */
    private function convertToMinutes(int $value, string $unit): int
    {
        return match ($unit) {
            'minute' => $value,           // 分钟 -> 分钟
            'hour' => $value * 60,        // 小时 -> 分钟
            'day' => $value * 60 * 24,    // 天 -> 分钟
            default => $value,            // 默认按分钟处理
        };
    }

    /**
     * 将分钟转换回原始时间单位
     *
     * @param int $minutes 分钟数
     * @param string $unit 目标时间单位 (minute, hour, day)
     * @return int 转换后的时间值
     */
    private function convertFromMinutes(int $minutes, string $unit): int
    {
        $result = match ($unit) {
            'minute' => $minutes,                    // 分钟 -> 分钟
            'hour' => intval($minutes / 60),         // 分钟 -> 小时
            'day' => intval($minutes / (60 * 24)),   // 分钟 -> 天
            default => $minutes,                     // 默认按分钟处理
        };

        return $result;
    }

}
