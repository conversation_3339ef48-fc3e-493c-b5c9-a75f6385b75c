<?php

namespace App\Services\CouponGrant\Strategies;

use App\Models\Comment;
use App\Models\Coupon\Coupon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

class CommentStrategy extends RuleStrategy
{

    public function getCoupon(): Coupon
    {
        if ($this->model->hasImage) {
            $couponId = Arr::get($this->rule->config, 'picture_coupon_id');
        }
        if ($this->model->hasVideo) {
            $couponId = Arr::get($this->rule->config, 'video_coupon_id');
        }
        if ($this->model->hasImage && $this->model->hasVideo) {
            $couponId = Arr::get($this->rule->config, 'picture_video_coupon_id');
        }
        return Coupon::query()->find($couponId);
    }

    public function checkCanGrant(): bool
    {
        if (!$this->model instanceof Comment) {
            return false;
        }
        // 没有用户的
        if (!$this->model->user_id) {
            return false;
        }
        // init
        $this->model->hasImage = false;
        $this->model->hasVideo = false;

        $fileTypes = $this->model->images()->pluck('file_type')->toArray();
        if (array_intersect($fileTypes, ["image/webp", 'image/jpeg', 'image/png', 'image/jpg'])) {
            $this->model->hasImage = true;
        }
        if (array_intersect($fileTypes, ["video/mp4"])) {
            $this->model->hasVideo = true;
        }
        // 图和视频都没有
        if (!$this->model->hasImage && !$this->model->hasVideo) {
            return false;
        }
        // 图和视频都有
        // if ($this->model->hasImage && $this->model->hasVideo) {
        //     return !Comment::query()->where('user_id', $this->model->user_id)
        //         ->where('id', '!=', $this->model->id)
        //         ->whereHas('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["image/webp", 'image/jpeg', 'image/png', 'image/jpg']);
        //         })
        //         ->whereHas('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["video/mp4"]);
        //         })
        //         ->exists();
        // }
        // // 仅图片
        // if ($this->model->hasImage) {
        //     return !Comment::query()->where('user_id', $this->model->user_id)
        //         ->where('id', '!=', $this->model->id)
        //         ->whereHas('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["image/webp", 'image/jpeg', 'image/png', 'image/jpg']);
        //         })
        //         ->whereDoesntHave('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["video/mp4"]);
        //         })
        //         ->exists();
        // }
        // // 仅视频
        // if ($this->model->hasVideo) {
        //     return !Comment::query()->where('user_id', $this->model->user_id)
        //         ->where('id', '!=', $this->model->id)
        //         ->whereDoesntHave('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["image/webp", 'image/jpeg', 'image/png', 'image/jpg']);
        //         })
        //         ->whereHas('images', function (Builder $query) {
        //             $query->whereIn('file_type', ["video/mp4"]);
        //         })
        //         ->exists();
        // }
        return true;
    }

}
