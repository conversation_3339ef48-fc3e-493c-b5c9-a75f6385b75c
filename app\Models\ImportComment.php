<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class ImportComment extends MiddleModel
{
    use HasFactory, SoftDeletes;
    protected $guarded = [];

    // 注意这里产品的sku和变体的sku是相反使用的
    public static array $header = [
        'sku',
        // 'spu',
        'first_name',
        'last_name',
        'email',
        'content',
        'grade',
        'status',
        'enabled',
        // 'top_at',
        'create_time',
        // 'images',
    ];

    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class);
    }
}
