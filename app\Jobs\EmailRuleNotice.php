<?php

namespace App\Jobs;

use App\Mail\TemplateEmail;
use App\Models\Coupon\Coupon;
use App\Models\Email\EmailRule;
use App\Models\Email\EmailTemplate;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use App\Models\User\User;
use App\Models\User\UserGroup;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use App\Models\Order\OrderAddress;
use App\Models\Enums\Order\OrderAddressTypeEnum;

class EmailRuleNotice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 重试间隔时间（秒）
     */
    public int $backoff = 60;
    
    public $timeout = 120; 

    /**
     * 处理邮件规则消息发放
     */
    public function __construct(
        public EmailRuleEventEnum $event,
        public ?Order             $order = null,
        public ?User              $user = null,
        public ?array             $data = null,
    ) {
        //
    }

    /**
     * 处理邮件发送规则结构
     */
    public function handle(): void
    {
        try {
            // 获取邮箱
            $email = match ($this->event) {
                EmailRuleEventEnum::OrderPaidSuccess => $this->getOrderEmail(),
                EmailRuleEventEnum::OrderPaidFail => $this->getOrderEmail(),
                EmailRuleEventEnum::UserInvite => Arr::get($this->data, 'email'),
                EmailRuleEventEnum::FindPassword => $this->user->email,
                EmailRuleEventEnum::SubscribeSuccess => $this->user->email,
                EmailRuleEventEnum::SubscribeMessage => $this->getSubscribeEmails(),
                EmailRuleEventEnum::SubscriptionCancel => $this->user->email,
                EmailRuleEventEnum::SubscribeConfirm => $this->user->email,
                EmailRuleEventEnum::Register => $this->user->email,
                EmailRuleEventEnum::Coupon => $this->user->email,
                EmailRuleEventEnum::LogisticsInspection => $this->order->email,
                EmailRuleEventEnum::FirstOrder => $this->getOrderEmail(),
                EmailRuleEventEnum::CouponExpire => $this->user->email,
                EmailRuleEventEnum::Verificationcode => $this->user->email,
                EmailRuleEventEnum::Group => $this->getGroupEmails(Arr::get($this->data, 'group_ids')),
                EmailRuleEventEnum::InviteComment => $this->getOrderEmail(),
                // EmailRuleEventEnum::Comment => $this->getCommentEmails(Arr::get($this->data, 'comment_id')),
                default => null
            };
  
            if (!$email) {
                return;
            }

            //测试没有用户的时候指定用户
            // if(!$this->user){
            // $email = [];
            // $email[] = '<EMAIL>';
            // $email[] = '<EMAIL>';
            // }
            // 清空模板变量数据
            $baseData = Arr::map(EmailTemplate::BASE_DATA, fn() => "");
            // 填充数据
            switch ($this->event) {
                case   EmailRuleEventEnum::OrderPaidSuccess:
                    $this->fillOrderSuccessData($baseData);
                    break;
                case   EmailRuleEventEnum::UserInvite:
                    $this->fillUserInviteData($baseData);
                    break;
                case   EmailRuleEventEnum::FindPassword:
                    $this->fillFindPasswordData($baseData);
                    break;
                case   EmailRuleEventEnum::SubscribeMessage:
                    $this->fillSubscribeMessageData($baseData);
                    break;
                case   EmailRuleEventEnum::SubscribeConfirm:
                    $this->fillSubscribeConfirmData($baseData);
                    break;
                case   EmailRuleEventEnum::Register:
                    $this->fillRegisterData($baseData);
                    break;
                case   EmailRuleEventEnum::SubscriptionCancel:
                    $this->fillSubscriptionCancelData($baseData);
                    break;
                case   EmailRuleEventEnum::OrderPaidFail:
                    $this->fillOrderPaidFailData($baseData);
                    break;
                case   EmailRuleEventEnum::SubscribeSuccess:
                    $this->fillSubscribeSuccesslData($baseData);
                    break;
                case   EmailRuleEventEnum::CouponExpire:
                    $this->fillCouponExpireData($baseData);
                    break;
                case   EmailRuleEventEnum::Coupon:
                    $this->fillCouponData($baseData);
                    break;
                case   EmailRuleEventEnum::Verificationcode:
                    $this->fillVerificationcodeData($baseData);
                    break;
                case   EmailRuleEventEnum::LogisticsInspection:
                    $this->fillOrderSuccessData($baseData);
                    break;
                case   EmailRuleEventEnum::InviteComment:
                    $this->fillOrderSuccessData($baseData);
                    break;
                default:
                    break;
            };
            // 匹配规则
            EmailRule::query()
                ->with(['template'])
                ->where('enabled', true)
                ->where('event', $this->event)
                ->chunkMap(function (EmailRule $rule) use ($email, $baseData) {
                    $data = $baseData;
                    // 规则默认数据 或者模板默认数据
                    $defaultData = $rule->data ?: $rule->template->data;
                    // 填充默认数据
                    if ($couponId = Arr::get($defaultData, 'coupon_id')) {
                        if (empty($data['couponCode'])) {
                            $coupon = Coupon::query()->findOrFail($couponId);
                            $data['couponName'] = $coupon->name ?? '';
                            $data['couponCode'] = $coupon->code ?? '';
                            $data['couponDesc'] = $coupon->desc ?? '';
                            $data['couponDiscountDesc'] = $coupon->discount_desc ?? '';
                            $data['couponEffectiveEndAt'] = $coupon->expire_at ?? '';
                        }
                    } elseif ($subscribeList = Arr::get($defaultData, 'subscribeList')) {
                        $data['subscribeList'] = [
                            'name' => $subscribeList['name'] ?? '',
                            'imageSrc' => $subscribeList['imageSrc'] ?? '',
                            'linkSrc' => $subscribeList['linkSrc'] ?? ''
                        ];
                    }
                    // 延迟发送
                    if ($rule->delay_second) {
                        Mail::to(Arr::wrap($email))
                            ->later(
                                now()->addSeconds($rule->delay_second),
                                new TemplateEmail($rule->template, $data)
                            );
                    } else {
                        Mail::to(Arr::wrap($email))
                            ->send(new TemplateEmail($rule->template, $data));
                    }
                });
        } catch (\Exception $e) {
            logger()->channel('email')->warning('EmailRuleNotice: ' . $e->getMessage());
            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        logger()->channel('email')->error('EmailRuleNotice failed after max attempts: ' . $exception->getMessage(), [
            'event' => $this->event->value,
            'order_id' => $this->order?->id,
            'user_id' => $this->user?->id,
            'data' => $this->data,
            'exception' => $exception->getTraceAsString()
        ]);
    }

    public function getOrderEmail()
    {
        if ($this->order->email) {
            return $this->order->email;
        } else {
            $this->order->loadMissing('user');
            if ($this->order->user) {
                return $this->order->user->email;
            }
        }
        return "";
    }

    public function getSubscribeEmails(): array
    {
        return User::query()
            ->where('email_preferences', Arr::get($this->data, 'email_preferences'))
            ->pluck('email')
            ->toArray();
    }

    public function getGroupEmails($group_ids): array
    {
        return User::query()
            ->join('user_groups', 'users.id', '=', 'user_groups.user_id')
            ->whereIn('user_groups.group_id', $group_ids)
            ->distinct() // 去重
            ->pluck('users.email')
            ->toArray();
    }

    public function fillFindPasswordData(&$baseData): void
    {
        $baseData['resetPasswordLink'] = Arr::get($this->data, 'url');
        $baseData['email'] = $this->user->email;
    }

    public function fillRegisterData(&$baseData): void
    {
        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['email'] = $this->user->email;
    }

    public function fillSubscribeSuccesslData(&$baseData): void
    {
        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['email'] = $this->user->email;
    }

    public function fillSubscriptionCancelData(&$baseData): void
    {
        $baseData['userName'] = $this->user->first_name . ' ' . $this->user->last_name;
    }

    public function fillSubscribeConfirmData(&$baseData): void
    {
        $baseData['subscribeConfirmToken'] = Arr::get($this->data, 'token');
    }

    public function fillSubscribeMessageData(&$baseData): void
    {
        $baseData['subscribeList'] = Arr::get($this->data, 'list');
    }

    public function fillUserInviteData(&$baseData): void
    {
        if (!$this->user instanceof User) {
            return;
        }
        $baseData['userInviteLink'] = Arr::get($this->data, 'invite_link');
        $baseData['userInviteCode'] = Arr::get($this->data, 'invite_code');
        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['inviteMessage'] = Arr::get($this->data, 'optional');
        $baseData['email'] = Arr::get($this->data, 'email');
    }

    public function fillOrderSuccessData(&$baseData): void
    {
        if (!$this->order instanceof Order) {
            return;
        }

        $this->order->loadMissing(['billingAddress.country', 'shippingAddress.country']);
        // 初始化地址数组，确保所有键都存在
        $billingAddress = [
            'name' => '',
            'address' => '',
            'state' => '',
            'city' => '',
            'zip' => '',
            'phone' => '',
            'country' => ''
        ];

        $shippingAddress = [
            'name' => '',
            'address' => '',
            'state' => '',
            'city' => '',
            'zip' => '',
            'phone' => '',
            'country' => ''
        ];

        // 如果地址存在，填充数据
        if ($this->order->billingAddress) {
            $billingAddress['name'] = $this->order->billingAddress->first_name . ' ' . $this->order->billingAddress->last_name;
            $billingAddress['address'] = $this->order->billingAddress->address;
            $billingAddress['state'] = $this->order->billingAddress->state;
            $billingAddress['city'] = $this->order->billingAddress->city;
            $billingAddress['zip'] = $this->order->billingAddress->zip;
            $billingAddress['phone'] = $this->order->billingAddress->phone;
            $billingAddress['country'] = $this->order->billingAddress->country->name ?? '';
        }
        if ($this->order->shippingAddress) {
            $shippingAddress['name'] = $this->order->shippingAddress->first_name . ' ' . $this->order->shippingAddress->last_name;
            $shippingAddress['address'] = $this->order->shippingAddress->address;
            $shippingAddress['state'] = $this->order->shippingAddress->state;
            $shippingAddress['city'] = $this->order->shippingAddress->city;
            $shippingAddress['zip'] = $this->order->shippingAddress->zip;
            $shippingAddress['phone'] = $this->order->shippingAddress->phone;
            $shippingAddress['country'] = $this->order->shippingAddress->country->name ?? '';
        }
        // 加载关联
        $baseData['userName'] = $this->user ? ($this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer') : 'Customer';
        $baseData['orderNo'] = $this->order->no ?? '';
        $baseData['orderSubtotalPrice'] = $this->order->subtotal_price ?? '';
        $baseData['orderTotalPrice'] = round($this->order->paid_amount, 2) ?? '';
        $baseData['orderCreatedAt'] = $this->order->paid_at ?? '';
        $baseData['payMethod'] = $this->order->payment_method ?? '';
        $baseData['billingAddress'] = $billingAddress;
        $baseData['shippingAddress'] = $shippingAddress;
        $baseData['email'] = $this->getOrderEmail();
        $baseData['shippingFee'] = $this->order->shipping_fee_currency - $this->order->shipping_fee_discount_currency;
        $baseData['totalNum'] = $this->order->items->sum('num');
        $baseData['orderPrice'] = $baseData['orderTotalPrice'] - $baseData['shippingFee'];
        $baseData['orderItems'] = $this->order->items->map(function (OrderItem $item) {
            $productInfo = json_decode($item->product_info);
            // 使用订单货币进行价格转换，而不是依赖可能不准确的 price_currency 字段
            $orderCurrency = $this->order->currency;
            $itemPrice = convertPrice($item->price, $orderCurrency) * $item->num;
            $couponDiscount = convertPrice($item->coupon_discount_price, $orderCurrency);
            $finalPrice = round($itemPrice - $couponDiscount, 2);

            return [
                "name" => $item->{'name'} ?? '',
                "imageSrc" => $item->{'image_url'} ?? '',
                "price" => $finalPrice,
                "originalPrice" => $item->{'original_price'} ?? '',
                "num" => $item->{'num'} ?? '',
                "size" => $productInfo->size ?? '',
                "color" => $productInfo->color ?? '',
                "slugTitle" => $item->{'slug_title'} ?? '',
            ];
        })->toArray();
        $baseData['time'] = now()->format('Y-m-d H:i:s');
        $baseData['currency'] = $this->order->currency->currency_code ?? '';
        $baseData['symbol'] = $this->order->currency->symbol ?? '';
    }
    public function fillOrderPaidFailData(&$baseData): void
    {
        if (!$this->order instanceof Order) {
            return;
        }
        $this->order->loadMissing(['billingAddress.country', 'shippingAddress.country']);
        // 初始化地址数组，确保所有键都存在
        $billingAddress = [
            'name' => '',
            'address' => '',
            'state' => '',
            'city' => '',
            'zip' => '',
            'phone' => '',
            'country' => ''
        ];

        $shippingAddress = [
            'name' => '',
            'address' => '',
            'state' => '',
            'city' => '',
            'zip' => '',
            'phone' => '',
            'country' => ''
        ];

        // 如果地址存在，填充数据
        if ($this->order->billingAddress) {
            $billingAddress['name'] = $this->order->billingAddress->first_name . ' ' . $this->order->billingAddress->last_name;
            $billingAddress['address'] = $this->order->billingAddress->address;
            $billingAddress['state'] = $this->order->billingAddress->state;
            $billingAddress['city'] = $this->order->billingAddress->city;
            $billingAddress['zip'] = $this->order->billingAddress->zip;
            $billingAddress['phone'] = $this->order->billingAddress->phone;
            $billingAddress['country'] = $this->order->billingAddress->country->name ?? '';
        }
        if ($this->order->shippingAddress) {
            $shippingAddress['name'] = $this->order->shippingAddress->first_name . ' ' . $this->order->shippingAddress->last_name;
            $shippingAddress['address'] = $this->order->shippingAddress->address;
            $shippingAddress['state'] = $this->order->shippingAddress->state;
            $shippingAddress['city'] = $this->order->shippingAddress->city;
            $shippingAddress['zip'] = $this->order->shippingAddress->zip;
            $shippingAddress['phone'] = $this->order->shippingAddress->phone;
            $shippingAddress['country'] = $this->order->shippingAddress->country->name ?? '';
        }
        // 加载关联
        $baseData['userName'] = $this->user ? ($this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer') : 'Customer';
        $baseData['orderNo'] = $this->order->no ?? '';
        $baseData['orderSubtotalPrice'] = $this->order->subtotal_price ?? '';
        $baseData['orderTotalPrice'] = round($this->order->paid_amount, 2) ?? '';
        $baseData['orderCreatedAt'] = $this->order->paid_at ?? '';
        $baseData['payMethod'] = $this->order->payment_method ?? '';
        $baseData['billingAddress'] = $billingAddress;
        $baseData['shippingAddress'] = $shippingAddress;
        $baseData['email'] = $this->getOrderEmail();
        $baseData['shippingFee'] = $this->order->shipping_fee_currency - $this->order->shipping_fee_discount_currency;
        $baseData['totalNum'] = $this->order->items->sum('num');
        $baseData['orderPrice'] = $baseData['orderTotalPrice'] - $baseData['shippingFee'];
        $baseData['orderItems'] = $this->order->items->map(function (OrderItem $item) {
            $productInfo = json_decode($item->product_info);
            return [
                "name" => $item->{'name'} ?? '',
                "imageSrc" => $item->{'image_url'} ?? '',
                "price" => round($item->{'price_currency'} * $item->{'num'}  - $item->{'coupon_discount_price_currency'}, 2),
                "originalPrice" => $item->{'original_price'} ?? '',
                "num" => $item->{'num'} ?? '',
                "size" => $productInfo->size ?? '',
                "color" => $productInfo->color ?? '',
                "slugTitle" => $item->{'slug_title'} ?? '',
            ];
        })->toArray();
        $baseData['time'] = now()->format('Y-m-d H:i:s');
        $baseData['currency'] = $this->order->currency->currency_code ?? '';
        $baseData['symbol'] = $this->order->currency->symbol ?? '';
    }

    public function fillCouponExpireData(&$baseData): void
    {
        // 优惠券数据直接在 $this->data 根级别
        $coupon = Arr::get($this->data, 'coupon');
        $rule = json_decode($coupon['rules'], true)[0] ?? []; // 强制返回数组，添加默认值

        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['email'] = $this->user->email;
        $baseData['couponName'] = $coupon['name'] ?? '';
        $baseData['couponCode'] = $coupon['code'] ?? '';
        $baseData['couponDesc'] = $coupon['desc'] ?? '';
        $baseData['couponEffectiveEndAt'] = $coupon['effective_end_at'] ?? '';
        $baseData['discount'] = $rule['discount_rate'] ?? 0;

        if (isset($rule['price'])) {
            $baseData['rule'] = 'Orders $' . $rule['price'] . ' +';
        } elseif (isset($rule['quantity'])) {
            $baseData['rule'] = 'Buy ' . $rule['quantity'] . ' GET ' . $rule['discount_rate'] . '%';
        } else {
            $baseData['rule'] = 'Special Offer';
        }
    }

    public function fillCouponData(&$baseData): void
    {
        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['email'] = $this->user->email;
        $baseData['couponName'] = Arr::get($this->data, 'coupon_name');
        $baseData['couponCode'] = Arr::get($this->data, 'coupon_code');
    }

    public function fillVerificationcodeData(&$baseData): void
    {
        $baseData['email_code'] = Arr::get($this->data, 'code');
        $baseData['userName'] = $this->user->first_name ? $this->user->first_name . ' ' . $this->user->last_name : 'Customer';
        $baseData['email'] = $this->user->email;
    }
}
