<?php

namespace App\Http\Controllers;

use App\Models\ContactUsMessage;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Arr;

class ContactUsMessageController extends Controller
{
    public function store(Request $request, ContactUsMessage $ContactUsMessage): JsonResource
    {
        $validated = $request->validate([
            'email' => ['required', 'string', 'email', 'max:255'],
            'name' => ['required', 'string', 'max:255'],
            'message' => ['nullable', 'string'],
        ], [
            'email.required' => 'Email is required!',
            'email.email' => 'Please provide a valid email address!',
        ]);
        DB::beginTransaction();
        try {
            $ContactUsMessage->fill($validated)->save();
            
            Mail::raw($request->message, function ($message) use ($validated) {
                $message->to('<EMAIL>')
                    ->replyTo(Arr::get($validated, 'email'), Arr::get($validated, 'name'))
                    ->subject('客户邮件');
            });
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($ContactUsMessage);
    }
}
