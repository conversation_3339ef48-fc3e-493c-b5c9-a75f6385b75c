<?php

namespace App\Http\Controllers;


use App\Constants\CacheKey;
use App\Models\HelpFaq;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class HelpFaqController extends Controller
{

    /**
     * 获取列表
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        // 缓存
        $list = Cache::remember(CacheKey::HelpFaqList->getKey(), now()->addMinutes(31), function () {
            $list = HelpFaq::query()
                ->with(['icon:id,path,disk,module'])
                ->whereNull('parent_id')
                ->orderBy('sort')->get();
            $first = $list->first();
            $first->{'list'} = $first->children()->orderBy('sort')->take($this->getPerPage())->get();
            return $list;
        });
        return JsonResource::collection($list);
    }

    /**
     * 列表 2
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function list(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(HelpFaq::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('parent_id'),
                AllowedFilter::partial('title'),
            ])
            ->defaultSort('sort')
            ->get();
        return JsonResource::collection($builder);

    }
}
