<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Footer\Footer;

class FooterSeeder extends Seeder
{
    public function run()
    {
        $footer = Footer::create([
            'name' => 'Main Footer',
            'slug' => 'main-footer',
        ]);

        $footer->links()->createMany([
            [
                'label' => 'Privacy Policy',
                'url' => 'https://example.com/privacy-policy',
                'sort' => 1,
            ],
            [
                'label' => 'Terms of Service',
                'url' => 'https://example.com/terms-of-service',
                'sort' => 2,
            ],
            [
                'label' => 'Contact Us',
                'url' => 'https://example.com/contact-us',
                'sort' => 3,
            ],
            [
                'label' => 'About Us',
                'url' => 'https://example.com/about-us',
                'sort' => 4,
            ],
        ]);
        $this->command->info('Seeded: Successfully created a footer.');
    }
}
