<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotion_contents', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('promotion_id')->index()->comment('广告位ID');
            $table->bigInteger('product_id')->index()->nullable()->comment('商品ID(仅产品类型广告位可用)');
            $table->bigInteger('collection_id')->index()->nullable()->comment('集合ID(仅集合/产品类型广告位可用)');
            $table->string('title', 32)->nullable()->comment('标题');
            $table->string('link')->nullable()->comment('链接');
            $table->bigInteger('attachment_id')->nullable()->comment('图片id');
            $table->Integer('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('子广告位内容');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_contents');
    }
};
