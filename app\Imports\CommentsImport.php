<?php

namespace App\Imports;

use App\Models\Attachment;
use App\Models\Comment;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;

class CommentsImport implements ToArray, WithHeadingRow, WithEvents, WithValidation, SkipsEmptyRows, SkipsOnFailure
{
    use RegistersEventListeners;

    /**
     * @var \Illuminate\Database\Eloquent\Collection
     */
    protected Collection $projects;

    public static function beforeImport(BeforeImport $event): void
    {
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            // 确保 $value 是字符串类型
            if (!is_string($value) && !is_numeric($value)) {
                return null;
            }
            $value = (string) $value;
            return Arr::get(static::$header, $value, $value);
        });
        HeadingRowFormatter::default('slug');
    }

    public static function afterImport(AfterImport $event): void
    {
        HeadingRowFormatter::reset();
    }

    // 注意这里产品的sku和变体的sku是相反使用的
    public static array $header = [
        'sku' => 'spu',
        'spu' => 'sku',
        'first_name' => 'first_name',
        'last_name' => 'last_name',
        'email' => 'email',
        'content' => 'content',
        'grade' => 'grade',
        'status' => 'status',
        'enabled' => 'enabled',
        // 'top_at' => 'top_at',
        'created_at' => 'created_at',
        // 'images' => 'images',
    ];

    public function rules(): array
    {
        return [
            'spu' => ['required', new Exists(Product::class, 'spu')],
            'spu' => 'sku',
            'first_name' => ['required'],
            'last_name' => ['nullable'],
            'email' => ['nullable'],
            'content' => ['required'],
            'grade' => ['required'],
            'status' => ['required', 'int', new Enum(CommentStatusEnum::class)],
            'enabled' => ['required', 'bool'],
            // 'top_at' => 'top_at',
            'created_at' => ['required'],
            // 'images' => 'images',
        ];
    }

    public function array(array $array): array
    {
        // 验证数组是否为空
        if (empty($array)) {
            return ['Excel文件为空或没有数据行'];
        }

        // 验证表头
        // $headerValidation = $this->validateHeaders($array);
        // if (!empty($headerValidation)) {
        //     return $headerValidation;
        // }

        // todo:: 优化（后期需要优化修改）
        $products = Product::query()->select(['id', 'spu'])->get();
        // $productVariants = ProductVariant::query()->select(['id', 'product_id', 'sku'])->get();
        $attachments = Attachment::query()->select('id')->get();

        $errors = [];
    
        foreach ($array as $key => $item) {
            $line = $key + 2;
            $comment = new Comment();

            if (!Arr::exists($item, 'spu') && !Arr::exists($item, 'sku')) {
               $errors[] ="{$line}行数据错误，sku或spu不能为空";
               continue;
            }

            try {
                $product_id = null;
                $product_variant_id = null;
                // 注意这里产品的sku和变体的sku是相反使用的
                if (Arr::exists($item, 'spu')) {
                    $product_id = $products->where('spu', Arr::get($item, 'spu'))->value('id');
                }
                // if (Arr::exists($item, 'sku')) {
                //     $product_variant_id = $productVariants->where('sku', Arr::get($item, 'sku'))->value('id');
                //     $product_id = $productVariants->where('sku', Arr::get($item, 'sku'))->value('product_id');
                // }
                // 判断产品数据是否存在
                if (!$product_id && !$product_variant_id) {
                    $errors[] ="{$line}行数据错误，sku或spu找不到对应的产品";
                    continue;
                }

                $data['product_id'] = (int) $product_id;
                // $data['product_variant_id'] = $product_variant_id;
                // 处理可能的编码问题
                $data['first_name'] = $this->cleanString(Arr::get($item, 'first_name'));
                $data['last_name'] = $this->cleanString(Arr::get($item, 'last_name'));
                $data['email'] = $this->cleanString(Arr::get($item, 'email'));
                $data['content'] = $this->cleanString(Arr::get($item, 'content'));
                $data['grade'] = (float) Arr::get($item, 'grade');
                $data['status'] = (int) Arr::get($item, 'status');
                $data['enabled'] = (int) Arr::get($item, 'enabled');
                // $data['top_at'] = Arr::get($item, 'top_at');
                // 处理字符编码问题
                $dateValue = Arr::get($item, 'created_at');
                // logger()->info('原始日期: ' . $dateValue);
                if ($dateValue && is_string($dateValue)) {
                    // 清理可能的编码问题
                    $dateValue = mb_convert_encoding($dateValue, 'UTF-8', 'UTF-8');
                    // 移除不可见字符
                    $dateValue = preg_replace('/[\x00-\x1F\x7F]/', '', $dateValue);
                }
                $data['created_at'] = trim($dateValue);
                // logger()->info('处理后的日期: ' . $data['created_at']);
                $comment->fill($data)->save();
                // 图片
                if (Arr::exists($item, 'images')) {
                    $images = explode(',', Arr::get($item, 'images'));
                    $comment->images()->sync($attachments->whereIn('id', $images)->pluck('id')->toArray());
                }
            } catch (\Throwable $e) {
                $errors[] ="{$line}行数据错误，{$e->getMessage()}";
                continue;
            }
        }

        return $errors;
    }

    public function onFailure(Failure ...$failures)
    {
    }

    /**
     * 验证表头
     *
     * @param array $array
     * @return array
     */
    private function validateHeaders(array $array): array
    {
        if (empty($array)) {
            return ['数据为空'];
        }

        $firstRow = reset($array);
        if (!is_array($firstRow)) {
            return ['数据格式错误'];
        }

        $requiredHeaders = ['spu', 'first_name', 'content', 'grade', 'status', 'enabled', 'created_at'];
        $missingHeaders = [];

        foreach ($requiredHeaders as $header) {
            if (!array_key_exists($header, $firstRow)) {
                $missingHeaders[] = $header;
            }
        }

        if (!empty($missingHeaders)) {
            return ['缺少必需的表头列: ' . implode(', ', $missingHeaders)];
        }

        return [];
    }

    /**
     * 清理字符串编码问题
     *
     * @param mixed $value
     * @return string|null
     */
    private function cleanString($value)
    {
        if (empty($value)) {
            return $value;
        }

        if (!is_string($value)) {
            return $value;
        }

        try {
            // 检测并转换编码
            $encoding = mb_detect_encoding($value, ['UTF-8', 'GBK', 'GB2312', 'ISO-8859-1'], true);
            if ($encoding && $encoding !== 'UTF-8') {
                $value = mb_convert_encoding($value, 'UTF-8', $encoding);
            }

            // 确保是有效的 UTF-8
            $value = mb_convert_encoding($value, 'UTF-8', 'UTF-8');

            // 移除控制字符但保留换行符
            $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);

            return trim($value);
        } catch (\Exception $e) {
            // 如果转换失败，返回原值或空字符串
            return is_string($value) ? trim($value) : '';
        }
    }
}
