<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\BrandMasterProgram;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class BrandMasterProgramController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::BrandMasterProgramsUpdate)->except(['index', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::BrandMasterProgramsIndex, Permissions::BrandMasterProgramsUpdate)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $BrandMasterProgram = QueryBuilder::for(BrandMasterProgram::class, $request)
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $BrandMasterProgram->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function update(Request $request, BrandMasterProgram $brandMasterProgram): JsonResource
    {
        $validated = $request->validate([
            'Instagram' =>['nullable', 'string', 'max:255'],
            'Facebook' => ['nullable', 'string', 'max:255'],
            'TikTok' => ['nullable', 'string', 'max:255'],
            'YouTube' =>['nullable', 'string', 'max:255'],
        ]);

        DB::beginTransaction();
        try {
            $brandMasterProgram->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($brandMasterProgram);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(BrandMasterProgram::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            BrandMasterProgram::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $BrandMasterProgram = QueryBuilder::for(BrandMasterProgram::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('email'),
            ])
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $BrandMasterProgram->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(BrandMasterProgram $BrandMasterProgram): JsonResource
    {
        return JsonResource::make($BrandMasterProgram);
    }
}
