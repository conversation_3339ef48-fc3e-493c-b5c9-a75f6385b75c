<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('zone_id');
            $table->string('iso_code',64);
            $table->string('name',64);
            $table->boolean('need_zip_code')->default(0);
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('国家');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
