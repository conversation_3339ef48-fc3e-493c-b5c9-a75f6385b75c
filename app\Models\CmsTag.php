<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CmsTag extends MiddleModel
{
    use SoftDeletes;
    use HasFactory;
    public $timestamps = true;

    protected $fillable = [
        'name', 'status', 'admin_user_id'
    ];

    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class, 'admin_user_id');
    }
} 