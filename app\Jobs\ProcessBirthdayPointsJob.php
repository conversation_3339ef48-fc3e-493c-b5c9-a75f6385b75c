<?php

namespace App\Jobs;

use App\Models\User\User;
use App\Services\MembershipService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessBirthdayPointsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 执行任务
     */
    public function handle(MembershipService $membershipService): void
    {
        $today = now();
        
        // 查找今天生日的用户
        $birthdayUsers = User::whereNotNull('birthday')
            ->whereMonth('birthday', $today->month)
            ->whereDay('birthday', $today->day)
            ->get();

        $processedCount = 0;
        $errorCount = 0;

        foreach ($birthdayUsers as $user) {
            try {
                $pointRecord = $membershipService->processBirthdayPoints($user->id);
                
                if ($pointRecord) {
                    $processedCount++;
                    Log::info('Birthday points processed successfully', [
                        'user_id' => $user->id,
                        'points' => $pointRecord->earned_points,
                        'membership_level' => $pointRecord->membershipLevel->name,
                    ]);
                }
            } catch (\Exception $e) {
                $errorCount++;
                Log::error('Failed to process birthday points', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Birthday points job completed', [
            'total_birthday_users' => $birthdayUsers->count(),
            'processed_count' => $processedCount,
            'error_count' => $errorCount,
        ]);
    }
}
