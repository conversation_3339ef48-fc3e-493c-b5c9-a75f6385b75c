<?php

namespace App\Exports;

use App\Models\User\User as UserModel;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;


class UserExport implements FromQuery, WithHeadings, WithMapping, WithColumnFormatting
{
    use Exportable;


    public function __construct(public Builder $builder)
    {
//
    }

    public function headings(): array
    {
        return [
            '用户id',
            '微信昵称',
            '手机号码',
            '注册时间',
        ];
    }

    /**
     * @return Builder
     */
    public function query(): Builder
    {
        return $this->builder;
    }

    /**
     * @return array
     * @var UserModel $invoice
     */
    public function map($invoice): array
    {
        return [
            $invoice->id,
            $invoice->name,
            $invoice->phone,
            $invoice->created_at ? Date::dateTimeToExcel($invoice->created_at) : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }
}
