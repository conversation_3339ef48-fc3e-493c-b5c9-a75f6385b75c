<?php

namespace App\Jobs\Order;

use App\Constants\QueueKey;
use App\Models\Order\Order;
use App\Services\MembershipService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Enums\Order\OrderPaidStatusEnum;

class ProcessMembershipRewards implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 处理会员等级升级和积分奖励
     */
    public function __construct(public Order $order)
    {
        $this->onQueue(QueueKey::Order->value);
    }

    /**
     * Execute the job.
     */
    public function handle(MembershipService $membershipService): void
    {
        try {
            // 只处理已支付的订单
            if ($this->order->paid_status !== OrderPaidStatusEnum::Paid) {
                return;
            }

            // 只处理有用户的订单
            if (!$this->order->user_id) {
                return;
            }

            // 处理订单积分奖励（包含会员等级检查和升级）
            $pointRecord = $membershipService->processOrderPoints($this->order);

        } catch (\Exception $e) {
            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        logger()->error('ProcessMembershipRewards job failed permanently', [
            'order_id' => $this->order->id,
            'user_id' => $this->order->user_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
