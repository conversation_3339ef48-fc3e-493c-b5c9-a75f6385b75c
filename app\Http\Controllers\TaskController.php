<?php

namespace App\Http\Controllers;

use App\Models\Task\TaskPointConfig;
use App\Models\Task\UserTaskRecord;
use App\Services\TaskService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class TaskController extends Controller
{
    protected TaskService $taskService;

    public function __construct(TaskService $taskService)
    {
        $this->taskService = $taskService;
    }

    /**
     * 获取所有任务列表（标记执行状态）
     */
    public function getAvailableTasks(Request $request): JsonResource
    {
        $user = Auth::user();

        // 获取所有激活的任务配置
        $tasks = TaskPointConfig::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        $taskList = [];

        foreach ($tasks as $task) {
            $taskData = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'task_name' => $task->task_name,
                'channel' => $task->channel,
                'points_per_action' => $task->points_per_action,
                'daily_limit' => $task->daily_limit,
                'total_limit' => $task->total_limit,
                'description' => $task->description,
                'extra_config' => $task->extra_config,
                'sort_order' => $task->sort_order,
            ];

            // 只有登录用户才返回执行状态信息
            if ($user) {
                // 获取用户今日完成次数
                $todayCount = UserTaskRecord::where('user_id', $user->id)
                    ->where('task_type', $task->task_type)
                    ->where('channel', $task->channel)
                    ->today()
                    ->count();

                // 获取用户总完成次数
                $totalCount = UserTaskRecord::where('user_id', $user->id)
                    ->where('task_type', $task->task_type)
                    ->where('channel', $task->channel)
                    ->count();

                // 获取今日获得积分
                $todayPoints = UserTaskRecord::where('user_id', $user->id)
                    ->where('task_type', $task->task_type)
                    ->where('channel', $task->channel)
                    ->today()
                    ->sum('earned_points');

                // 判断是否可执行
                $canExecute = $task->canUserExecute($user->id);

                // 判断今日是否已执行
                $executedToday = $todayCount > 0;

                // 计算剩余次数
                $remainingCount = -1; // 默认无限制
                if ($task->daily_limit > 0) {
                    $remainingCount = max(0, $task->daily_limit - $todayCount);
                }

                // 添加执行状态信息
                $taskData = array_merge($taskData, [
                    'can_execute' => $canExecute,
                    'executed_today' => $executedToday,
                    'today_count' => $todayCount,
                    'total_count' => $totalCount,
                    'today_points' => $todayPoints,
                    'remaining_count' => $remainingCount,
                ]);
            }

            $taskList[] = $taskData;
        }

        return JsonResource::make([
            'success' => true,
            'data' => $taskList,
        ]);
    }

    /**
     * 执行任务
     */
    public function executeTask(Request $request): JsonResource
    {
        $validated = $request->validate([
            'task_type' => 'required|string|max:50',
            'channel' => 'nullable|string|max:50',
            'task_data' => 'nullable|array',
            'external_id' => 'nullable|string',
        ]);

        $user = Auth::user();

        try {
            $result = $this->taskService->executeTask(
                $user,
                $validated['task_type'],
                $validated['channel'] ?? null,
                $validated['task_data'] ?? []
            );

            if ($result) {
                return JsonResource::make([
                    'success' => true,
                    'message' => '任务执行成功',
                    'data' => $result,
                ]);
            } else {
                return JsonResource::make([
                    'success' => false,
                    'message' => '任务执行失败，可能已达到限制或任务不存在',
                ]);
            }
        } catch (\Exception $e) {
            return JsonResource::make([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 获取用户任务统计
     */
    public function getTaskStats(): JsonResource
    {
        $user = Auth::user();
        $stats = $this->taskService->getUserTaskStats($user);

        return JsonResource::make([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 获取用户任务记录
     */
    public function getTaskRecords(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();

        $records = QueryBuilder::for(UserTaskRecord::class)
            ->where('user_id', $user->id)
            ->with(['taskConfig'])
            ->allowedFilters([
                AllowedFilter::exact('task_type'),
                AllowedFilter::exact('channel'),
            ])
            ->allowedSorts(['created_at', 'points_earned'])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }
}
