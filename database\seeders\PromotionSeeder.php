<?php

namespace Database\Seeders;

use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\Promotion\PromotionAlignmentMethodEnum;
use App\Models\Enums\Promotion\PromotionStatusEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Promotion;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;


class PromotionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $arr = [
            [
                'title' => 'Browse The Range',
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'index_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::CenterAlignment,
                'type' => PromotionTypeEnum::CategoryType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::WebEquipment,
                'contents' => [
                    [
                        'collection_id' => 1,
                        'product_id' => NULL,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => 2,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => NULL,
                        'attachment_id' => 3,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => 'Browse The Range',
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'index_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::CenterAlignment,
                'type' => PromotionTypeEnum::CategoryType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::MobileEquipment,
                'contents' => [
                    [
                        'collection_id' => 1,
                        'product_id' => NULL,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ]
                ]
            ],
            [
                'title' => 'Editor\'s Picks' ,
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'index_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::LeftAlignment,
                'type' => PromotionTypeEnum::ProductType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 1,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => 1,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 2,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 3,
                        'title' => NUll,
                        'link' => NULL,
                        'attachment_id' => NUll,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => 'Editor\'s Picks' ,
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'product_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::LeftAlignment,
                'type' => PromotionTypeEnum::ProductType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => 1,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 2,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 3,
                        'title' => NUll,
                        'link' => NULL,
                        'attachment_id' => NUll,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => 'Editor\'s Picks' ,
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'cart_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::LeftAlignment,
                'type' => PromotionTypeEnum::ProductType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => 1,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 2,
                        'title' => NULL,
                        'link' => NULL,
                        'attachment_id' => NULL,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => 3,
                        'title' => NUll,
                        'link' => NULL,
                        'attachment_id' => NUll,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => '50+ Beautiful suits inspiration' ,
                'description' => 'Our designer already made a lot of beautiful prototipe of',
                'button_text' => 'Explore More',
                'button_link' => 'https://www.google.com',
                'button_background_color' => '#FFB54C',
                'position' => 'index_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::RightAlignment,
                'type' => PromotionTypeEnum::BoxSetType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 2,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 3,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => 'Beautiful suits' ,
                'description' => 'From your imagination to your doorstep. Create a fully personalized bra and panty set with just a few words.',
                'button_text' => 'Explore More',
                'button_link' => 'https://www.google.com',
                'button_background_color' => '#FFB54C',
                'position' => 'index_middle',
                'alignment_method' => PromotionAlignmentMethodEnum::LeftAlignment,
                'type' => PromotionTypeEnum::DesignType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 3,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NUll,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 3,
                        'sort' => 2
                    ]
                ]
            ],
            [
                'title' => NULL,
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'top_nav_search_box',
                'alignment_method' => PromotionAlignmentMethodEnum::CenterAlignment,
                'type' => PromotionTypeEnum::CategoryType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::WebEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ],
                    [
                        'collection_id' => NULL,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 3,
                        'sort' => 2
                    ],
                    [
                        'collection_id' => NULL,
                        'product_id' => NULL,
                        'title' => 'Fairy',
                        'link' => 'https://www.google.com',
                        'attachment_id' => 4,
                        'sort' => 3
                    ]
                ]
            ],
            [
                'title' => NULL,
                'description' => NULL,
                'button_text' => NULL,
                'button_link' => NUll,
                'button_background_color' => NULL,
                'position' => 'category_banner',
                'alignment_method' => PromotionAlignmentMethodEnum::CenterAlignment,
                'type' => PromotionTypeEnum::BannerType,
                'status' => PromotionStatusEnum::StatusEnable,
                'sort' => 0,
                'equipment_type' => EquipmentTypeEnum::AllEquipment,
                'contents' => [
                    [
                        'collection_id' => NULL,
                        'product_id' => NULL,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 1,
                        'sort' => 0
                    ],
                    [
                        'collection_id' => NUll,
                        'product_id' => NULL,
                        'title' => NULL,
                        'link' => 'https://www.google.com',
                        'attachment_id' => 2,
                        'sort' => 1
                    ],
                ]
            ],
        ];
        foreach ($arr as $item) {
            $promotion = Promotion::query()->create(Arr::except($item, ['contents']));
            $contents = Arr::get($item, 'contents');
            $promotion->contents()->createMany($contents);
        }
    }
}
