<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('cms_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('标签名');
            $table->boolean('status')->default(true)->comment('状态：1启用 0停用');
            $table->unsignedBigInteger('admin_user_id')->comment('添加人');
            $table->timestamps(); // created_at, updated_at
            $table->softDeletes(); // deleted_at
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cms_tags');
    }
}; 