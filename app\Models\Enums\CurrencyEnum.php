<?php

namespace App\Models\Enums;
use Illuminate\Support\Arr;

enum CurrencyEnum: string
{
    case Before = 'before';
    case After = 'after';


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }
    public static function list(): array
    {
        return [
            self::Before->value => '之前',
            self::After->value => '之后',
        ];
    }
}
