<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShippingFee extends MiddleModel
{
    use SoftDeletes;
    use HasFactory;
    // 运费明细表
    protected $table = 'shipping_fees';

    // 可批量赋值字段
    protected $fillable = [
        'country',        // 国家iso_code
        'min_weight',     // 区间最小重量(kg)
        'max_weight',     // 区间最大重量(kg)
        'shipping_fee',   // 运费
        'shipping_id',    // 运费模板ID（可选）
    ];

    /**
     * 关联到 shippings 表（运费模板）
     */
    public function shipping()
    {
        return $this->belongsTo(Shipping::class, 'shipping_id');
    }
} 