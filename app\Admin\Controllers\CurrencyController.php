<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\Enums\CurrencyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\CountryCurrency;

class CurrencyController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CurrenciesUpdate)->only(['store', 'update', 'destroy',]);
        $this->hasPermissionOr(Permissions::CurrenciesUpdate, Permissions::CurrenciesIndex)->only(['index', 'show']);
    }

    /**
     * 新增
     * @param Request $request
     * @return JsonResource
     */
    public function store(Request $request): JsonResource
    {
        $validated = $request->validate([
            'currency_code'     => ['required', 'string', 'max:3', 'unique:currencies,currency_code'],
            'symbol'            => ['required', 'string', 'max:10'],
            'currency_name'     => ['required', 'string', 'max:255'],
            'exchange_rate'     => ['required', 'numeric'],
            'symbol_position'   => [new Enum(CurrencyEnum::class)],
            'active'            => ['required', 'boolean'],
            'is_default'        => ['required', 'boolean']
        ]);

        DB::beginTransaction();
        try {
            if ($validated['is_default'] === true) {
                Currency::where('is_default', true)
                    ->update(['is_default' => false]);
            }
            $currency = Currency::create($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($currency);
    }

    /**
     * 修改
     * @param Currency $currency
     * @param Request $request
     * @return JsonResource
     */

    public function update(Request $request, Currency $currency): JsonResource
    {
        $validated = $request->validate([
            'currency_code'     => ['required', 'string', 'max:3', 'unique:currencies,currency_code,' . $currency->id],
            'symbol'            => ['required', 'string', 'max:10'],
            'currency_name'     => ['required', 'string', 'max:255'],
            'exchange_rate'     => ['required', 'numeric'],
            'symbol_position'   => [new Enum(CurrencyEnum::class)],
            'active'            => ['required', 'boolean'],
            'is_default'        => ['required', 'boolean'],
        ]);

        DB::beginTransaction();
        try {
            if ($validated['is_default'] === true) {
                Currency::where('is_default', true)
                    ->where('id', '!=', $currency->id)
                    ->update(['is_default' => false]);
            }
            $currency->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($currency);
    }

    /**
     * 列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Currency::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('currency_code'),
                AllowedFilter::partial('currency_name'),
                AllowedFilter::exact('active')
            ])
            ->allowedSorts(['currency_code', 'currency_name'])
            ->defaultSort('-id');

        $currencies = $builder->paginate($this->getPerPage());

        return JsonResource::collection($currencies);
    }

    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Currency::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        // 检查是否有 active 状态为 true 的货币
        $activeCount = Currency::query()->whereIn('id', $ids)->where('active', true)->count();
        if ($activeCount > 0) {
            throw new DataException('存在启用状态的货币，无法删除！');
        }
        DB::beginTransaction();
        try {
            // 先删除 country_currencies 表中对应的 currency_code
            $currencyCodes = Currency::query()->whereIn('id', $ids)->pluck('currency_code')->toArray();
            CountryCurrency::query()->whereIn('currency_code', $currencyCodes)->delete();
            // 再删除 currencies 表
            Currency::query()->whereIn('id', $ids)->delete();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    /**
     * 详情
     * @param Currency $currency
     * @return JsonResource
     */
    public function show(Currency $currency): JsonResource
    {
        return JsonResource::make($currency);
    }


}
