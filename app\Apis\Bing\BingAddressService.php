<?php

namespace App\Apis\Bing;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BingAddressService
{
    public static function autoSuggest(string $query, string $ct)
    {
        $param = [
            'query' => $query,
            'countryCode' => $ct,
            'limit' => 10,
        ];
        $response = BingAddressClient::getInstance()->get('/v1/search/autocomplete', $param);
        try {
            $response = $response->json();
            if ($response['meta']['code'] == 200) {
                return $response['addresses'];
            }
            return [];
        } catch (\Throwable $e) {
            Log::error('Bing Search' . $e->getMessage());
        }
        return [];
    }
}
