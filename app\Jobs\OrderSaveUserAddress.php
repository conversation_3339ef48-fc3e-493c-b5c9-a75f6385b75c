<?php

namespace App\Jobs;

use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class OrderSaveUserAddress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 重试间隔时间（秒）
     */
    public int $backoff = 60;
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = $this->order;
            $user = $this->order->user;
            if (!$user) {
            return;
        }
        $order->loadMissing(['billingAddress', 'shippingAddress']);
        if ($order->billingAddress->is_save) {
            $this->saveAddressIfNotExists($user, $order->billingAddress, true);
        }
        if ($order->shippingAddress->is_save) {
            $this->saveAddressIfNotExists($user, $order->shippingAddress, false);
        }
        } catch (\Exception $e) {
            throw $e;
        }
    }
  
    /**
     * 保存地址（如果不存在）
     */
    private function saveAddressIfNotExists($user, $orderAddress, $isBilling = true)
    {
        $addressData = Arr::only($orderAddress->toArray(), ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip', 'phone']);

        // 检查是否已存在相同的地址
        $existingAddress = $user->addresses()
            ->where('first_name', $addressData['first_name'])
            ->where('last_name', $addressData['last_name'])
            ->where('country_id', $addressData['country_id'])
            ->where('address', $addressData['address'])
            ->where('city', $addressData['city'])
            ->where('zip', $addressData['zip'])
            ->where('phone', $addressData['phone'])
            ->first();

        if ($existingAddress) {
            // 如果地址已存在，只更新默认状态
            if ($isBilling) {
                $user->addresses()->update(['is_billing_default' => false]);
                $existingAddress->update([
                    'is_billing_default' => true,
                    'is_both_default' => $existingAddress->is_shipping_default
                ]);
            } else {
                $user->addresses()->update(['is_shipping_default' => false]);
                $existingAddress->update([
                    'is_shipping_default' => true,
                    'is_both_default' => $existingAddress->is_billing_default
                ]);
            }
        } else {
            // 地址不存在，创建新地址
            if ($isBilling) {
                $addressData['is_billing_default'] = true;
                $addressData['is_shipping_default'] = false;
                $user->addresses()->update(['is_billing_default' => false, 'is_both_default' => false]);
            } else {
                $addressData['is_shipping_default'] = true;
                $addressData['is_billing_default'] = false;
                $user->addresses()->update(['is_shipping_default' => false, 'is_both_default' => false]);
            }

            $addressData['is_both_default'] = false;
            $user->addresses()->create($addressData);
        }
<<<<<<< HEAD
        } catch (\Exception $e) {
            throw $e;
        }
=======
>>>>>>> wrr
    }
}
