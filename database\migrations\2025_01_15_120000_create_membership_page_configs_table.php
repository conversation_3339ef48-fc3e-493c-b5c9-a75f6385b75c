<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_page_configs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('pc_login_before_image_id')->nullable()->comment('PC端登录前banner图片ID');
            $table->bigInteger('pc_login_after_image_id')->nullable()->comment('PC端登录后banner图片ID');
            $table->bigInteger('mobile_login_before_image_id')->nullable()->comment('移动端登录前banner图片ID');
            $table->bigInteger('mobile_login_after_image_id')->nullable()->comment('移动端登录后banner图片ID');
            $table->boolean('is_enabled')->default(true)->comment('是否启用');
            $table->bigInteger('admin_user_id')->nullable()->comment('操作管理员ID');
            $table->timestamps();

            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_page_configs');
    }
};
