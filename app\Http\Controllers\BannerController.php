<?php

namespace App\Http\Controllers;

use App\Constants\CacheKey;
use App\Models\Banner\Banner;
use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\SysConfigKeyEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Arr;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class BannerController extends Controller
{

    /**
     * 列表
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $config = sysConfigService()->get(SysConfigKeyEnum::BannerCarouselConfig);
        $banner_carousel = Arr::get($config, 'value');
        $uniqueKey = md5(json_encode($request->all()) . $banner_carousel);
        // 缓存
        $list = Cache::remember(CacheKey::BannerBuilderList->getKey($uniqueKey), now()->addDays(1), function () use ($request, $banner_carousel) {
            // 查询条件
            $query = QueryBuilder::for(Banner::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('equipment_type', 'equipment_type')->default(EquipmentTypeEnum::WebEquipment),
                AllowedFilter::callback('group_slug', 'groups.slug'),
            ])
            ->where('is_visible', true)
            ->allowedSorts(['id', 'sort'])
            ->defaultSort('sort')
            ->with(['image:id,path,file_type,disk,module']);
            if ($banner_carousel) {
                // 如果是 true，查询包含 image 的图片
                $query->whereHas('image', function ($query) {
                    $query->where('file_type', 'like', 'image/%');
                });
            } else {
                // 如果是 false，查询包含 video 的视频
                $query->whereHas('image', function ($query) {
                    $query->where('file_type', 'like', 'video/%');
                });
            }
            return $query->get();
        });
        return JsonResource::collection($list)->additional(['banner_carousel' => $banner_carousel]);
    }

}
