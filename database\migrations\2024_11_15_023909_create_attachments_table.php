<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->string('file_name')->default('')->comment("文件名");
            $table->string('file_type', 255)->default('')->comment("文件类型");
            $table->integer('file_size')->default(0)->comment("文件大小，单位字节");
            $table->string('file_hash')->index();  // 哈希值唯一
            $table->string('disk')->default("")->comment('驱动');
            $table->string('module')->default("")->comment("所属模块");
            $table->dateTime('upload_time')->comment("上传时间");
            $table->text('path')->comment("文件存储路径");
            $table->bigInteger('user_id')->nullable()->comment("上传用户");
            $table->timestamps();
            $table->timestamp('deleted_at')->nullable();

            $table->comment('附件表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attachments');
    }
};
