<?php

namespace App\Http\Middleware;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\ExternalClient;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ExternalSign
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $appKey = $request->header('App-Key');
        $sign = $request->input('sign');
        $nonce = $request->input('nonce');
        if (!$appKey || !$sign || !$nonce) {
            throw new DataException("签名错误", ErrorCode::ExternalSingError);
        }
        $app = Cache::remember(CacheKey::ExternalClientKey->getKey($appKey), now()->addDay(), function () use ($appKey) {
            return ExternalClient::query()->where('key', $appKey)->first();
        });
        if (!$app instanceof ExternalClient) {
            throw new DataException("客户端不存在", ErrorCode::ExternalSingError);
        }
        $timestamp = $request->input('timestamp');
        if (Carbon::createFromTimestamp($timestamp)->addMinutes(5)->lte(now())) {
            throw new DataException("签名已过期", ErrorCode::ExternalSingError);
        }
        if (md5("{$appKey}{$app->secret}{$timestamp}{$nonce}") != $sign) {
            throw new DataException("签名错误", ErrorCode::ExternalSingError);
        }

        return $next($request);
    }
}
