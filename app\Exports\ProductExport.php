<?php

namespace App\Exports;

use App\Models\User\User as UserModel;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;


class ProductExport implements FromQuery, WithHeadings, WithMapping, WithColumnFormatting
{
    use Exportable;


    public function __construct(public Builder $builder)
    {
        //
    }

    public function headings(): array
    {
        return [
            'sku',
            'title',
            'desc',
            'price',
            'original_price',
            'reduction_price',
            'reduction_percent',
            'color',
            'size',
            'image',
            'material',
            'product_active',
            'variant_active',
            'brand',
            'manufacturer',
            'project'
        ];
    }

    /**
     * @return Builder
     */
    public function query(): Builder
    {
        return $this->builder;
    }

    /**
     * @return array
     * @var UserModel $invoice
     */
    public function map($invoice): array
    {
        return [
            $invoice->product->sku,
            $invoice->product->title,
            $invoice->product->desc,
            $invoice->price,
            $invoice->original_price,
            (float) $invoice->original_price - $invoice->price,
            getRateNumber($invoice->price, $invoice->original_price),
            $invoice->colorAttribute?->value,
            $invoice->sizeAttribute?->value,
            $invoice->image?->url,
            $invoice->product->material->name,
            (int) $invoice->product->is_publish ? 1 : 0,
            (int) $invoice->product->is_publish ? ($invoice->is_publish ? 1 : 0) : 0,
            'Ardoreva',
            '苗梦婷',
            '内衣项目'
        ];
    }

    public function columnFormats(): array
    {
        return [];
    }
}
