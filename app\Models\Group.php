<?php

namespace App\Models;

use App\Models\User\User;
use App\Models\User\UserGroup;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Group extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    /**
     * 用户id列表
     * @return HasMany
     */
    public function userIds(): HasMany
    {
        return $this->hasMany(UserGroup::class);
    }

    /**
     * 用户
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            UserGroup::class,
            'group_id',
            'user_id',
            'id',
            'id'
        );
    }
}
