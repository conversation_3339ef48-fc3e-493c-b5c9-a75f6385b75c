<?php

namespace App\Models\Enums\Comment;

use Illuminate\Support\Arr;

enum CommentStatusEnum: int
{
    case Pending = 0;
    case Approved = 1;
    case Rejected = 2;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Pending->value => 'Pending', // 待审核
            self::Approved->value => 'Approved', // 已通过
            self::Rejected->value => 'Rejected' // 已拒绝
        ];
    }
}
