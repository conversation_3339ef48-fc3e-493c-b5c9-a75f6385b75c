<?php

namespace App\Services\CouponGrant\Strategies;

use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRecord;
use App\Models\CouponGrantRule;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Jobs\EmailRuleNotice;
use App\Models\Enums\EmailRuleEventEnum;
use App\Constants\QueueKey;
use App\Models\Enums\CouponGrantType;

abstract class RuleStrategy
{

    public function __construct(public CouponGrantRule $rule, public Model $model) {}

    /**
     * 发放
     * @return Coupon|false|void
     * @throws \Throwable
     */
    public function grant($is_framed = false)
    {
        // 检查能否发放优惠券
        if (!$this->checkCanGrant()) {
            return false;
        }
        if (!$coupon = $this->getCoupon()) {
            return false;
        }
        if (!$user = $this->getUser()) {
            return false;
        };

        // 同样的对象没发过才会发
        if (!$this->rule->records()
            ->where('user_id', $user->id)
            ->where('source_type', get_class($this->model))
            ->where('source_id', $this->model->id)
            ->exists()) {
            try {
                DB::beginTransaction();
                $giveOk = userService()->giveUserCoupon($user, $coupon, $this->rule, $this->rule->effective_end_at, $is_framed);
                // 记录发给谁, 发了哪一张优惠券
                if ($giveOk) {
                    $record = new CouponGrantRecord([
                        'user_id' => $user->id,
                        'user_coupon_id' => $giveOk->id,
                    ]);
                    $record->source()->associate($this->model);
                    $recordOk = $this->rule->records()->save($record);
                    if (!$recordOk) {
                        throw new \Exception("发放优惠券失败, 记录创建结果{$recordOk},优惠券发放结果{$giveOk}");
                    }
                    $this->rule->increment('grant_count');
                    //规则中开启了发送邮件
                    if ($this->rule->send_email) {
                        $emailEvent = match ($this->rule->type) {
                            CouponGrantType::FirstOrder => EmailRuleEventEnum::FirstOrder,
                            CouponGrantType::Subscribed => EmailRuleEventEnum::SubscribeSuccess,
                            CouponGrantType::Comment => EmailRuleEventEnum::Comment,
                            CouponGrantType::RegisterUser => EmailRuleEventEnum::Register,
                        };
                        // 准备额外数据
                        $extraData = [
                            'coupon_id' => $coupon->id,
                            'coupon_name' => $coupon->name,
                            'coupon_code' => $coupon->code,
                        ];

                        EmailRuleNotice::dispatch($emailEvent, user: $user, data: $extraData)
                            ->onQueue(QueueKey::Default->value);
                    }
                }
                DB::commit();
            } catch (\Throwable $exception) {
                logger()->error("CouponGrantCheck : " . $exception->getMessage());
                DB::rollBack();
            }
            return $coupon;
        }
        return false;
    }


    public function checkCanGrant(): bool
    {
        return false;
    }

    public function getUser(): ?User
    {
        return $this->model->user;
    }


    public function getCoupon(): ?Coupon
    {
        return $this->rule->coupon;
    }
}
