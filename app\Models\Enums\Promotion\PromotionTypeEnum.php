<?php

namespace App\Models\Enums\Promotion;

use App\Models\Attachment;
use App\Models\Collection;
use App\Models\Mode;
use App\Models\Product\Product;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;

enum PromotionTypeEnum: int
{
    case CategoryType = 1;
    case ProductType = 2;
    case BoxSetType = 3;
    case DesignType = 4;
    case BannerType = 5;
    case SearchNavType = 6;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::CategoryType->value => 'Category', // 分类
            self::ProductType->value => 'Product', // 产品
            self::BoxSetType->value => 'BoxSet', // 套盒
            self::DesignType->value => 'Design', // 设计
            self::BannerType->value => 'Banner', // banner
            self::SearchNavType->value => 'SearchNav', // 搜索导航
        ];
    }

    public function rules()
    {
        return match ($this) {
            self::CategoryType => [
                'title' => ['required', 'string', 'max:32'],
                'contents' => ['required', 'array'],
                'contents.*.attachment_id' => ['required', 'integer', new Exists(Attachment::class, 'id')],
                'contents.*.collection_id' => ['nullable', 'integer', new Exists(Collection::class, 'id')],
                'contents.*.title' => ['nullable', 'string', 'max:32'],
                'contents.*.link' => ['nullable', 'string', 'max:255'],
                'contents.*.sort' => ['required', 'integer']
            ],
            self::ProductType => [
                'title' => ['required', 'string', 'max:32'],
                'product_contents' => ['required', 'array'],
                'product_contents.type' => ['required', 'integer', new Enum(PromotionProductTypeEnum::class)],
                'product_contents.data' => ['required', 'array'],
                // 固定SKU，则必填商品id
                'product_contents.data.product_ids' => ['required_if:product_contents.type,' . PromotionProductTypeEnum::Fixed->value, 'nullable', 'array'],
                'product_contents.data.product_ids.*' => ['integer', new Exists(Product::class, 'id')],
                // 自定义SKU & 购物车，必填
                // 'product_contents.data.threshold_price' => ['nullable', 'numeric'],
                // 自定义SKU & 首页\购物车，必填
                'product_contents.data.collection_ids' => ['nullable', 'array'],
                'product_contents.data.collection_ids.*' => ['integer', new Exists(Collection::class, 'id')],
                'product_contents.data.mode_ids' => ['nullable', 'array'],
                'product_contents.data.mode_ids.*' => ['integer', new Exists(Mode::class, 'id')],
                // 首页和商品页可以设置属性
                'product_contents.data.attributes' => ['nullable', 'array'],
                'product_contents.data.attributes.*' => ['string', 'in:collection,mode'],
                // 自定义SKU，必填
                'product_contents.data.collection_sell' => ['required_if:product_contents.type,' . PromotionProductTypeEnum::Customize->value, 'nullable', 'boolean'],
                'product_contents.data.mode_sell' => ['required_if:product_contents.type,' . PromotionProductTypeEnum::Customize->value, 'nullable', 'boolean'],
                'product_contents.data.limit' => ['required_if:product_contents.type,' . PromotionProductTypeEnum::Customize->value, 'nullable', 'integer'],
                // 必填项
                'product_contents.data.sort' => ['required', 'string'],
            ],
            self::BoxSetType, self::DesignType => [
                'title' => ['required', 'string', 'max:32'],
                'description' => ['required', 'string', 'max:128'],
                'button_text' => ['required', 'string', 'max:32'],
                'button_link' => ['required', 'string', 'max:255'],
                'contents' => ['required', 'array'],
                'contents.*.attachment_id' => ['required', 'integer', new Exists(Attachment::class, 'id')],
                'contents.*.link' => ['nullable', 'string', 'max:255'],
                'contents.*.sort' => ['required', 'integer']
            ],
            self::BannerType, self::SearchNavType => [
                'contents' => ['required', 'array'],
                'contents.*.attachment_id' => ['required', 'integer', new Exists(Attachment::class, 'id')],
                'contents.*.link' => ['nullable', 'string', 'max:255'],
                'contents.*.sort' => ['required', 'integer']
            ],
            default => []
        };
    }

}
