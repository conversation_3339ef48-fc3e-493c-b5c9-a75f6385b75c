<?php

namespace App\Models;

use App\Models\ActivityProduct;
use App\Models\Product\Product;
use App\Models\UserExperienceActivityCoupon;
use App\Models\Coupon\Coupon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserExperienceActivity extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    public $timestamps = true;
    protected $guarded = [];

    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

    // Accessor for is_listed
    public function getIsListedAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_coupon
    public function getIsCouponAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_first
    public function getIsFirstAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_automatic_listed
    public function getIsAutomaticListedAttribute($value)
    {
        return (bool) $value;
    }

    // Accessor for is_register
    public function getIsRegisterAttribute($value)
    {
        return (bool) $value;
    }

    //关联体验活动产品信息
    public function activityProducts()
    {
        return $this->hasMany(ActivityProduct::class);
    }
    //关联体验活动优惠券信息
    public function UserExperienceActivityCoupons()
    {
        return $this->hasMany(UserExperienceActivityCoupon::class);
    }

    // 定义与产品关系
    public function products()
    {
        return $this->belongsToMany(Product::class, ActivityProduct::class, 'user_experience_activity_id', 'product_id')
            ->withPivot('type', 'price', 'percentage', 'currency_id');
    }

    // 定义与优惠券关系
    public function coupons()
    {
        return $this->belongsToMany(Coupon::class, UserExperienceActivityCoupon::class, 'user_experience_activity_id', 'coupon_id');
    }

    //更新人信息
    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class);
    }

}
