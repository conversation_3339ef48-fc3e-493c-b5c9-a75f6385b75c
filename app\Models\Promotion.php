<?php

namespace App\Models;

use App\Constants\CacheKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;


class Promotion extends MiddleModel
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'status' => 'boolean'
    ];

    protected static function booted()
    {
        parent::booted();
        static::creating(function ($promotion) {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
        static::saved(function ($promotion) {
            cachePartialDelete(CacheKey::PromotionBuilderList->getKey('*'));
        });
    }

    // 子广告位内容
    public function contents()
    {
        return $this->hasMany(PromotionContent::class)->orderBy('sort');
    }

    // 产品广告位内容
    public function productContents(): HasOne
    {
        return $this->hasOne(PromotionProduct::class);
    }

}
