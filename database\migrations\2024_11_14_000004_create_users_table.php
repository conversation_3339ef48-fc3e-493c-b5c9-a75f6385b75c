<?php

use App\Models\Enums\User\UserRegisterTypesEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('parent_id')->nullable()->comment('父级id');
            $table->string('email', 128)->comment('邮箱')->index()->nullable();
            $table->string('password', 60)->nullable();
            $table->string('first_name', 32)->nullable();
            $table->string('last_name', 32)->nullable();
            $table->tinyInteger('register_type')->comment('注册类型')->default(UserRegisterTypesEnum::Default);
            $table->tinyInteger('email_preferences')->comment('邮箱首选项')->default(0);
            $table->string('avatar', 512)->nullable();
            $table->tinyInteger('status')->comment('状态')->default(1);
            $table->dateTime('birth_date')->comment('生日')->nullable();
            $table->bigInteger('sharing_rule_id')->comment('用户分享规则id')->nullable();
            $table->bigInteger('inviters_rule_id')->comment('被邀请人分享规则ID')->nullable();
            $table->timestamp('first_share_date')->comment('首次分享日期')->nullable();
            $table->timestamp('first_withdraw_date')->comment('首次提现日期')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
};
