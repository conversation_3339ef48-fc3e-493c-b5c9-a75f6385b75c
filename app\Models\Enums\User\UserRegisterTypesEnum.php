<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum UserRegisterTypesEnum: int
{
    case  Default = 0;
    case  Invite = 1;
    case  FastPay = 2;
    case  Google = 3;
    case  Facebook = 4;
    case  Twitter = 5;
    case  Subscription = 6;
    case  CouponRandomGrant = 7;
    case  Comment = 8;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Default->value => 'Default',
            self::Invite->value => 'Invite',
            self::FastPay->value => 'FastPay',
            self::Google->value => 'Google',
            self::Facebook->value => 'Facebook',
            self::Twitter->value => 'Twitter',
            self::Subscription->value => 'Subscription',
            self::CouponRandomGrant->value => 'CouponRandomGrant',
            self::Comment->value => 'Comment',
        ];
    }

}
