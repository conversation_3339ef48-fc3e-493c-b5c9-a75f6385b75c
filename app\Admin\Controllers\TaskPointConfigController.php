<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Task\TaskPointConfig;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class TaskPointConfigController extends Controller
{
    /**
     * 获取任务积分配置列表
     */
    public function index(Request $request): JsonResponse
    {
        $configs = QueryBuilder::for(TaskPointConfig::class)
            ->where('is_active', 1)
            ->allowedSorts(['id', 'task_type', 'channel', 'points_per_action', 'sort_order', 'created_at'])
            ->defaultSort('sort_order', 'id')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $configs,
        ]);
    }

    /**
     * 创建任务积分配置
     */
    public function store(Request $request): JsonResource
    {
        $validated = $request->validate([
            'task_type' => 'required|string|max:50',
            'task_name' => 'required|string|max:100',
            'channel' => 'nullable|string|max:50',
            'points_per_action' => 'required|numeric|min:0',
            'daily_limit' => 'nullable|integer|min:0',
            'total_limit' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'extra_config' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
        ]);

        $config = TaskPointConfig::create($validated);

        return new JsonResource($config);
    }

    /**
     * 获取单个任务积分配置
     */
    public function show(TaskPointConfig $taskPointConfig): JsonResource
    {
        return new JsonResource($taskPointConfig);
    }

    /**
     * 更新任务积分配置
     */
    public function update(Request $request, TaskPointConfig $taskPointConfig): JsonResource
    {
        $validated = $request->validate([
            'task_type' => 'required|string|max:50',
            'task_name' => 'required|string|max:100',
            'channel' => 'nullable|string|max:50',
            'points_per_action' => 'required|numeric|min:0',
            'daily_limit' => 'nullable|integer|min:0',
            'total_limit' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'extra_config' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
        ]);

        $taskPointConfig->update($validated);

        return new JsonResource($taskPointConfig);
    }

    /**
     * 删除任务积分配置
     */
    public function destroy(TaskPointConfig $taskPointConfig): JsonResponse
    {
        $taskPointConfig->delete();

        return response()->json([
            'success' => true,
            'message' => '任务积分配置删除成功',
        ]);
    }

    /**
     * 批量更新可操作次数和每次操作获得积分
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'configs' => 'required|array',
            'configs.*.id' => 'required|integer|exists:task_point_configs,id',
            'configs.*.points_per_action' => 'required|numeric|min:0',
            'configs.*.total_limit' => 'nullable|integer|min:0',
        ]);

        foreach ($validated['configs'] as $configData) {
            TaskPointConfig::where('id', $configData['id'])
                ->update([
                    'points_per_action' => $configData['points_per_action'],
                    'total_limit' => $configData['total_limit'],
                ]);
        }

        return response()->json([
            'success' => true,
            'message' => '批量更新成功',
        ]);
    }
}
