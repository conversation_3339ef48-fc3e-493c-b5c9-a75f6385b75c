<?php

namespace App\Models\Enums\Discount;

use App\Models\Shipping;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;

enum DiscountTypeEnum: int
{
    case  QuantityBasedDiscount = 1;
    case  SpendBasedDiscount = 2;
    case  FreeShippingPromotion = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::QuantityBasedDiscount->value => '基于数量享折扣',
            self::SpendBasedDiscount->value => '基于价格享折扣',
            self::FreeShippingPromotion->value => '运费折扣',
        ];
    }

    /**
     * 获取验证规则
     * @return array|string[]
     */
    public function validateRules(): array
    {
        return match ($this) {
            self::QuantityBasedDiscount => [
                'rules' => 'array|required|min:1',
                'rules.*.quantity' => 'required|int|min:1', // 第几件
                'rules.*.discount_amount_type' => ['required', new Enum(DiscountAmountTypeEnum::class)], // 折扣类型
                'rules.*.discount_price' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::FixedAmount->value, 'numeric'], // 固定金额
                'rules.*.discount_rate' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::PercentAmount->value, 'int'], // 折扣率
                'rules.*.discount_max_price' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::PercentAmount->value, 'numeric'], // 折扣率最大金额
                'rules.*.discount_range' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'array'], // 随机金额
                'rules.*.discount_range.min' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'numeric', 'min:0'], // 随机金额最小值
                'rules.*.discount_range.max' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'numeric'], // 随机金额最大值
            ],
            self::SpendBasedDiscount => [
                'rules' => 'array|required|min:1',
                'rules.*.price' => 'required|numeric|min:0.01', // 满多少钱
                'rules.*.discount_amount_type' => ['required', new Enum(DiscountAmountTypeEnum::class)], // 折扣类型
                'rules.*.discount_price' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::FixedAmount->value, 'numeric'], // 固定金额
                'rules.*.discount_rate' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::PercentAmount->value, 'int'], // 折扣率
                'rules.*.discount_max_price' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::PercentAmount->value, 'numeric'], // 折扣率最大金额
                'rules.*.discount_range' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'array'], // 随机金额
                'rules.*.discount_range.min' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'numeric', 'min:0'], // 随机金额最小值
                'rules.*.discount_range.max' => ['required_if:rules.*.discount_amount_type,' . DiscountAmountTypeEnum::RandomAmount->value, 'numeric'], // 随机金额最大值
            ],
            self::FreeShippingPromotion => [
                'rules' => 'array|required|min:1',
                'rules.*.price' => 'required|numeric|min:0.01', // 满多少钱
                'rules.*.shipping_id' => ['required', 'int', new Exists(Shipping::class, 'id')], // 运输方式id
                // 'rules.*.shipping_fee' => ['required', 'numeric'], // 减多少运费
            ],
            default => [],
        };
    }

}
