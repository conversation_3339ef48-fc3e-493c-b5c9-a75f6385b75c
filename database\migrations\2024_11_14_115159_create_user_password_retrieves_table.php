<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_password_retrieves', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->string('key', 255)->nullable();
            $table->boolean('is_used')->comment('是否使用的')->default(false);
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('用户找回密码');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_password_retrieves');
    }
};
