<?php

namespace App\Http\Resources;

use App\Models\Product\ProductColor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property ProductColor $resource
 */
class ProductColorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->resource->color->value,
            'value' => $this->resource->color->id,
        ];
    }
}
