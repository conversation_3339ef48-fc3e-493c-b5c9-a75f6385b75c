<?php

namespace App\Models\Product;

use App\Models\Attachment;
use App\Models\AttributeValue;
use HTMLPurifier_AttrCollections;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductColor extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function color(): BelongsTo
    {
        return $this->belongsTo(AttributeValue::class, 'color_id');
    }

    /**
     * 颜色属性
     */
    public function images(): BelongsToMany
    {
        return $this->belongsToMany(
            Attachment::class,
            ProductColorImage::class,
            'product_color_id',
            'image_id',
            'id',
            'id'
        )->withPivot('sort')->orderByPivot('sort', 'desc');
    }
}
