<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Shipping;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use App\Models\ImportShippingfee;

class ShippingController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::ShippingsUpdate)->except(['update']);
        $this->hasPermissionOr(Permissions::ShippingsIndex, Permissions::ShippingsUpdate)->only(['index', 'show']);
    }

    /**
     * 选择列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function options(Request $request): AnonymousResourceCollection
    {

        $res = QueryBuilder::for(Shipping::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();

        return JsonResource::collection($res);
    }

    public function index(Request $request)
    {
        $builder = QueryBuilder::for(Shipping::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-id');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    public function show(Request $request, Shipping $shipping): JsonResource
    {
        $validated = $request->validate([
            'weight' => ['numeric', 'nullable'],
            'country' => ['string', 'nullable'],
        ]);
        $feesQuery = $shipping->shippingFees();
        // 用 QueryBuilder 统一风格
        $feesQuery = QueryBuilder::for($feesQuery, $request)
            ->allowedFilters([
                AllowedFilter::partial('country'),
                AllowedFilter::callback('weight', function ($query, $value) {
                    $query->where('min_weight', '<=', $value)
                          ->where('max_weight', '>=', $value);
                }),
            ]);
        $shipping->setRelation('shippingFees', $feesQuery->orderBy('min_weight', 'asc')->get());
        return JsonResource::make($shipping);
    }

    public function update(Request $request, Shipping $shipping)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'fee' => 'required|numeric',
            'desc' => 'required|string',
            'type' => 'required|string',
        ]);

        $shipping->update($validated);

        return JsonResource::make($shipping);
    }

    /**
     * 设置默认
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Shipping $shipping
     * @return JsonResource
     */
    public function default_status(Request $request, Shipping $shipping): JsonResource
    {
        $validated = $request->validate([
            'is_default' => ['required', 'boolean'],
        ]);
        if ($validated['is_default']) {
            // 先将所有记录设置为非默认
            Shipping::query()->where('id', '!=', $shipping->id)->update(['is_default' => false]);
        }
        $shipping->update($validated);
        return JsonResource::make($shipping);
    }

    /**
     * 运费导入
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function import(Request $request)
    {
        $validated = $request->validate([
            'file' => ['required', 'file', 'mimes:xlsx,xls,csv'],
        ]);
        $file = Arr::get($validated, 'file');
        // 获取文件名
        $extension = $file->getClientOriginalExtension();
        // 本地目的文件夹
        $directory = 'storage' . DIRECTORY_SEPARATOR . 'import' . DIRECTORY_SEPARATOR . 'shipping' . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR;
        // 使用 Laravel Storage 类创建文件夹（如果不存在）
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
        }
        // 生成文件路径
        $fileName = date('YmdHis') . '.' . $extension;
        $path = $directory . $fileName;
        // 将文件移动到指定目录中
        Storage::disk('public')->putFileAs($directory, $file, $fileName);

        // 创建导入对象
        $import = new ImportShippingfee();
        $import->fill([
            'file' => $path,
            'admin_user_id' => Auth::id(),
        ])->save();

        return JsonResource::make($import);
    }
}
