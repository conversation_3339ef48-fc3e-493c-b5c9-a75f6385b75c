<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Membership\MembershipLevel;

class MembershipLevelSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // 检查是否已经有会员等级数据
        if (MembershipLevel::count() > 0) {
            $this->command->info('会员等级数据已存在，跳过种子数据创建');
            return;
        }

        $this->command->info('创建默认会员等级数据...');

        $levels = [
            [
                'name' => '普通会员',
                'slug' => 'bronze',
                'description' => '新注册用户默认等级，享受基础服务',
                'min_spend_amount' => 0.00,
                'point_rate' => 0.01, // 消费1元获得0.01积分
                'birthday_points_enabled' => true,
                'birthday_points_amount' => 50.00, // 生日赠送50积分
                'benefits' => [
                    'point_rate' => '1%',
                    'birthday_bonus' => '生日赠送50积分',
                    'free_shipping_threshold' => 99
                ],
                'color' => '#CD7F32',
                'icon' => 'bronze-medal',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => '银卡会员',
                'slug' => 'silver',
                'description' => '累计消费满500元可升级，享受更多优惠',
                'min_spend_amount' => 500.00,
                'point_rate' => 0.015, // 消费1元获得0.015积分
                'birthday_points_enabled' => true,
                'birthday_points_amount' => 100.00, // 生日赠送100积分
                'benefits' => [
                    'point_rate' => '1.5%',
                    'birthday_bonus' => '生日赠送100积分',
                    'free_shipping_threshold' => 79,
                    'exclusive_coupons' => '专属优惠券'
                ],
                'color' => '#C0C0C0',
                'icon' => 'silver-medal',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => '金卡会员',
                'slug' => 'gold',
                'description' => '累计消费满2000元可升级，享受高级服务',
                'min_spend_amount' => 2000.00,
                'point_rate' => 0.02, // 消费1元获得0.02积分
                'birthday_points_enabled' => true,
                'birthday_points_amount' => 200.00, // 生日赠送200积分
                'benefits' => [
                    'point_rate' => '2%',
                    'birthday_bonus' => '生日赠送200积分',
                    'free_shipping_threshold' => 59,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '优先客服'
                ],
                'color' => '#FFD700',
                'icon' => 'gold-medal',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => '白金会员',
                'slug' => 'platinum',
                'description' => '累计消费满5000元可升级，享受顶级服务',
                'min_spend_amount' => 5000.00,
                'point_rate' => 0.025, // 消费1元获得0.025积分
                'birthday_points_enabled' => true,
                'birthday_points_amount' => 500.00, // 生日赠送500积分
                'benefits' => [
                    'point_rate' => '2.5%',
                    'birthday_bonus' => '生日赠送500积分',
                    'free_shipping_threshold' => 0,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '优先客服',
                    'early_access' => '新品抢先购'
                ],
                'color' => '#E5E4E2',
                'icon' => 'platinum-medal',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => '钻石会员',
                'slug' => 'diamond',
                'description' => '累计消费满10000元可升级，享受至尊服务',
                'min_spend_amount' => 10000.00,
                'point_rate' => 0.03, // 消费1元获得0.03积分
                'birthday_points_enabled' => true,
                'birthday_points_amount' => 1000.00, // 生日赠送1000积分
                'benefits' => [
                    'point_rate' => '3%',
                    'birthday_bonus' => '生日赠送1000积分',
                    'free_shipping_threshold' => 0,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '专属客服',
                    'early_access' => '新品抢先购',
                    'vip_events' => 'VIP活动邀请',
                    'personal_shopper' => '专属购物顾问'
                ],
                'color' => '#B9F2FF',
                'icon' => 'diamond',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($levels as $levelData) {
            MembershipLevel::create($levelData);
            $this->command->info("创建会员等级: {$levelData['name']}");
        }

        $this->command->info('默认会员等级数据创建完成！');
    }
}
