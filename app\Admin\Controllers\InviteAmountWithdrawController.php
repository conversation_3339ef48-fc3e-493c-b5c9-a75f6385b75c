<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\InviteAmountWithdraw;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class InviteAmountWithdrawController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::WithdrawsIndex)->only(['index', 'show', 'updateStatus', 'export']);
    }

    /**
     * 提现记录列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(InviteAmountWithdraw::class, request: $request)
            ->with([
                'user:id,first_name,last_name,email,inviters_rule_id',
                'user.invitersRule:id,title'
            ])
            ->allowedFilters([
                AllowedFilter::callback('status', function ($query, $value) {
                    if ($value !== null && $value !== '') {  // 只有明确传值时才过滤
                        $query->where('status', $value);
                    }
                }),
                // AllowedFilter::partial('user.email'),
                AllowedFilter::partial('email', 'user.email'),
                // 修改 name 过滤器，空值时跳过
                AllowedFilter::callback('name', function ($query, $value) {
                    if (!empty($value)) {
                        $query->whereHas('user', function ($q) use ($value) {
                            $q->where('first_name', 'like', "%{$value}%")
                                ->orWhere('last_name', 'like', "%{$value}%");
                        });
                    }
                }),
                // 修改时间过滤器，空值时跳过
                AllowedFilter::callback('start_at', function ($query, $value) {
                    if (!empty($value)) {
                        $date = Carbon::parse($value);
                        if (strlen($value) <= 10) { // 纯日期格式（如 2025-07-28）
                            $query->where('created_at', '>=', $date->startOfDay()); // 当天 00:00:00
                        } else { // 完整时间格式（如 2025-07-28 15:30:00）
                            $query->where('created_at', '>=', $date);
                        }
                    }
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                    if (!empty($value)) {
                        $date = Carbon::parse($value);
                        if (strlen($value) <= 10) { // 纯日期格式
                            $query->where('created_at', '<=', $date->endOfDay()); // 当天 23:59:59
                        } else { // 完整时间格式
                            $query->where('created_at', '<=', $date);
                        }
                    }
                }),
            ])
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        $res = $builder->paginate($this->getPerPage());

        return JsonResource::collection($res);
    }
    /**
     * 修改提现状态
     * @param Request $request
     * @param InviteAmountWithdraw $withdraw 提现记录
     * @return JsonResource
     */
    public function updateStatus(Request $request, InviteAmountWithdraw $withdraw)
    {
        $withdraw->update([
            'status' => $request->input('status'), // 直接获取输入值
        ]);
        // 返回更新后的提现记录
        return JsonResource::make($withdraw->fresh());
    }
    /**
     * 导出已审核的提现记录为CSV
     * @param \Illuminate\Http\Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        // 查数据 - 只查询状态为1(已审核)的记录
        $builder = QueryBuilder::for(InviteAmountWithdraw::class, $request)
            ->with([
                'user:id,first_name,last_name,email,inviters_rule_id',
                'user.invitersRule:id,title'
            ])
            ->where('status', 1) // 只导出已审核的记录
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        // 使用Excel门面导出文件
        return Excel::download(
            new \App\Exports\InviteAmountWithdrawExport($builder->getEloquentBuilder()),
            'withdraws.csv'
        );
    }
}