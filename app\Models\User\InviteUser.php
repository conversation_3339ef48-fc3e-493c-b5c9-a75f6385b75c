<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

/**
 * @property boolean $is_shipping_default
 * @property boolean $is_billing_default
 */
class InviteUser extends MiddleModel
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = [];

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 用户关系
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 被邀请人关系
    public function invitedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_user_id', 'id');
    }

}
