<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Coupon\Coupon;
use App\Models\Coupon\CouponCode;
use App\Models\Enums\Discount\DiscountEffectiveDateTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Product\Product;
use App\Models\User\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Exceptions\DataException;

class CouponController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CouponsUpdate)->only(['store', 'patchUpdate', 'destroy', 'update',]);
        $this->hasPermissionOr(Permissions::CouponsUpdate, Permissions::CouponsIndex)->only(['index', 'show']);
    }

    /**
     * 新增优惠券
     * @param Request $request
     * @param Coupon $coupon
     * @return JsonResource
     * @throws ValidationException
     */
    public function store(Request $request, Coupon $coupon): JsonResource
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
            'name' => 'required|string|max:255',
            'desc' => 'required|string',
            'code' => 'required|string|max:255|unique:coupons,code,' . $coupon->id,
            'effective_date_type' => ['required', new Enum(DiscountEffectiveDateTypeEnum::class)],
            'effective_days' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Day->value, 'int', 'nullable'],
            'effective_start_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'effective_end_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'is_global' => 'bool',
            'is_auto' => 'bool',
            'total_count' => 'integer|nullable',
            'user_count' => 'integer|nullable',
            'price_type' => ['required', new Enum(DiscountPriceTypeEnum::class)],
            'type' => ['required', new Enum(DiscountTypeEnum::class)],
            'rules' => ['required', 'array'],
            // 'product_ids' => ['nullable', 'array'],
            // 'product_ids.*' => ['integer', new Exists(Product::class, 'id')],
            //标签配置
            'show_discount_tag' => 'boolean',
            'discount_tag_config' => 'nullable|array',
            'discount_tag_config.display_locations' => 'required_if:show_discount_tag,1|array',
            'discount_tag_config.display_locations.pc' => 'sometimes|array',
            'discount_tag_config.display_locations.pc.*' => 'string', // 明确允许的PC端位置
            'discount_tag_config.display_locations.mobile' => 'sometimes|array',
            'discount_tag_config.display_locations.mobile.*' => 'string', // 明确允许的移动端位置
            'discount_tag_config.tag_content' => 'nullable|string|max:100',
            'discount_tag_config.bg_color' => 'nullable|string|max:20',
            'discount_tag_config.text_color' => 'nullable|string|max:20',
            'discount_tag_config.sort_order' => 'nullable|integer|min:0',
        ]);
        // 自定义验证：当 show_discount_tag=1 时 tag_content 不能为空
        if ($validated['show_discount_tag']) {
            $tagContent = Arr::get($validated, 'discount_tag_config.tag_content');
            if (empty($tagContent)) {
                throw ValidationException::withMessages([
                    'discount_tag_config.tag_content' => ['当启用折扣标签时，标签内容不能为空']
                ]);
            }
        }
        $type = DiscountTypeEnum::tryFrom(Arr::get($validated, 'type'));
        $validator = Validator::make($request->all(), $type->validateRules());
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        // 保存
        // $productIds = Arr::pull($validated, 'product_ids', []);
        $coupon->fill($validated)->save();
        // if (is_array($productIds)) {
        //     $coupon->products()->sync($productIds);
        // }
        return JsonResource::make($coupon);
    }

    /**
     * 优惠券列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(Coupon::class)
            ->allowedFilters([
                AllowedFilter::exact('enabled'),
                AllowedFilter::exact('type'),
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['enabled', 'updated_at'])
            ->defaultSort('-enabled', '-updated_at');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    public function show(Coupon $coupon): JsonResource
    {
        $coupon->loadMissing([
            'products:id'
        ]);

        // 确保 discount_tag_config.display_locations 有默认结构
        $discountTagConfig = $coupon->discount_tag_config ?? [];
        if (!isset($discountTagConfig['display_locations'])) {
            $discountTagConfig['display_locations'] = [
                'pc' => [],
                'mobile' => []
            ];
            $coupon->discount_tag_config = $discountTagConfig;
        }

        $coupon->setHidden(['products']);
        return JsonResource::make($coupon);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Coupon::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Coupon::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

    /**
     * 优惠券
     * @param Request $request
     * @param Coupon $coupon
     * @return JsonResource
     */
    public function patchUpdate(Request $request, Coupon $coupon): JsonResource
    {
        $update = $request->validate([
            'enabled' => 'required|boolean',
            'effective_date_type' => [new Enum(DiscountEffectiveDateTypeEnum::class), 'nullable'],
            'effective_days' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Day->value, 'int', 'nullable'],
            'effective_start_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'effective_end_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'total_count' => 'integer',
            'user_count' => 'integer',
            'is_global' => 'bool',
            'is_auto' => 'bool',
        ]);

        $update && $coupon->update($update);
        return JsonResource::make($coupon);
    }

    /**
     * 修改优惠券
     * @param Request $request
     * @param Coupon $coupon
     * @return JsonResource
     */
    public function update(Request $request, Coupon $coupon): JsonResource
    {
        $update = $request->validate([
            'enabled' => 'required|boolean',
            'name' => 'required|string|max:255',
            'desc' => 'required|string',
            // 'code' => ['required', 'string', 'max:255', Rule::unique('coupons', 'code')->ignore($coupon->id)],
            'effective_date_type' => ['required', new Enum(DiscountEffectiveDateTypeEnum::class)],
            'effective_days' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Day->value, 'int', 'nullable'],
            'effective_start_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'effective_end_at' => ['required_if:effective_date_type,' . DiscountEffectiveDateTypeEnum::Cycle->value, 'date', 'nullable'],
            'is_global' => 'bool',
            'is_auto' => 'bool',
            'total_count' => 'integer|nullable',
            'user_count' => 'integer|nullable',
            'price_type' => ['required', new Enum(DiscountPriceTypeEnum::class)],
            'type' => ['required', new Enum(DiscountTypeEnum::class)],
            'rules' => ['required', 'array'],
            // 'product_ids' => ['nullable', 'array'],
            // 'product_ids.*' => ['integer', new Exists(Product::class, 'id')],
            //标签配置
            'show_discount_tag' => 'boolean',
            'discount_tag_config' => 'nullable|array',
            'discount_tag_config.display_locations' => 'required_if:show_discount_tag,1|array',
            'discount_tag_config.display_locations.pc' => 'sometimes|array',
            'discount_tag_config.display_locations.pc.*' => 'string', // 明确允许的PC端位置
            'discount_tag_config.display_locations.mobile' => 'sometimes|array',
            'discount_tag_config.display_locations.mobile.*' => 'string', // 明确允许的移动端位置
            'discount_tag_config.tag_content' => 'nullable|string|max:100',
            'discount_tag_config.bg_color' => 'nullable|string|max:20',
            'discount_tag_config.text_color' => 'nullable|string|max:20',
            'discount_tag_config.sort_order' => 'nullable|integer|min:0',
        ]);
        //启用中的优惠券不给修改
        if ($coupon->enabled) {
            throw new DataException('The rules in use do not allow modification');
        }
        // 自定义验证：当 show_discount_tag=1 时 tag_content 不能为空
        if ($update['show_discount_tag']) {
            $tagContent = Arr::get($update, 'discount_tag_config.tag_content');
            if (empty($tagContent)) {
                throw ValidationException::withMessages([
                    'discount_tag_config.tag_content' => ['当启用折扣标签时，标签内容不能为空']
                ]);
            }
        }
        $type = DiscountTypeEnum::tryFrom(Arr::get($update, 'type'));
        $validator = Validator::make($request->all(), $type->validateRules());

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        // $productIds = Arr::pull($update, 'product_ids', []);
        // if (is_array($productIds)) {
        //     $coupon->products()->sync($productIds);
        // }
        $update && $coupon->update($update);
        return JsonResource::make($coupon);
    }

    /**
     * 生成优惠卷码
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed
     */
    public function code(Request $request)
    {
        $code = new CouponCode();
        $code->save();

        return response()->json([
            'code' => 'VIP' . Str::padLeft($code->id, 3, 0)
        ]);
    }


    /**
     * 优惠劵选择
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function option(Request $request)
    {
        $now = now();
        $res = QueryBuilder::for(Coupon::class)
            ->where('enabled', true)
            ->where(function ($query) use ($now) {
                // 筛选有效期内的优惠券
                $query->where(function ($q) use ($now) {
                    // 固定周期类型的优惠券
                    $q->where('effective_date_type', DiscountEffectiveDateTypeEnum::Cycle->value)
                        ->where('effective_end_at', '>=', $now);
                })->orWhere(function ($q) {
                    // 按天数类型的优惠券 (这类优惠券在领取时才开始计算有效期，所以只要是启用状态就可以)
                    $q->where('effective_date_type', DiscountEffectiveDateTypeEnum::Day->value);
                });
            })
            ->get();

        return JsonResource::collection($res);
    }
}
