<?php

namespace App\Http\Controllers;

use App\Constants\Permissions;
use App\Constants\Roles;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Routing\ControllerMiddlewareOptions;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function getPerPage($num = 15)
    {
        return request()->input('per_page', $num);
    }

    public function hasPermissionOr(...$permissions): ControllerMiddlewareOptions
    {
        $permissions = array_map(function ($permission) {
            return $permission instanceof Permissions ? $permission->value : (string)$permission;
        }, $permissions);
        return $this->middleware('permission:' . implode('|', $permissions));
    }

    public function hasRoleOr(...$roles): ControllerMiddlewareOptions
    {
        $roles = array_map(function ($role) {
            return $role instanceof Roles ? $role->value : (string)$role;
        }, $roles);
        return $this->middleware('role:' . implode('|', $roles));
    }
}
