<?php

namespace App\Http\Resources;

use App\Models\Enums\User\InviteUserTypeEnum;
use App\Models\User\User;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property User $resource
 */
class UserInviteStatListResource extends JsonResource
{
    /**
     * 将资源转换为数组。
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // 统计用户邀请人数
        $this->resource->allInvitedUsers->each(function ($item) {
            if ($item->pivot->type == InviteUserTypeEnum::ExclusiveFans->value) {
                $this->resource->exclusive_fans_count++;
            }
            if ($item->pivot->type == InviteUserTypeEnum::OrdinaryFans->value) {
                $this->resource->ordinary_fans_count++;
            }
        });
        // 统计预估次数
        $this->resource->inviteRewards->each(function ($item) {
            if ($item->is_cashback) {
                $this->resource->actual_count++;
                $this->resource->actual_amount += $item->reward_amount;
            }
        });
        return [
            'id' => $this->resource->id,
            'email' => $this->resource->email,
            'first_share_date' => $this->resource->first_share_date,
            'exclusive_fans_count' => $this->resource->exclusive_fans_count ?: 0,
            'ordinary_fans_count' => $this->resource->ordinary_fans_count ?: 0,
            'estimated_count' => $this->resource->inviteRewards->count(),
            'estimated_amount' => $this->resource->inviteRewards->sum('reward_amount'),
            'actual_count' => $this->resource->actual_count ?: 0,
            'actual_amount' => $this->resource->actual_amount ?: 0,
        ];
    }
}
