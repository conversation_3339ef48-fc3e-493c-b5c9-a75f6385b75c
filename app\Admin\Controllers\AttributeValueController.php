<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Attachment;
use App\Models\Attribute;
use App\Models\AttributeValue;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class AttributeValueController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::AttributesUpdate)->except(['index', 'store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::AttributesIndex, Permissions::AttributesUpdate)->only(['index', 'show']);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AttributeValue $value
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function store(Request $request, AttributeValue $value): JsonResource
    {
        $validated = $request->validate([
            'attribute_id' => ['required', new Exists(Attribute::class, 'id')],
            'attachment_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'value' => ['required', 'string', 'max:32'],
            'sort' => ['integer', 'min:0']
        ]);
        DB::beginTransaction();
        try {
            // 验证唯一
            if (AttributeValue::query()->where('value', Arr::get($validated, 'value'))
                ->where('attribute_id', Arr::get($validated, 'attribute_id'))
                ->exists()) {
                throw new DataException('attribute value already exists');
            }

            // 新增
            $value->fill([
                ...$validated,
                'extra' => Arr::get($request, 'extra')
            ])->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($value->loadMissing([
            'attachment:id,path,module'
        ]));
    }

    /**
     * 修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AttributeValue $value
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function update(Request $request, AttributeValue $value): JsonResource
    {
        $validated = $request->validate([
            'attribute_id' => ['required', new Exists(Attribute::class, 'id')],
            'attachment_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'value' => ['required', 'string', 'max:32'],
            'sort' => ['integer', 'min:0']
        ]);
        DB::beginTransaction();
        try {
            // 验证唯一
            if (AttributeValue::query()->where('value', Arr::get($validated, 'value'))
            ->where('attribute_id', Arr::get($validated, 'attribute_id'))
            ->where('id', '<>', $value->id)
            ->exists()) {
                throw new DataException('attribute value already exists');
            }
            $value->update([
                ...$validated,
                'extra' => Arr::get($request, 'extra')
            ]);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($value->loadMissing([
            'attachment:id,path,module'
        ]));
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return JsonResponse|mixed
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(AttributeValue::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            // 删除属性值
            AttributeValue::query()->whereIn('id', $ids)->delete();

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    /**
     * 列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request, Attribute $attribute): AnonymousResourceCollection
    {

        $values= QueryBuilder::for(AttributeValue::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('value'),
            ])
            ->with(['attachment:id,path,module'])
            ->where('attribute_id', $attribute->id)
            ->defaultSort('sort')
            ->allowedSorts('-sort');
        $res = $values->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 详情
     * @param \App\Models\AttributeValue $value
     * @return JsonResource
     */
    public function show(AttributeValue $value): JsonResource
    {
        $value->loadMissing([
            'attachment:id,path,module'
        ]);
        return JsonResource::make($value);
    }
}
