<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('states', function (Blueprint $table) {
            $table->id(); // 自动创建 `id` 字段，类型为 bigint(20) unsigned
            $table->unsignedBigInteger('country_id'); // 创建 `country_id` 字段，类型为 bigint(11) unsigned
            $table->unsignedBigInteger('id_zone'); // 创建 `id_zone` 字段，类型为 bigint(11) unsigned
            $table->string('name', 80); // 创建 `name` 字段，类型为 varchar(80)
            $table->string('iso_code', 7); // 创建 `iso_code` 字段，类型为 varchar(7)
            // 创建索引
            $table->timestamps();
            $table->index('country_id'); // 为 `country_id` 创建索引
            $table->index('name'); // 为 `name` 创建索引
            $table->index('id_zone'); // 为 `id_zone` 创建索引
            $table->comment('州');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('states');
    }
};
