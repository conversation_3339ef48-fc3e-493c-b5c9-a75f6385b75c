<?php

namespace App\Models\Order\Payment;

use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Order\Order;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;
use App\Models\Cart;

/**
 * @property string $paypal_order_id
 * @property array $detail
 */
class PaypalOrder extends MiddleModel implements Payment
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'detail' => 'json'
    ];
    protected $hidden = ['detail'];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    public function loadPaymentInfo(): void
    {

    }

    /**
     * 获取支付金额
     * @return float
     */
    public function getPaidAmount(): float
    {
        if (Arr::get($this->detail, 'status') != 'COMPLETED') {
            return 0;
        }
        // 多语言时候需要注意
        foreach ($this->detail['purchase_units'] as $purchase_unit) {
            return collect($purchase_unit['payments']['captures'])->sum(fn($re) => Arr::get($re, 'amount.value'));
        }
    }

    /**
     * 获取支付时间
     * @return string|Carbon|null
     */
    public function getPaidAt(): string|Carbon|null
    {
        if (Arr::get($this->detail, 'status') != 'COMPLETED') {
            return null;
        }
        foreach ($this->detail['purchase_units'] as $purchase_unit) {
            $payAt = collect($purchase_unit['payments']['captures'])->where('status', 'COMPLETED')->value('create_time');
            $payAt ??= $this->detail['create_time'];
            return Carbon::parse($payAt);
        }
        return null;
    }

    /**
     * 获取支付类型
     * @return OrderPaidTypeEnum
     */
    public function getPaidType(): OrderPaidTypeEnum
    {
        return OrderPaidTypeEnum::PayPal;
    }

    /**
     * 获取支付状态
     * @return OrderPaidStatusEnum
     */
    public function getPaidStatus(): OrderPaidStatusEnum
    {
        if (Arr::get($this->detail, 'status') != 'COMPLETED') {
            return OrderPaidStatusEnum::Unpaid;
        }
        return OrderPaidStatusEnum::Paid;
    }

}
