<?php

namespace App\Services\CouponGrant\Strategies;

use App\Exceptions\DataException;
use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRecord;
use App\Models\Enums\User\PointChangeTypeEnum;
use App\Models\User\User;
use App\Services\UserPointService;
use Illuminate\Support\Facades\DB;

class PointExchangeStrategy extends RuleStrategy
{
    private int $requiredPoints;
    private int $couponId;

    public function __construct($rule, $model, int $couponId, int $requiredPoints)
    {
        parent::__construct($rule, $model);
        $this->couponId = $couponId;
        $this->requiredPoints = $requiredPoints;
    }

    public function checkCanGrant(): bool
    {
        $user = $this->getUser();
        if (!$user) {
            return false;
        }

        // 检查用户积分是否足够
        $userPoints = $user->wallet ? $user->wallet->point : 0;
        if ($userPoints < $this->requiredPoints) {
            return false;
        }

        // 检查优惠券是否存在且启用
        $coupon = $this->getCoupon();
        if (!$coupon || !$coupon->enabled) {
            return false;
        }

        return true;
    }

    public function getCoupon(): ?Coupon
    {
        return Coupon::find($this->couponId);
    }

    public function getUser(): ?User
    {
        return $this->model;
    }

    /**
     * 积分兑换优惠券
     * @param bool $is_framed
     * @return Coupon|false
     * @throws \Throwable
     */
    public function grant($is_framed = false)
    {
        // 检查能否发放优惠券
        if (!$this->checkCanGrant()) {
            return false;
        }

        $coupon = $this->getCoupon();
        $user = $this->getUser();

        if (!$coupon || !$user) {
            return false;
        }

        try {
            DB::beginTransaction();

            // 发放优惠券
            $giveOk = userService()->giveUserCoupon($user, $coupon, $this->rule, $this->rule->effective_end_at, $is_framed);

            if ($giveOk) {
                // 使用新的积分服务扣除积分
                $pointService = new UserPointService();
                $pointService->deductPoints(
                    user: $user,
                    points: $this->requiredPoints,
                    changeType: PointChangeTypeEnum::CouponExchange,
                    description: "积分兑换优惠券：{$coupon->name}",
                    source: $giveOk,
                    couponCode: $giveOk->code,
                    couponName: $coupon->name
                );

                // 记录发放记录
                $record = new CouponGrantRecord([
                    'user_id' => $user->id,
                    'user_coupon_id' => $giveOk->id,
                ]);
                $record->source()->associate($this->model);
                $recordOk = $this->rule->records()->save($record);

                if (!$recordOk) {
                    throw new \Exception("积分兑换优惠券失败, 记录创建结果{$recordOk},优惠券发放结果{$giveOk}");
                }

                $this->rule->increment('grant_count');
            }

            DB::commit();
            return $coupon;
        } catch (\Throwable $exception) {
            logger()->error("PointExchangeStrategy : " . $exception->getMessage());
            DB::rollBack();
            throw $exception;
        }
    }
}
