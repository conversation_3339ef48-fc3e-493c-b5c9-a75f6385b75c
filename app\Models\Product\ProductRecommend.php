<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductRecommend extends Model
{
    use HasFactory;
    protected $table = 'product_recommends';

    protected $fillable = [
        'product_id',
        'recommend_product_id',
        'sort',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function recommendProduct()
    {
        return $this->belongsTo(Product::class, 'recommend_product_id');
    }

    // 推荐商品简要信息
    public function getRecommendAttribute()
    {
        $product = $this->recommendProduct;
        if (!$product) return null;
        return [
            'id' => $product->id,
            'spu' => $product->spu,
            'min_price' => $product->min_price,
            'origin_price' => $product->origin_price,
            'image' => $product->image_id,
        ];
    }
} 