<?php

namespace App\Models\Enums\Reward;

use Illuminate\Support\Arr;

enum RewardRemarkTypeEnum: int
{
    case OrderSuccess = 1;
    case OrderCancel = 2;
    case OrderPartialCancel = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::OrderSuccess->value => '订单成功:发放所有的奖励金额',
            self::OrderCancel->value => '订单取消、订单全部退款:扣除所有的奖励金额',
            self::OrderPartialCancel->value => '部分订单退款:部分订单退款，则需要根据退款金额的占比进行扣除奖励金额',
        ];
    }
}
