<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->dateTime('first_publish_at')->nullable()->comment('首次上架时间');
            $table->dateTime('new_publish_at')->nullable()->comment('最新上架时间');
            $table->boolean('is_publish')->default(0)->comment('是否上架');
            $table->boolean('is_new')->default(0)->comment('是否最新发布');
            $table->boolean('is_featured')->default(0)->comment('推荐的');
            $table->string('spu', 512)->nullable()->comment('SPU');
            $table->string('title', 512)->comment('标题');
            $table->string('slug_title', 1024)->comment('slug标题');
            $table->string('meta_title', 512)->default("")->comment('seo标题');
            $table->string('meta_description', 512)->default("")->comment('seo描述');
            $table->string('meta_keywords', 1024)->default("")->comment('seo关键词');
            $table->string('keywords', 255)->comment('关键词');
            $table->text('desc')->nullable()->comment('详情描述');
            $table->text('short_desc')->nullable()->comment('简短描述');
            $table->text('model_desc')->nullable()->comment('模特描述');
            $table->decimal('max_price', 10, 4)->default(0)->comment('最高售价');
            $table->decimal('min_price', 10, 4)->default(0)->comment('最低售价');
            $table->decimal('origin_price', 10, 4)->default(0)->comment('最低售价的原价');
            $table->bigInteger('video_id')->nullable()->comment('商品主图视频');
            $table->bigInteger('image_id')->comment('商品主图');
            $table->bigInteger('brand_id')->comment('品牌id');
            $table->bigInteger('category_id')->comment('品类id');
            $table->bigInteger('style_id')->comment('款式id');
            $table->bigInteger('material_id')->comment('材质id');
            $table->bigInteger('mode_id')->nullable()->comment('材质id');
            $table->string('brand_name', 64)->nullable()->comment('品牌名称-冗余');
            $table->string('category_name', 64)->nullable()->comment('品类名称-冗余');
            $table->string('style_name')->nullable()->comment('款式名称');
            $table->string('material_name')->nullable()->comment('材质名称');
            $table->date('make_at')->comment('制造时间');
            $table->integer('sale_num')->default(0)->comment('销量');
            $table->dateTime('publish_at')->nullable()->comment('预上架时间');
            $table->decimal('weight', 10, 3)->nullable()->comment('重量');
            $table->timestamps();
            $table->softDeletes();

            $table->engine('InnoDB');
            $table->comment('商品表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
