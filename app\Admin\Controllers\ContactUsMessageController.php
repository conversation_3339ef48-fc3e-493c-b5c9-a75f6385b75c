<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\ContactUsMessage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class ContactUsMessageController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::ContactUsMessagesUpdate)->except(['index', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::ContactUsMessagesIndex, Permissions::ContactUsMessagesUpdate)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $ContactUsMessage = QueryBuilder::for(ContactUsMessage::class, $request)
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $ContactUsMessage->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function update(Request $request, ContactUsMessage $ContactUsMessage): JsonResource
    {
        $validated = $request->validate([
            'name' =>['nullable', 'string', 'max:255'],
            'message' => ['nullable', 'string', 'max:255'],
        ]);

        DB::beginTransaction();
        try {
            $ContactUsMessage->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($ContactUsMessage);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(ContactUsMessage::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            ContactUsMessage::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $ContactUsMessage = QueryBuilder::for(ContactUsMessage::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('email'),
            ])
            ->defaultSort('-created_at')
            ->allowedSorts('created_at', '-created_at');
        $res = $ContactUsMessage->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(ContactUsMessage $ContactUsMessage): JsonResource
    {
        return JsonResource::make($ContactUsMessage);
    }
}
