<?php

namespace App\Models\Enums\Promotion;

use Illuminate\Support\Arr;

enum PromotionProductTypeEnum: int
{
    case Fixed = 1;
    case Customize = 2;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Fixed->value => '固定',
            self::Customize->value => '自定义'
        ];
    }
}
