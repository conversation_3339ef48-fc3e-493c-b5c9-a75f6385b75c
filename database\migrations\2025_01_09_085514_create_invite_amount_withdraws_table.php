<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invite_amount_withdraws', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->decimal('amount', 10, 2)->comment('提现金额');
            $table->string('currency_code')->comment('体现货币');
            $table->decimal('commission_amount')->comment('提现手续费');
            $table->decimal('withdrawable_amount', 10, 2)->comment('剩余可提现金额');
            $table->tinyInteger('status')->default(0)->comment('状态 0未审核， 1已通过，2已体现，4审核不通过');
            $table->json('payment_account')->comment('收款信息');
            $table->string('payment_platform')->comment('收款平台');

            $table->engine('InnoDB');
            $table->comment('邀请奖励体现记录');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invite_amount_withdraws');
    }
};
