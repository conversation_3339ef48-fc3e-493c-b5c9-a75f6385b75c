<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotion_products', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('promotion_id')->index()->comment('广告ID');
            $table->tinyInteger('type')->comment('类型 1:固定SKU 2:自定义SKU');
            $table->text('data')->comment('数据');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('商品广告表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_products');
    }
};
