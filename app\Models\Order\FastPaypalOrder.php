<?php

namespace App\Models\Order;

use App\Models\Cart;
use App\Models\Order\Payment\PaypalOrder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FastPaypalOrder extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    public static function booted()
    {
        static::creating(function (self $model) {
            $model->no ??= 'FARD' . date('YmdHis') . mt_rand(100,999);
        });
    }

    public function paypalOrder(): BelongsTo
    {
        return $this->belongsTo(PaypalOrder::class);
    }

    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }
}
