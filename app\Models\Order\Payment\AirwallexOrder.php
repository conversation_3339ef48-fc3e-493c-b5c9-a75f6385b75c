<?php

namespace App\Models\Order\Payment;

use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Order\Order;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;

class AirwallexOrder extends MiddleModel implements Payment
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'detail' => 'json'
    ];
    protected $hidden = ['detail'];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function loadPaymentInfo(): void
    {
    }

    /**
     * 获取支付金额
     * @return float
     */
    public function getPaidAmount(): float
    {
        if (Arr::get($this->detail, 'status') != 'SUCCEEDED') {
            return 0;
        }
        return (float)Arr::get($this->detail, 'latest_payment_attempt.amount');
    }

    /**
     * 获取支付时间
     * @return string|Carbon|null
     */
    public function getPaidAt(): string|Carbon|null
    {
        if (Arr::get($this->detail, 'status') != 'SUCCEEDED') {
            return null;
        }
        return (string)Arr::get($this->detail, 'latest_payment_attempt.updated_at');
    }

    /**
     * 获取支付类型
     * @return OrderPaidTypeEnum
     */
    public function getPaidType(): OrderPaidTypeEnum
    {

        return OrderPaidTypeEnum::Bank;
    }

    /**
     * 获取支付状态
     * @return OrderPaidStatusEnum
     */
    public function getPaidStatus(): OrderPaidStatusEnum
    {
        if (Arr::get($this->detail, 'status') != 'SUCCEEDED') {
            return OrderPaidStatusEnum::Unpaid;
        }
        return OrderPaidStatusEnum::Paid;
    }
}
