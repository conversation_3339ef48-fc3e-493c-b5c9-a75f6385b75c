<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Admin\Requests\SharingRuleRequest;
use App\Models\SharingRule;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;


class SharingRuleController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::SharingRulesUpdate)->except(['store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::SharingRulesIndex, Permissions::SharingRulesUpdate)->only(['index', 'show']);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\SharingRule $rule
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function store(SharingRuleRequest $request, SharingRule $rule): JsonResource
    {
        $validated = $request->validated();

        DB::beginTransaction();
        try {
            // 验证唯一性
            if (SharingRule::query()->where('title', Arr::get($validated, 'title'))->exists()) {
                throw new DataException('Rule title already exist');
            }
            // 限制条件判断
            $condition = Arr::pull($validated, 'condition');
            if (Arr::get($condition, 'enabled', false)) {
                $condition['days'] = Arr::get($condition, 'days', 45);
            }

            // 如果设置为全局规则，则将其他规则的全局标志设为false
            if (Arr::get($validated, 'is_global', false) === true) {
                // 将其他规则的is_global设置为false
                SharingRule::query()
                    ->where('is_global', true)
                    ->update(['is_global' => false]);
            }
            
            $rule->fill([
                ...$validated,
                'condition' => $condition,
                'update_user_id' => auth()->id()
            ])->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($rule);
    }

    /**
     * 修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\SharingRule $rule
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function update(SharingRuleRequest $request, SharingRule $rule): JsonResource
    {
        $validated = $request->validated();

        DB::beginTransaction();
        try {
            // 验证唯一性
            if (
                SharingRule::query()->where('title', Arr::get($validated, 'title'))
                ->where('id', '<>', $rule->id)
                ->exists()
            ) {
                throw new DataException('Rule title already exist');
            }
            // 上架判断  一次只能有一个默认的全局规则，无需判断时间是否重叠
            // if (Arr::get($validated, 'is_publish', false)) {
            //     // 是否符合上架的条件
            //     if (sharingRuleService()->HasEffectivePeriodOverlaps($rule->effective_start_at, $rule->effective_end_at, $rule)) {
            //         throw new DataException('The effective period overlaps');
            //     }
            // }
            // 限制条件判断
            $condition = Arr::pull($validated, 'condition');
            if (Arr::get($condition, 'enabled', false)) {
                $condition['days'] = Arr::get($condition, 'days', 45);
            }
            // 更新属性
            $rule->update([
                ...$validated,
                'condition' => $condition,
                'update_user_id' => auth()->id()
            ]);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new \Exception($throwable->getMessage());
        }

        return JsonResource::make($rule);
    }

    /**
     * 删除
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return JsonResponse|mixed
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(SharingRule::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            // 删除属性
            SharingRule::query()->whereIn('id', $ids)->delete();

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    /**
     * 列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $rules = QueryBuilder::for(SharingRule::class, $request)
            ->with([
                'updateUser'
            ])
            ->allowedFilters([
                AllowedFilter::exact('title'),
                AllowedFilter::exact('is_global'),
                AllowedFilter::exact('is_publish'),
            ])
            ->defaultSort('created_at')
            ->allowedSorts('-created_at');
        $res = $rules->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 详情
     * @param \App\Models\SharingRule $rule
     * @return JsonResource
     */
    public function show(SharingRule $rule): JsonResource
    {
        return JsonResource::make($rule);
    }

    /**
     * 部分更新
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\SharingRule $rule
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function patchUpdate(Request $request, SharingRule $rule): JsonResource
    {
        $validated = $request->validate([
            'is_publish' => ['bool'],
            'is_global' => ['bool']
        ]);
        // 上架判断
        // if (Arr::get($validated, 'is_publish')) {
        //     // 是否符合上架的条件
        //     if (sharingRuleService()->HasEffectivePeriodOverlaps($rule->effective_start_at, $rule->effective_end_at, $rule)) {
        //         throw new DataException('The effective period overlaps');
        //     }
        // }
        // 如果设置为全局规则，则将其他规则的全局标志设为false
        if (Arr::get($validated, 'is_global', false) === true) {
            DB::beginTransaction();
            try {
                // 将其他规则的is_global设置为false
                SharingRule::query()
                    ->where('id', '<>', $rule->id)
                    ->where('is_global', true)
                    ->update(['is_global' => false]);

                // 更新当前规则
                $validated && $rule->update($validated);

                DB::commit();
            } catch (\Throwable $throwable) {
                DB::rollBack();
                throw new DataException($throwable->getMessage());
            }
        } else {
            // 正常更新当前规则
            $validated && $rule->update($validated);
        }
        $validated && $rule->update($validated);
        return JsonResource::make($rule);
    }

    /**
     * 选择
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function options(Request $request)
    {
        $res = QueryBuilder::for(SharingRule::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'),
            ])
            ->where('is_publish', true)
            ->select([DB::raw('title as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }
}
