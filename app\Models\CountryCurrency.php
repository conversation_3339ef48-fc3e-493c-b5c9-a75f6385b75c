<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CountryCurrency extends MiddleModel
{
    use SoftDeletes;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'country_code',
        'currency_code',
        'image_id',
        'sort',
        'active',
        'is_default',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'active' => 'boolean',
        'sort' => 'integer',
        'is_default' => 'boolean',
    ];

    /**
     * 获取关联的国家
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_code', 'iso_code');
    }

    /**
     * 获取关联的货币
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'currency_code');
    }

    /**
     * 获取关联的图片
     */
    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    /**
     * 获取默认的国家货币
     */
    public static function getDefaultCountryCurrency()
    {
        return static::where('is_default', true)
            ->where('active', true)
            ->first();
    }
} 