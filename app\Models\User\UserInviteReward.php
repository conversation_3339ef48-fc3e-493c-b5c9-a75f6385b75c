<?php

namespace App\Models\User;

use App\Models\Order\Order;
use App\Models\SharingRule;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserInviteReward extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'is_effective' => 'boolean',
        'is_cashback' => 'boolean',
        'reward_amount' => 'float',
    ];
    protected $appends = [
        'order_total_agent',
        'reward_amount_agent'
    ];

    /**************** 金额币种转换 ****************/
    public function orderTotalAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->order_total, currentCurrency())
        );
    }

    public function rewardAmountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->reward_amount, currentCurrency())
        );
    }

    // 订单
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // 下单用户
    public function orderUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'order_user_id');
    }

    // 用户
    public function rewardUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reward_user_id');
    }

    // 规则
    public function rule(): BelongsTo
    {
        return $this->belongsTo(SharingRule::class);
    }
}
