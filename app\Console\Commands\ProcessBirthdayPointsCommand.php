<?php

namespace App\Console\Commands;

use App\Jobs\ProcessBirthdayPointsJob;
use Illuminate\Console\Command;

class ProcessBirthdayPointsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'membership:birthday-points {--queue : 是否使用队列处理}';

    /**
     * The console command description.
     */
    protected $description = '处理用户生日积分发放';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('开始处理生日积分发放...');

        if ($this->option('queue')) {
            // 使用队列处理
            ProcessBirthdayPointsJob::dispatch();
            $this->info('生日积分处理任务已加入队列');
        } else {
            // 同步处理
            $job = new ProcessBirthdayPointsJob();
            $job->handle(app(\App\Services\MembershipService::class));
            $this->info('生日积分处理完成');
        }

        return self::SUCCESS;
    }
}
