<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('名称');
            $table->boolean('enabled')->default(false)->comment('启用');
            $table->tinyInteger('type')->comment('类型');
            $table->tinyInteger('event')->comment('事件类型');
            $table->tinyInteger('send_type')->comment('发送类型');
            $table->bigInteger('email_template_id')->comment('模板id');
            $table->json('data')->comment('支持绑定模板数据');

            $table->boolean('is_delay')->default(false)->comment('是否延迟发送');
            $table->integer('delay_second')->default(0)->comment('延迟多少秒');
            $table->string('delay_unit')->nullable()->comment('客户端展示单位/ 时分秒');
            $table->string('desc')->nullable()->comment('描述');
            $table->bigInteger('admin_user_id')->nullable()->index()->comment('更新人');
            $table->timestamps();
            $table->engine('InnoDB');
            $table->comment('邮件发送规则');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_rules');
    }
};
