<?php

namespace App\Models\Order;

use App\Models\Enums\Order\OrderStatusEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;

class OrderHistory extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'status_cn',
    ];

    protected $casts = [
        'status' => OrderStatusEnum::class,
    ];

    public function statusCn(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->status->desc(),
        );
    }
}
