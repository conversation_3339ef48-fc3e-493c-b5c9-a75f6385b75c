<?php

namespace Database\Seeders;

use App\Models\Attribute;
use App\Models\Enums\AttributesEnum;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 强制1是颜色 2是尺寸
        $arr = [
            [
                'name' => AttributesEnum::Color,
                'values' => [
                    ['value' => 'Red', 'extra' => ['color' => '#D39394']],
                    ['value' => 'Black', 'extra' => ['color' => '#9C7C68']],
                    ['value' => 'Blue', 'extra' => ['color' => '#4E3227']],
                ],
            ],
            [
                'name' => AttributesEnum::Size,
                'values' => [
                    [
                        'value' => 'L',
                    ],
                    [
                        'value' => 'XL',
                    ],
                    [
                        'value' => 'XXL',
                    ]
                ],
            ],
        ];
        //
        foreach ($arr as $item) {
            $attribute = Arr::get($item, 'attribute');
            if (!Attribute::query()->where('name', $attribute)->exists()) {
                $values = Arr::get($item, 'values');
                /**
                 * @var Attribute $attribute
                 */
                $attribute = Attribute::query()->create(Arr::except($item, ['values']));
                $attribute->values()->createMany($values);
            }
        }
    }
}
