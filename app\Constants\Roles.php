<?php

namespace App\Constants;

use Illuminate\Support\Arr;

enum Roles: string
{
    case SuperAdmin = 'superAdmin';
    case Admin = 'admin';


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::SuperAdmin->value => '超级管理员',
            self::Admin->value => '管理员',
        ];
    }
}
