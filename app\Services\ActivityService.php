<?php

namespace App\Services;


use App\Models\Cart;
use App\Models\User\User;
use App\Models\UserExperienceActivity;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Order\OrderPaidStatusEnum;

class ActivityService
{
    /**
     * 获取产品是否是特价活动产品
     * @param $productId
     * @return
     */
    public function isProductOnSale($productId, ?User $user = null): mixed
    {
        // 查找当前的活动并加载相关产品信息
        $userExperienceActivities = UserExperienceActivity::with(['activityProducts.product'])
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->where('is_listed', true)
            ->first();

        // 如果没有找到活动，直接返回 false
        if (!$userExperienceActivities || $userExperienceActivities->activityProducts->isEmpty()) {
            return false;
        }

        // 获取活动中的产品
        $activityProduct = $userExperienceActivities->activityProducts->where('product_id', $productId)->first();
        //没有该产品
        if (!$activityProduct) {
            return false;
        }
        $product = $activityProduct->product;

        // 首次购买，并且该产品用户已经购买过，或者购物车添加过则不参与活动
        if (
            $user && ($this->hasUserPurchasedProduct($user) || $this->hasUserCartProduct($user, $userExperienceActivities->id))
        ) {
            return false;
        }

        // 如果用户未登录且活动需要注册才能参加，直接返回 false
        if (!$user && $userExperienceActivities->is_register) {
            return false;
        }

        // 计算折扣价格
        if ($activityProduct->type == 2) {  // 百分比折扣
            $price = number_format($product->min_price * ($activityProduct->percentage / 100), 2);
        } else {  // 固定价格
            $price = $activityProduct->price;
        }
        return [
            'price' => $price,
            'user_experience_activity_id' => $activityProduct->user_experience_activity_id,
        ];
    }

    //判断用户是否成功购买过商品（任何商品）
    private function hasUserPurchasedProduct(User $user): bool
    {
        return $user->orders()
            ->whereHas('items')  // 只要有订单项目即可，不限制商品类型
            ->where('paid_status', OrderPaidStatusEnum::Paid)
            ->exists();
    }


    // 判断用户是否已经购买过当前活动特价产品
    private function hasUserOrderedProduct(User $user, $user_experience_activity_id): bool
    {
        return $user->orders()
            ->whereHas('items', function ($query) use ($user_experience_activity_id) {
                $query->where('is_activity', true)
                    ->where('user_experience_activity_id', $user_experience_activity_id);
            })
            ->where('paid_status',  OrderPaidStatusEnum::Paid)
            ->exists();
    }
    //判断购物车是不是存在当前活动特价产品
    private function hasUserCartProduct(User $user, $user_experience_activity_id): bool
    {   
        return $user->carts()
            ->whereHas('items', function ($query) use ($user_experience_activity_id) {
                $query->where('is_activity', true)
                    ->where('user_experience_activity_id', $user_experience_activity_id);
            })
            ->where('order_id', null)
            ->exists();
    }


    /**
     * 获取特价产品价格不考虑登录
     */
    public function getProductPrice($productId): array|bool
    {
        $price = false;
        $userExperienceActivities = UserExperienceActivity::with([
            'activityProducts' => function ($query) use ($productId) {
                $query->where('product_id', $productId);
            }
        ])
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->where('is_listed', true)
            ->first();
        // 获取活动的第一个产品
        if (!$userExperienceActivities) {
            return false;
        }
        $activityProduct = $userExperienceActivities->activityProducts->first();

        // 如果活动产品存在，计算价格
        if ($activityProduct) {
            // 判断折扣类型
            if ($activityProduct->type == 2) {  // 百分比折扣
                $product = $activityProduct->product;  // 获取产品
                $price = $product->min_price * ($activityProduct->percentage / 100);
            } else {  // 固定价格
                $price = $activityProduct->price;
            }
        } else {
            return false;
        }

        return [
            'price' => $price,
            'user_experience_activity_id' => $activityProduct->user_experience_activity_id,
        ];
    }

    /**
     * 查看未登录用户的特价产品的价格
     */
    public function getSessionUuidProductPrice($productId, string $sessionUuid = null)
    {
        $price = false;
        // 查找当前的活动并加载相关产品信息
        $userExperienceActivities = UserExperienceActivity::with(['activityProducts.product'])
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->where('is_listed', true)
            ->first();
        // 如果没有找到活动，直接返回 false
        if (!$userExperienceActivities || $userExperienceActivities->activityProducts->isEmpty()) {
            return false;
        }
        // 获取活动中的产品
        $activityProduct = $userExperienceActivities->activityProducts->where('product_id', $productId)->first();
        //没有该产品
        if (!$activityProduct) {
            return false;
        }
        $product = $activityProduct->product;

        //判断当前session_uuid是否已添加过当前产品到购物车
        if ($this->hasSessionUuidCartProduct($productId, $sessionUuid, $userExperienceActivities->id)) {
            return false;
        }

        //判断当前session_uuid是否已购买过任何商品
        if ($this->hasSessionUuidPurchased($sessionUuid)) {
            return false;
        }
        // 计算折扣价格
        if ($activityProduct->type == 2) {  // 百分比折扣
            $price = number_format($product->min_price * ($activityProduct->percentage / 100), 2);
        } else {  // 固定价格
            $price = $activityProduct->price;
        }
        return [
            'price' => $price,
            'user_experience_activity_id' => $activityProduct->user_experience_activity_id,
        ];
    }

    //未登录判断当前临时用户是否添加过当前产品
    public function hasSessionUuidCartProduct($productId, ?string $sessionUuid = null, $user_experience_activity_id)
    {
        $cart = userService()->getOrCreateCart(null, $sessionUuid);
        $cartProducts = $cart->items()->where('is_activity', true)->where('user_experience_activity_id', $user_experience_activity_id)->exists();
        return $cartProducts;
    }

    //判断当前临时用户是否已购买过任何商品
    public function hasSessionUuidPurchased(?string $sessionUuid = null)
    {
        if (!$sessionUuid) {
            return false;
        }

        // 检查该session_uuid是否购买过任何商品
        $hasPurchased = Cart::where('session_uuid', $sessionUuid)
            ->where('is_checkout', true)
            ->whereHas('items')  // 只要有商品项目即可，不限制商品类型
            ->whereHas('order', function ($query) {
                $query->where('paid_status', OrderPaidStatusEnum::Paid->value);
            })
            ->exists();
        return $hasPurchased;
    }

    /**
     * 根据购物车产品查看你是否存在体验活动产品来计算优惠券规则
     */
    public function getUserExperienceActivitiesByCartProducts($cartProducts, $user = null)
    {
        $userExperienceActivities = UserExperienceActivity::query()
            ->whereHas('activityProducts', function ($query) use ($cartProducts) {
                $query->whereIn('product_id', $cartProducts);
            })
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->where('is_listed', true)
            ->with('UserExperienceActivityCoupons')
            ->first();
        if ($userExperienceActivities && !$userExperienceActivities->is_coupon) {
            //没启用优惠券直接跳过
            return [];
        }
        if (
            $userExperienceActivities &&
            $userExperienceActivities->activityProducts->isNotEmpty() &&
            $userExperienceActivities->is_coupon
        ) {
            if ($user) {
                // 获取指定可用优惠券
            } else {
                if ($userExperienceActivities->is_register) {
                    // 需要注册才能用
                    return [];
                }
            }
            //可以使用的活动
            $couponId = $userExperienceActivities->UserExperienceActivityCoupons->pluck('coupon_id')->toArray();
            $cartDiscountRule = Coupon::query()
                ->where('is_global', true)
                ->where('enabled', true)
                ->whereIn('id', $couponId)
                ->get();
        } else {
            // 获取全局优惠规则
            $cartDiscountRule = Coupon::query()
                ->where('is_global', true)
                ->where('is_auto', true)
                ->where('enabled', true)
                ->get();
        }

        return $cartDiscountRule;
    }

    public function getCartDiscountProduct($product)
    {
        //
    }
}
