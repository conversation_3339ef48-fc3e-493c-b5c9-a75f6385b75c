APP_NAME=neiyi
APP_ENV=local
APP_KEY=base64:cKMF1XkcxeG0CW6amBcnaHJEudeSmSeJH9L8V+ubZx4=
APP_DEBUG=true
APP_URL=http://neiyi.test

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=


BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


BING_SEARCH_ADDRESS_KEY=bing地图搜索key
# 谷歌授权登录
GOOGLE_CLIENT_ID=谷歌授权登录id
GOOGLE_CLIENT_SECRET=谷歌授权登录SECRET
GOOGLE_ANDROID_CLIENT_ID=无用
# 谷歌人机验证key
GOOGLE_CAPTCHA_BACKEND_KEY=谷歌人机验证key

SOCIALITE_PROXY_ENABLED=网络代理(翻墙用)
SOCIALITE_PROXY_HTTP=网络代理ip端口
SOCIALITE_PROXY_HTTPS=网络代理ip端口
# paypal支付
PAYPAL_CLIENT_ID=your-client-id
PAYPAL_CLIENT_SECRET=your-client-secret
PAYPAL_MODE=sandbox
# 脸书登录key
FACEBOOK_CLIENT_ID=脸书授权登录clientid
FACEBOOK_CLIENT_SECRET=脸书授权登录SECRET
# 分享hashid加盐
HASHIDS_SALT=allurense

# 推特登录
TWITTER_CLIENT_ID=*************************
TWITTER_CLIENT_SECRET=UQFMgwmovqeZ6wqxuQpt7RI7qbcy84XrTKajDm1AKuuzj9VAGV
TWITTER_REDIRECT_URL=http://allurense.test/twittercallback

## AIRwallex 支付
PAYMENT_AIRWALLEX_CLIENT_ID=xT2-DftORlCJe9ynuG4G-Q
PAYMENT_AIRWALLEX_API_KEY=a87c4c7a70060bb273e93f73ca0e16590fdb7c67448d6c6c1b646533c3d0fb3ead39f242cac1b189643c07a7f14821a8
PAYMENT_AIRWALLEX_SANDBOX=true
PAYMENT_AIRWALLEX_3DS_CALLBACK_URL=http://allurense.test/3dscallback
PAYMENT_AIRWALLEX_ERROR_PAGE_URL=http://allurense.test/airwallex/pay-error
PAYMENT_AIRWALLEX_SUCCESS_PAGE_URL=http://allurense.test/airwallex/pay-success
