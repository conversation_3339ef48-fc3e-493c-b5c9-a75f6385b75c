<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum EmailRuleEventEnum: int
{
    case OrderPaidSuccess = 1;
    case LogisticsInspection = 2;
    case OrderPaidFail = 3;
    case SubscribeSuccess = 4;
    case SubscriptionCancel = 5;
    case Register = 6;
    case Question = 7;
    case UserInvite = 8;
    case FindPassword = 9;
    case InitPassword = 10;
    case SubscribeMessage = 11;
    case Coupon = 12;
    case SubscribeConfirm = 13;
    case UserInviteRewardGrant = 14;
    case Group = 15;
    case Comment = 16;
    case FirstOrder = 17;
    case CouponExpire = 18;
    case Verificationcode = 19;
    case InviteComment = 20;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::OrderPaidSuccess->value => '支付成功',
            self::LogisticsInspection->value => '物流查收',
            self::OrderPaidFail->value => '支付失败',
            self::SubscribeSuccess->value => '订阅成功',
            self::SubscriptionCancel->value => '订阅取消',
            self::Register->value => '注册成功',
            self::Question->value => '问题咨询',
            self::UserInvite->value => '邀请好友',
            self::FindPassword->value => '找回密码',
            self::InitPassword->value => '初始化密码',
            self::SubscribeMessage->value => '订阅消息',
            self::Coupon->value => '优惠券',
            self::SubscribeConfirm->value => '订阅确认',
            self::UserInviteRewardGrant->value => '用户邀请奖励发放',
            self::Group->value => '用户组',
            self::Comment->value => '评论有礼',
            self::FirstOrder->value => '首单奖励',
            self::CouponExpire->value => '优惠券过期提醒',
            self::Verificationcode->value => '验证码',
            self::InviteComment->value => '邀请评论'
        ];
    }
}
