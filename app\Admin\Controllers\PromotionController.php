<?php

namespace App\Admin\Controllers;

use App\Admin\Requests\PromotionRequest;
use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Filters\EquipmentTypeFilter;
use App\Http\Controllers\Controller;
use App\Models\AssociatedCollection;
use App\Models\AssociatedMode;
use App\Models\Enums\EquipmentTypeEnum;
use App\Models\Enums\Promotion\PromotionPositionEnum;
use App\Models\Product\Product;
use App\Models\Promotion;
use App\Models\PromotionContent;
use App\Models\PromotionProduct;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PromotionController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::PromotionUpdate)->only(['update', 'store', 'destroy']);
        $this->hasPermissionOr(Permissions::PromotionUpdate, Permissions::PromotionIndex)->only(['index', 'show']);
    }

    /**
     *  获取列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request)
    {
        $builder = QueryBuilder::for(Promotion::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('title'), // 模糊查询标题
                AllowedFilter::callback('status', function (Builder $builder, $value) {
                    if ($value !== '') {
                        $builder->where('status', $value);
                    }
                }),
                AllowedFilter::custom('equipment_type', new EquipmentTypeFilter()), // 自定义过滤器
            ])
            ->allowedSorts(['sort', 'id'])
            ->defaultSort('sort');
        // 分页
        $res = $builder->paginate($this->getPerPage());

        // 处理 position 数据
        $res->each(function ($item) {
            $item->position = PromotionPositionEnum::from($item->position)->desc();
            $item->equipment_type = EquipmentTypeEnum::from($item->equipment_type)->desc();
        });
        return JsonResource::collection($res);
    }

    /**
     * 展示信息
     * @param \App\Models\Promotion $promotion
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(Promotion $promotion): JsonResource
    {
        $promotion->loadMissing([
            'contents',
            'contents.collection',
            'contents.attachment:id,path,disk,module',
            'productContents'
        ]);
        if ($promotion->productContents) {
            $data = $promotion->productContents->data;
            $product_ids = Arr::get($data, 'product_ids', []);
            if ($product_ids) {
                $products = Product::query()->select('id', 'spu')->whereIn('id', $product_ids)->get();
            }
            $promotion->productContents->data = array_merge($data, ['products' => $products ?? []]);
        }

        return JsonResource::make($promotion);
    }

    /**
     * 创建
     * @param PromotionRequest $request
     * @return JsonResource
     */
    public function store(PromotionRequest $request, Promotion $promotion): JsonResource
    {
        $validated = $request->validated();
        try {
            DB::beginTransaction();

            // 内容信息
            $contents = Arr::pull($validated, 'contents', []);
            $product_contents = Arr::pull($validated, 'product_contents', []);

            // 商品信息
            $promotion->fill($validated)->save();

            // 内容
            if ($contents) {
                Arr::map($contents, function ($content) use ($promotion) {
                    $content = new PromotionContent($content);
                    $content->promotion()->associate($promotion);
                    $content->save();
                });
            }

            // 商品内容
            if ($product_contents) {
                $product_content = new PromotionProduct($product_contents);
                $product_content->promotion()->associate($promotion);
                $product_content->save();
            }

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($promotion);
    }

    /**
     * 更新
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Promotion $promotion
     * @return \Illuminate\Http\Resources\Json\JsonResource
     * @throws \App\Exceptions\DataException
     */
    public function update(PromotionRequest $request, Promotion $promotion): JsonResource
    {
        $validated = $request->validated();
        try {
            DB::beginTransaction();

            // 内容信息
            $contents = Arr::pull($validated, 'contents', []);
            $product_contents = Arr::pull($validated, 'product_contents', []);
            // 更新广告位信息
            $promotion->update(
                $validated,
            );

            // 删除不在给定 ids 列表中的内容
            if (is_array($contents)) {
                $ids = array_filter(array_column($contents, 'id'));
                $promotion->contents()->whereNotIn('id', $ids)->delete();

                // 修改子广告位
                foreach ($contents as $content) {
                    $content = Arr::except($content, ['product', 'collection', 'attachment']);
                    if ($id = Arr::get($content, 'id')) {
                        // 修改子广告位
                        $old_content = $promotion->contents()->find($id);
                        $old_content->update($content);
                    } else {
                        // 新增子广告位
                        $content = new PromotionContent($content);
                        $content->promotion()->associate($promotion);
                        $content->save();
                    }
                }
            }
            // 商品广告内容
            if (is_array($product_contents)) {
                $promotion->productContents()->update($product_contents);
            }
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($promotion);
    }

    /**
     * 删除（批量）
     * @param Request $request
     * @return JsonResource
     */
    public function destroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Promotion::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除子广告位
        PromotionContent::query()->whereIn('promotion_id', $ids)->delete();

        // 删除产品广告位
        PromotionProduct::query()->whereIn('promotion_id', $ids)->delete();

        // 删除
        Promotion::query()->whereIn('id', $ids)->delete();
        return response()->json();
    }

    // 启用禁用
    public function status(Request $request, Promotion $Promotion)
    {
        $validated = $request->validate([
            'status' => ['required', 'boolean'],
        ]);
        $Promotion->update($validated);
        return response()->json();
    }
}
