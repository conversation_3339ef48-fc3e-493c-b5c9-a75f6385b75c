<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\User\UserPointChangeRecord;
use App\Models\Enums\User\PointChangeTypeEnum;
use App\Services\UserPointService;
use App\Models\User\User;
use App\Services\MembershipService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class MembershipController extends Controller
{
    protected MembershipService $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * 获取会员等级列表
     */
    public function levels(Request $request): AnonymousResourceCollection
    {
        $levels = QueryBuilder::for(MembershipLevel::class, $request)
            ->allowedSorts(['sort_order', 'min_spend_amount', 'created_at'])
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('slug'),
                AllowedFilter::exact('is_active'),
            ])
            ->defaultSort('sort_order')
            ->get();

        return JsonResource::collection($levels);
    }

    /**
     * 创建会员等级
     */
    public function createLevel(Request $request): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:membership_levels',
            'min_spend_amount' => 'required|numeric|min:0',
            'point_rate' => 'required|numeric|min:0',
            'birthday_points_enabled' => 'nullable|boolean',
            'birthday_points_amount' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:255',
            'benefits' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $level = MembershipLevel::create($validated);

        return JsonResource::make($level);
    }

    /**
     * 更新会员等级
     */
    public function updateLevel(Request $request, MembershipLevel $level): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('membership_levels')->ignore($level->id)
            ],
            'min_spend_amount' => 'required|numeric|min:0',
            'point_rate' => 'required|numeric|min:0',
            'birthday_points_enabled' => 'nullable|boolean',
            'birthday_points_amount' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:255',
            'benefits' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $level->update($validated);

        return JsonResource::make($level);
    }

    /**
     * 删除会员等级
     */
    public function deleteLevel(MembershipLevel $level): JsonResource
    {
        // 检查是否有用户使用此等级
        $userCount = UserMembershipLevel::where('membership_level_id', $level->id)->count();

        if ($userCount > 0) {
            return JsonResource::make([
                'success' => false,
                'message' => "无法删除，有 {$userCount} 个用户正在使用此会员等级",
            ]);
        }

        $level->delete();

        return JsonResource::make([
            'success' => true,
            'message' => '会员等级删除成功',
        ]);
    }

    /**
     * 获取用户会员信息列表
     */
    public function userMemberships(Request $request): AnonymousResourceCollection
    {
        $userMemberships = QueryBuilder::for(UserMembershipLevel::class, $request)
            ->with(['user', 'membershipLevel'])
            ->allowedSorts(['total_spend_amount', 'achieved_at', 'created_at'])
            ->allowedFilters([
                'membership_level_id',
                'is_current',
                AllowedFilter::exact('user.email'),
                AllowedFilter::partial('user.name'),
            ])
            ->where('is_current', true)
            ->defaultSort('-total_spend_amount')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($userMemberships);
    }

    /**
     * 获取用户详细会员信息
     */
    public function userMembershipDetail(User $user): JsonResource
    {
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);

        // 获取用户的会员等级历史
        $levelHistory = UserMembershipLevel::where('user_id', $user->id)
            ->with('membershipLevel')
            ->orderBy('achieved_at', 'desc')
            ->get();

        // 获取最近的积分记录
        $recentPointRecords = UserPointChangeRecord::where('user_id', $user->id)
            ->with(['source'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return JsonResource::make([
            'user' => $user,
            'membership_info' => $membershipInfo,
            'level_history' => $levelHistory,
            'recent_point_records' => $recentPointRecords,
        ]);
    }

    /**
     * 手动调整用户会员等级
     */
    public function adjustUserLevel(Request $request, User $user): JsonResource
    {
        $validated = $request->validate([
            'membership_level_id' => 'required|exists:membership_levels,id',
            'reason' => 'required|string|max:255',
        ]);

        $targetLevel = MembershipLevel::find($validated['membership_level_id']);
        $totalSpend = $this->membershipService->calculateUserTotalSpend($user);

        // 创建或更新用户会员等级
        $userLevel = UserMembershipLevel::createOrUpdate(
            $user->id,
            $targetLevel->id,
            $totalSpend
        );

        // 记录管理员操作
        $userLevel->update([
            'admin_adjusted' => true,
            'admin_reason' => $validated['reason'],
        ]);

        return JsonResource::make([
            'success' => true,
            'message' => '用户会员等级调整成功',
            'user_level' => $userLevel,
        ]);
    }

    /**
     * 获取会员等级统计信息
     */
    public function levelStats(): JsonResource
    {
        $stats = [];

        $levels = MembershipLevel::with(['userMembershipLevels' => function ($query) {
            $query->where('is_current', true);
        }])->get();

        foreach ($levels as $level) {
            $userCount = $level->userMembershipLevels->count();
            $totalSpend = UserMembershipLevel::where('membership_level_id', $level->id)
                ->where('is_current', true)
                ->sum('total_spend_amount');

            $stats[] = [
                'level' => $level,
                'user_count' => $userCount,
                'total_spend_amount' => $totalSpend,
                'avg_spend_amount' => $userCount > 0 ? $totalSpend / $userCount : 0,
            ];
        }

        // 总体统计
        $totalUsers = UserMembershipLevel::where('is_current', true)->count();
        $totalSpendAmount = UserMembershipLevel::where('is_current', true)->sum('total_spend_amount');
        $totalPointsAwarded = UserPointChangeRecord::increase()->completed()->sum('change_amount');

        return JsonResource::make([
            'level_stats' => $stats,
            'overall_stats' => [
                'total_users' => $totalUsers,
                'total_spend_amount' => $totalSpendAmount,
                'avg_spend_per_user' => $totalUsers > 0 ? $totalSpendAmount / $totalUsers : 0,
                'total_points_awarded' => $totalPointsAwarded,
            ],
        ]);
    }

    /**
     * 获取积分记录列表
     */
    public function pointRecords(Request $request): AnonymousResourceCollection
    {
        $records = QueryBuilder::for(UserPointChangeRecord::class, $request)
            ->with(['user', 'source'])
            ->allowedSorts(['created_at', 'change_amount'])
            ->allowedFilters([
                'change_type',
                'status',
                AllowedFilter::exact('user.email'),
                AllowedFilter::partial('user.name'),
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }

    /**
     * 手动发放积分
     */
    public function grantPoints(Request $request): JsonResource
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'points' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
        ]);

        $user = User::find($validated['user_id']);
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);

        if (!$userLevel) {
            return JsonResource::make([
                'success' => false,
                'message' => '用户还没有会员等级，无法发放积分',
            ]);
        }

        // 使用新的积分服务发放积分
        $pointService = new UserPointService();
        $pointRecord = $pointService->addPoints(
            user: $user,
            points: $validated['points'],
            changeType: PointChangeTypeEnum::ManualAdd,
            description: $validated['description'],
            source: $userLevel,
            immediate: true
        );

        return JsonResource::make([
            'success' => true,
            'message' => '积分发放成功',
            'point_record' => $pointRecord,
        ]);
    }

    /**
     * 批量重新计算用户会员等级
     */
    public function recalculateUserLevels(Request $request): JsonResource
    {
        $validated = $request->validate([
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $validated['user_ids'] ?? User::pluck('id')->toArray();
        $processedCount = 0;
        $errors = [];

        foreach ($userIds as $userId) {
            try {
                $user = User::find($userId);
                if ($user) {
                    $this->membershipService->checkAndUpgradeUserLevel($user);
                    $processedCount++;
                }
            } catch (\Exception $e) {
                $errors[] = "用户 ID {$userId}: " . $e->getMessage();
            }
        }

        return JsonResource::make([
            'success' => true,
            'message' => "成功处理 {$processedCount} 个用户的会员等级",
            'processed_count' => $processedCount,
            'total_count' => count($userIds),
            'errors' => $errors,
        ]);
    }

    /**
     * 用户积分统计
     */
    public function userPointStats(): AnonymousResourceCollection
    {
        // 简化查询，避免复杂的JOIN操作
        $query = User::query()
            ->with([
                'wallet',
                'currentMembershipLevel.membershipLevel'
            ]);

        // 应用筛选条件
        if (request()->filled('filter.email')) {
            $query->where('email', 'like', '%' . request()->input('filter.email') . '%');
        }

        if (request()->filled('filter.status')) {
            $query->where('status', request()->input('filter.status'));
        }

        if (request()->filled('filter.membership_level_id')) {
            $query->whereHas('currentMembershipLevel', function ($q) {
                $q->where('membership_level_id', request()->input('filter.membership_level_id'));
            });
        }

        // 创建时间筛选
        if (request()->filled('filter.created_at_start')) {
            $query->where('created_at', '>=', request()->input('filter.created_at_start'));
        }

        if (request()->filled('filter.created_at_end')) {
            $query->where('created_at', '<=', request()->input('filter.created_at_end') . ' 23:59:59');
        }

        // 先获取所有符合条件的用户
        $allUsers = $query->get();

        // 在应用层按会员等级排序
        $sortedUsers = $allUsers->sortBy(function ($user) {
            return $user->currentMembershipLevel?->membershipLevel?->sort_order ?? 999999;
        })->values();

        // 手动分页
        $perPage = $this->getPerPage();
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedUsers = $sortedUsers->slice($offset, $perPage);

        // 获取分页用户的ID，用于批量查询积分消耗
        $userIds = $paginatedUsers->pluck('id')->toArray();

        // 批量查询所有用户的积分消耗
        $pointConsumptions = $this->calculatePointConsumptions($userIds);

        // 为每个用户计算积分统计
        $transformedUsers = $paginatedUsers->map(function ($user) use ($pointConsumptions) {
            // 获取该用户的积分消耗
            $totalConsumed = $pointConsumptions[$user->id] ?? 0;

            return [
                'id' => $user->id,
                'email' => $user->email,
                'status' => $user->status,
                'birthday' => $user->birth_date ? $user->birth_date->format('Y-m-d') : null,
                'created_at' => $user->created_at ? $user->created_at->format('Y-m-d H:i:s') : null,
                'current_points' => $user->wallet ? $user->wallet->point : 0,
                'total_consumed_points' => $totalConsumed,
                'membership_level' => $user->currentMembershipLevel?->membershipLevel ? [
                    'id' => $user->currentMembershipLevel->membershipLevel->id,
                    'name' => $user->currentMembershipLevel->membershipLevel->name,
                    'level' => $user->currentMembershipLevel->membershipLevel->level,
                    'color' => $user->currentMembershipLevel->membershipLevel->color,
                    'sort' => $user->currentMembershipLevel->membershipLevel->sort,
                ] : null,
            ];
        });

        // 创建分页对象
        $users = new \Illuminate\Pagination\LengthAwarePaginator(
            $transformedUsers,
            $sortedUsers->count(),
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );

        // 添加查询参数到分页链接
        $users->appends(request()->query());

        return JsonResource::collection($users);
    }

    /**
     * 用户积分变化记录
     */
    public function userPointRecords(int $userId): AnonymousResourceCollection
    {
        // 验证用户是否存在
        User::findOrFail($userId);

        // 使用 QueryBuilder 查询用户的积分变化记录
        $records = QueryBuilder::for(UserPointChangeRecord::class)
            ->with([
                'user:id,email',
                'source' // 多态关联，可能是订单、任务等
            ])
            ->where('user_id', $userId)
            ->where('status', 'completed') // 只获取已完成的记录
            ->allowedFilters([
                // 时间筛选
                AllowedFilter::callback('created_at_start', function ($query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('created_at_end', function ($query, $value) {
                    $query->where('created_at', '<=', $value . ' 23:59:59');
                }),
                // 变化类型筛选
                AllowedFilter::exact('change_type'),
                // 状态筛选
                AllowedFilter::exact('status'),
                // 积分变化方向筛选
                AllowedFilter::callback('change_direction', function ($query, $value) {
                    if ($value === 'increase') {
                        $query->where('change_amount', '>', 0);
                    } elseif ($value === 'decrease') {
                        $query->where('change_amount', '<', 0);
                    }
                }),
            ])
            ->allowedSorts([
                'id',
                'change_amount',
                'created_at',
                'completed_at',
            ])
            ->defaultSort('-created_at')
            ->paginate($this->getPerPage())
            ->appends(request()->query());

        // 转换数据格式
        $records->getCollection()->transform(function ($record) {
            // 获取积分变化时的会员等级
            $membershipLevel = $this->getUserMembershipLevelAtTime($record->user_id, $record->created_at);
            $record->user_email = $record->user ? $record->user->email : null;
            $record->membership_level_at_time = $membershipLevel;

            // 移除不需要在前端显示的关联数据
            unset($record->user);

            return $record;
        });

        return JsonResource::collection($records);
    }

    /**
     * 获取用户在特定时间的会员等级
     */
    private function getUserMembershipLevelAtTime(int $userId, $timestamp): ?array
    {
        try {
            // 使用 Eloquent 查找在指定时间之前最近的会员等级记录
            $userMembershipLevel = UserMembershipLevel::with('membershipLevel')
                ->where('user_id', $userId)
                ->where('created_at', '<=', $timestamp)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$userMembershipLevel || !$userMembershipLevel->membershipLevel) {
                return null;
            }

            $level = $userMembershipLevel->membershipLevel;

            return [
                'id' => $level->id,
                'name' => $level->name,
                'level' => $level->level,
                'color' => $level->color,
                'sort' => $level->sort,
                'obtained_at' => $userMembershipLevel->created_at ? $userMembershipLevel->created_at->format('Y-m-d H:i:s') : null,
            ];
        } catch (\Exception) {
            return null;
        }
    }

    /**
     * 计算用户积分消耗
     *
     * @param array $userIds 用户ID数组
     * @return array 用户ID => 积分消耗总量的映射
     */
    private function calculatePointConsumptions(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }

        // 方法1：从优惠券兑换规则中计算积分消耗
        try {
            $results = [];

            // 查询所有积分兑换的优惠券记录
            $records = DB::table('coupon_grant_records as cgr')
                ->select('cgr.user_id', 'cgr.coupon_grant_rule_id', 'uc.coupon_id', 'rules.config')
                ->join('coupon_grant_rules as rules', 'cgr.coupon_grant_rule_id', '=', 'rules.id')
                ->join('user_coupons as uc', 'cgr.user_coupon_id', '=', 'uc.id')
                ->whereIn('cgr.user_id', $userIds)
                ->where('rules.type', 'point_exchange')
                ->get();

            // 处理每条记录，计算积分消耗
            foreach ($records as $record) {
                $userId = $record->user_id;
                $couponId = $record->coupon_id;
                $config = json_decode($record->config, true);

                if (!isset($results[$userId])) {
                    $results[$userId] = 0;
                }

                // 从规则配置中查找对应优惠券的积分消耗
                if (isset($config['coupons']) && is_array($config['coupons'])) {
                    foreach ($config['coupons'] as $couponConfig) {
                        if (isset($couponConfig['coupon_id']) && $couponConfig['coupon_id'] == $couponId) {
                            $points = $couponConfig['required_points'] ?? 0;
                            $results[$userId] += $points;
                            break;
                        }
                    }
                }
            }

            return $results;
        } catch (\Exception $e) {
            // 如果查询失败，返回空数组
            logger()->error('计算积分消耗失败: ' . $e->getMessage());
            return array_fill_keys($userIds, 0);
        }
    }
}
