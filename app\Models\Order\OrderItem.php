<?php

namespace App\Models\Order;

use App\Models\Coupon\Coupon;
use App\Models\User\UserCoupon;
use App\Utils\PrecisionMath;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property OrderItemProduct $product
 * @property int $num
 * @property float $coupon_discount_price
 * @property float $price
 */
class OrderItem extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    public static function booted()
    {
        static::creating(function (self $model) {
            $model->updateCurrencyPrices();
        });

        static::saving(function (self $model) {
            if ($model->isDirty(['price', 'original_price', 'coupon_discount_price'])) {
                $model->updateCurrencyPrices();
            }
        });
    }


    protected $casts = [
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'price_currency' => 'decimal:2',
        'original_price_currency' => 'decimal:2',
        'coupon_discount_price' => 'decimal:2',
        'coupon_discount_price_currency' => 'decimal:2',
    ];

    protected $appends = [
        'total_price',
        'total_price_agent',
        'price_agent',
        'original_price_agent',
        'coupon_discount_price_agent',
        'sell_price',
        'sell_price_agent'
    ];

    public function totalPrice(): Attribute
    {
        return Attribute::get(
            fn() => PrecisionMath::toFloat(PrecisionMath::mul($this->num, $this->price))
        );
    }
    public function sellPrice(): Attribute
    {
        return Attribute::get(
            fn() => PrecisionMath::toFloat(
                PrecisionMath::sub(
                    PrecisionMath::mul($this->price, $this->num),
                    $this->coupon_discount_price
                )
            )
        );
    }

    /**************** 金额币种转换 ****************/
    public function sellPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => PrecisionMath::toFloat(
                PrecisionMath::sub(
                    PrecisionMath::mul(convertPrice($this->price, currentCurrency()), $this->num),
                    convertPrice($this->coupon_discount_price, currentCurrency())
                )
            )
        );
    }

    /**************** 金额币种转换 ****************/
    public function totalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => PrecisionMath::toFloat(
                PrecisionMath::mul(convertPrice($this->price, $this->order->currency), $this->num)
            )
        );
    }

    public function priceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->price, $this->order->currency)
        );
    }

    public function originalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => PrecisionMath::toFloat(
                PrecisionMath::mul(convertPrice($this->original_price, $this->order->currency), $this->num)
            )
        );
    }

    public function couponDiscountPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->coupon_discount_price, $this->order->currency)
        );
    }

    /**************** 关联模型 ****************/
    public function product(): MorphTo
    {
        return $this->morphTo();
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function userCoupon(): BelongsTo
    {
        return $this->belongsTo(UserCoupon::class);
    }

    /**
     * 更新当前币种对应的价格
     */
    private function updateCurrencyPrices(): void
    {
        $currentCurrency = currentCurrency();
        // 转换价格到当前币种
        $this->price_currency = convertPrice($this->price, $currentCurrency);
        $this->original_price_currency = convertPrice($this->original_price, $currentCurrency);
        $this->coupon_discount_price_currency = convertPrice($this->coupon_discount_price, $currentCurrency);
    }
}
