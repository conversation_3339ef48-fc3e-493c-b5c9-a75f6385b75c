<?php

namespace App\Http\Controllers;


use App\Apis\Google\GoogleRecaptchaService;
use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Constants\QueueKey;
use App\Exceptions\DataException;
use App\Jobs\EmailRuleNotice;
use App\Models\CouponGrantRule;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\Order\Order;
use App\Models\User\User;
use App\Models\User\UserPasswordRetrieve;
use App\Services\CouponGrant\CouponGrantService;
use Google\Client;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Laravel\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\FacebookProvider;
use Laravel\Socialite\Two\User as SocialiteUser;
use Spatie\QueryBuilder\QueryBuilder;
use Throwable;

class AuthController extends Controller
{

    /**
     * 注册
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws ValidationException
     */
    public function register(Request $request): JsonResource
    {
        $validated = $request->validate([
            'email' => ['required', 'string', 'email'],
            'invite_code' => ['nullable', 'string'],
            'password' => ['required', 'string', 'min:6', 'max:32'],
            'first_name' => ['required', 'string', 'max:32'],
            'last_name' => ['required', 'string', 'max:32'],
            'birth_date' => ['date', 'nullable'],
        ]);
        $email = Arr::get($validated, 'email');
        $user = User::query()->where('email', $email)
            ->whereNull('deleted_at')
            ->first();
        if ($user instanceof User) {
            // 快速支付和转盘的用户可以二次注册, 有密码了说明注册过了
            if (
                !in_array($user->register_type, [
                    UserRegisterTypesEnum::FastPay,
                    UserRegisterTypesEnum::CouponRandomGrant,
                    UserRegisterTypesEnum::Subscription,
                    UserRegisterTypesEnum::Comment,
                ]) ||
                $user->password
            ) {
                throw new DataException("This email has already been registered, please log in directly.", ErrorCode::RegisterRepeat);
            }
        }
        $registerType = UserRegisterTypesEnum::Default;
        // 验证邀请码
        if ($inviteCode = Arr::pull($validated, 'invite_code')) {
            $registerType = UserRegisterTypesEnum::Invite;
            try {
                [$inviteUserId] = hashids()->decode($inviteCode);
            } catch (Throwable $e) {
                throw ValidationException::withMessages(['invite_code' => 'Invitation code error']);
            }
        }
        // 创建或者更新用户
        try {
            DB::beginTransaction();

            if ($user instanceof User) {
                $user->update($validated);
            } else {
                $user = userService()->createUser(
                    Arr::get($validated, 'email'),
                    Arr::get($validated, 'password'),
                    Arr::get($validated, 'first_name'),
                    Arr::get($validated, 'last_name'),
                    Arr::get($validated, 'birth_date'),
                    $registerType
                );
            }

            // 有邀请码，绑定
            if ($inviteCode && !empty($inviteUserId)) {
                // 绑定父级
                $user->parent()->associate($inviteUserId)->save();
                // 刷新
                $user->refresh();
                // 更新父级首次分享时间
                if ($user->parent && !$user->parent->first_share_date) {
                    $user->parent->update([
                        'first_share_date' => now(),
                    ]);
                }
            }

            if (!$user) {
                throw new DataException("Registration failed.");
            }
            // 禁用
            if ($user->{'status'} == UserStatusEnum::StatusDisabled) {
                throw new DataException("Login failed, the user has been blacklisted by the administrator.");
            }

            // 登录成功，生成token
            $expireAt = now()->addDays(30);
            $token = $user->createToken('api', ['*'], $expireAt);

            // 规则：注册用户发放优惠卷
            list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::RegisterUser, $user, true);
            $invite_code = hashids()->encode($user->id);
            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DataException("Registration failed.");
        }

        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt,
            'invite_code' => $invite_code,
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }

    /**
     * 找回密码
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws Throwable
     * @throws ValidationException
     */
    public function resetPassword(Request $request): JsonResource
    {
        $validated = $request->validate([
            'password' => 'required|string|min:6',
            'key' => 'required|string',
        ]);
        if (Str::length($validated['key']) !== 36) {
            throw ValidationException::withMessages(['key' => 'Modification failed, the information you submitted is incorrect']);
        }
        /**
         * @var UserPasswordRetrieve $userPasswordRetrieve
         */
        $userPasswordRetrieve = UserPasswordRetrieve::query()->where('key', $validated['key'])->first();
        if (!$userPasswordRetrieve instanceof UserPasswordRetrieve || now()->subDay()->gte($userPasswordRetrieve->created_at)) {
            throw ValidationException::withMessages(['key' => 'Modification failed, the information you submitted is incorrect']);
        }
        if ($userPasswordRetrieve->is_used) {
            throw ValidationException::withMessages(['key' => 'The key has been used']);
        }
        $user = $userPasswordRetrieve->user;
        DB::beginTransaction();
        try {
            $userPasswordRetrieve->update(['is_used' => true]);
            $user->update(['password' => $validated['password']]);
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            throw new DataException('The server is busy.');
        }

        $expireAt = now()->addMonths(6);
        $token = $user->createToken('api', ['*'], $expireAt);
        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt
        ]);
    }

    /**
     * 找回密码-发送邮件
     * @param Request $request
     * @return JsonResponse
     * @throws DataException
     * @throws ValidationException
     */
    public function resetSendEmail(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'callback_url' => 'required|string',
        ]);

        $user = userService()->findByEmail($validated['email']);
        if (!$user instanceof User) {
            throw ValidationException::withMessages(['email' => 'The email account you entered does not exist']);
        }
        // 查看最后一条
        $lastPasswordRetrieve = $user->passwordRetrieves()->latest()->first();
        if ($lastPasswordRetrieve && now()->subMinute()->lte($lastPasswordRetrieve->created_at)) {
            throw new DataException('Please do not send frequently');
        }
        $key = Str::uuid();
        $res = $user->passwordRetrieves()->create([
            'key' => $key,
        ]);
        $url = env('WEB_URL') . $validated['callback_url'] . '?key=' . $key . '&email=' . $validated['email'];
        // 邮件队列
        EmailRuleNotice::dispatch(EmailRuleEventEnum::FindPassword, user: $user, data: ['url' => $url])->onQueue(QueueKey::Default->value);

        return response()->json([]);
    }

    public function loginByFacebook(Request $request): JsonResource
    {
        $token = $request->validate(['token' => 'required|string'])['token'];
        try {
            /**
             * @var FacebookProvider $socialite
             */
            $socialite = Socialite::driver('facebook')->stateless();
            try {
                // 根据access_token 获取用户信息
                $userData = $socialite->userFromToken($token);
            } catch (Throwable $exception) {
                throw new DataException("Code verification failed.", ErrorCode::ValidationException);
            }
            // 正常的谷歌用户
            if (!$userData instanceof SocialiteUser) {
                throw new DataException("Login failed, user information retrieval failed.", ErrorCode::ValidationException);
            }
            // 创建用户
            $facebookUser = userService()->firstOrCreateFacebookUser($userData);
            $user = $facebookUser->user;
            $coupon_grant_rule = null;
            $coupon = null;
            if (!$user) {
                $user = userService()->createUserByFacebook($facebookUser);
                // 规则：注册用户发放优惠卷
                list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::RegisterUser, $user, true);
            }
        } catch (\Throwable $exception) {
            throw new DataException("Token parsing failed.", ErrorCode::ValidationException);
        }
        $expireAt = now()->addMonths(6);
        $token = $user->createToken('api', ['*'], $expireAt);
        $invite_code = hashids()->encode($user->id);
        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt,
            'invite_code' => $invite_code,
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * @throws DataException
     */
    public function twitterRedirect(Request $request): \Illuminate\Http\RedirectResponse
    {
        try {
            /**
             * @var \SocialiteProviders\Twitter\Provider $twitter
             */
            $serverRedirect = config('services.twitter.redirect');
            $param = $request->all();
            config()->set('services.twitter.redirect', $serverRedirect . '?' . http_build_query($param));
            $twitter = Socialite::driver('twitter');
            $redirectUrl = $twitter->redirect()->getTargetUrl();
        } catch (Throwable $exception) {
            throw new DataException("callback_url failed.", ErrorCode::ValidationException);
        }
        return response()->redirectTo($redirectUrl);
    }

    public function loginByTwitter(Request $request)
    {
        $request->validate([
            'oauth_token' => 'required|string',
            'oauth_verifier' => 'required|string',
        ]);
        try {
            $twitter = Socialite::driver('twitter');
            $userData = $twitter->user();
            //
            if (!$userData instanceof \SocialiteProviders\Manager\OAuth1\User) {
                throw new DataException("Login failed.");
            }
            // 创建用户
            $twitterUser = userService()->firstOrCreateTwitterUser($userData);
            $user = $twitterUser->user;
            $coupon_grant_rule = null;
            $coupon = null;
            if (!$user) {
                $user = userService()->createUserByTwitter($twitterUser);
                // 规则：注册用户发放优惠卷
                list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::RegisterUser, $user, true);
            }
        } catch (Throwable $exception) {
            return response()->redirectTo(config('services.twitter.frontend_redirect') . "?" . http_build_query([
                'code' => 401,
                ...$request->except(['oauth_token', 'oauth_verifier'])
            ]));
        }

        $expireAt = now()->addMonths(6);
        $token = $user->createToken('api', ['*'], $expireAt);
        $invite_code = hashids()->encode($user->id);
        return response()->redirectTo(config('services.twitter.frontend_redirect') . "?" . http_build_query([
            'token' => $token->plainTextToken,
            'code' => 200,
            'invite_code' => $invite_code,
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]],
            ...$request->except(['oauth_token', 'oauth_verifier'])
        ]));
    }

    /**
     * 谷歌登录
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function loginByGoogle(Request $request): JsonResource
    {
        $token = $request->validate(['token' => 'required|string'])['token'];
        try {
            // 本地需要代理
            $clientConfig = [
                'verify' => false,
                'timeout' => 30,
            ];
            if ($proxy = config('services.socialite_proxy')) {
                if ($proxy['enabled']) {
                    $clientConfig['proxy'] = [
                        'http' => $proxy['http'],
                        'https' => $proxy['https'],
                    ];
                }
            }
            $client = new \GuzzleHttp\Client($clientConfig);
            $google = new Client(['client_id' => config('services.google.client_id')]);
            $google->setHttpClient($client); //
            $payload = $google->verifyIdToken($token);
            if ($payload) {
                if ($payload['iss'] !== 'https://accounts.google.com') {
                    throw new DataException("Invalid ID token.", ErrorCode::ValidationException);
                }
            } else {
                throw new DataException("Invalid ID token.", ErrorCode::ValidationException);
            }
            // 创建用户去
            $googleUser = userService()->firstOrCreateGoogleUser($payload);
            $user = $googleUser->user;
            $coupon_grant_rule = null;
            $coupon = null;
            if (!$user) {
                $user = userService()->createUserByGoogle($googleUser);
                // 规则：注册用户发放优惠卷
                list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::RegisterUser, $user, true);
            }
        } catch (\Throwable $exception) {
            throw new DataException("Token parsing failed.", ErrorCode::ValidationException);
        }
        $expireAt = now()->addMonths(6);
        $token = $user->createToken('api', ['*'], $expireAt);
        $invite_code = hashids()->encode($user->id);
        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt,
            'invite_code' => $invite_code,
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResource
     * @throws ValidationException
     */
    public function login(Request $request): JsonResource
    {
        $validated = $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string|min:6',
            //            'g_recaptcha_response' => 'required|string'
        ]);
        // 安全验证
        $gRecaptchaResponse = Arr::pull($validated, 'g_recaptcha_response');
        if (!$gRecaptchaResponse || !GoogleRecaptchaService::siteVerify($gRecaptchaResponse)) {
            //            throw new DataException('Please verify through human-machine first.');
        }
        /**
         * @var  $user User
         */
        $user = userService()->findByEmail($validated['email']);

        $cacheKey = CacheKey::AuthLoginEmailErrorTimes->getKey($validated['email']);
        $errorTimes = (int) Cache::get($cacheKey);
        if ($errorTimes > 3) {
            throw new DataException('Too many attempts, please try again later');
        }
        if (!$user) {
            throw ValidationException::withMessages(['email' => ['Account not registered.'],]);
        }
        if (!Hash::check($validated['password'], $user->password)) {
            Cache::set($cacheKey, $errorTimes + 1, now()->addMinutes(30));
            throw ValidationException::withMessages(['password' => ['Password error, please check and try again.'],]);
        }
        // 禁用
        if ($user->{'status'} == UserStatusEnum::StatusDisabled) {
            throw ValidationException::withMessages(['email' => ['The account has been disabled by the administrator.'],]);
        }
        $expireAt = now()->addDays(30);
        $token = $user->createToken('api', ['*'], $expireAt);

        cache()->forget($cacheKey);
        $invite_code = hashids()->encode($user->id);
        return JsonResource::make($user)->additional([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt,
            'invite_code' => $invite_code
        ]);
    }
}
