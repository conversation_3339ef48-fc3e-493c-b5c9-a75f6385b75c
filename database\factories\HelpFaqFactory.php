<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HelpFaq>
 */
class HelpFaqFactory extends Factory
{
    /**
     * Define the model's default state.
     * 需要生成的字段
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'parent_id' => NUll,
            'icon_id' => $this->faker->numberBetween(1, 50),// 随机数
            'title' => $this->faker->title(), // 标题
            'desc' => $this->faker->randomHtml(), // 富文本
            'sort' => $this->faker->numberBetween(1, 100), // 排序
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return static
     */
    public function unverified()
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
