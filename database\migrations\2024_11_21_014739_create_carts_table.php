<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carts', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->nullable()->index()->comment('订单id');
            $table->bigInteger('user_id')->nullable()->index()->comment('用户id');
            $table->string('session_uuid', 36)->nullable()->comment('游客临时id');
            $table->decimal('subtotal', 10, 4)->default(0)->comment('小计金额(总价原价)');
            $table->decimal('discount', 10, 4)->default(0)->comment('优惠总计');
            $table->decimal('coupon_discount', 10, 4)->default(0)->comment('优惠金额/购物车优惠or常规优惠总计');
            $table->bigInteger('shipping_id')->nullable()->comment('运费模板id');
            $table->string('shipping_type')->default('general')->comment('运费类型');
            $table->decimal('delivery_fee', 10, 4)->default(0)->comment('运费');
            $table->decimal('delivery_fee_discount', 10, 4)->default(0)->comment('折扣运费部分');
            $table->bigInteger('delivery_fee_discount_id')->nullable()->comment('购物车优惠id(运费部分)');
            $table->decimal('total', 10, 4)->default(0)->comment('总价(小计-优惠+运费-运费折扣)');
            $table->boolean('is_checkout')->default(false)->comment('是否结算');
            $table->boolean('is_checkout_processing')->default(false)->comment('是否正在结算');
            $table->json('checkout_data')->nullable()->comment('结账信息');
            $table->string('country')->default('US')->comment('国家');
            $table->dateTime('country_update_at')->nullable()->comment('国家更新时间');
            $table->string('type', 100)->default('normal')->comment('购物车类型，正常normal,临时temp');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('购物车表,支持游客和正常用户');
            $table->engine('InnoDB');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carts');
    }
};
