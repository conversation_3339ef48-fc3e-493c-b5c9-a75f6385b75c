<?php

namespace App\Notifications;

use App\Models\Contract\Contract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class UserRemind extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public Contract $contract, $type = null)
    {
    }

    /**
     * @return string[]
     */
    public function via()
    {
        return ['database'];
    }


    /**
     * @return array
     */
    public function toArray(): array
    {
        //您的合同{合同名称}有一笔应收款即将在{应付款时间}到期。请及时处理。
        return [
            'contract_name' => $this->contract->contractName->name,
            'payment_date' => $this->contract->payment_date,
            'content' => "您的合同{$this->contract->contractName->name}有一笔应收款即将在{$this->contract->payment_date->toDateString()}到期。请及时处理。",
            'title' => '应收款到期提醒'
        ];
    }
}
