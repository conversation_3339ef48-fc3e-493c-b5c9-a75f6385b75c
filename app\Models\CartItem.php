<?php

namespace App\Models;

use App\Models\Coupon\Coupon;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\User\UserCoupon;
use App\Utils\PrecisionMath;
use App\Utils\PrecisionMath;
use App\Models\User\UserCoupon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property  ProductVariant $productVariant
 * @property  int $coupon_id
 * @property  int $num
 * @property  float $original_price
 * @property  float $price
 * @property  float $coupon_discount_price
 */
class CartItem extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'add_price' => 'float',
        'original_price' => 'float',
        'discount' => 'float',
        'price' => 'float',
        'is_activity' => 'boolean'
    ];

    protected $appends = [
        'total_price',
        'total_price_agent',
        'add_price_agent',
        'original_price_agent',
        'discount_agent',
        'price_agent',
        'sell_price',
        'sell_price_agent'
    ];

    public function totalPrice(): Attribute
    {
        return Attribute::get(
            fn () => PrecisionMath::toFloat(PrecisionMath::mul($this->num, $this->price))
        );
    }

    /**
     * 最终销售价
     * @return Attribute
     */
    public function sellPrice(): Attribute
    {
        return Attribute::get(
            fn () => PrecisionMath::toFloat(
                PrecisionMath::sub(
                    PrecisionMath::mul($this->price, $this->num),
                    $this->discount
                )
            )
        );
    }

    /**************** 金额币种转换 ****************/
    public function sellPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => PrecisionMath::toFloat(
                PrecisionMath::sub(
                    PrecisionMath::mul(convertPrice($this->price, currentCurrency()), $this->num),
                    convertPrice($this->discount, currentCurrency())
                )
            )
        );
    }
    public function totalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice(PrecisionMath::mul($this->num, $this->price), currentCurrency())
        );
    }

    public function addPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->add_price, currentCurrency())
        );
    }

    public function originalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn () => PrecisionMath::toFloat(
                PrecisionMath::mul(convertPrice($this->original_price, currentCurrency()), $this->num)
            )
        );
    }

    public function discountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->discount, currentCurrency())
        );
    }

    public function priceAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->price, currentCurrency())
        );
    }

    /**************** 关联模型 ****************/
    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function userCoupon(): BelongsTo
    {
        return $this->belongsTo(UserCoupon::class);
    }

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    public function userExperienceActivity(): BelongsTo
    {
        return $this->belongsTo(UserExperienceActivity::class);
    }
}
