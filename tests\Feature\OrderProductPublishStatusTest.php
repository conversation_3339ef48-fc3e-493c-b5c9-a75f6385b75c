<?php

namespace Tests\Feature;

use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderProductPublishStatusTest extends TestCase
{
    use RefreshDatabase;

    public function test_product_variant_shows_current_publish_status()
    {
        // 创建商品
        $product = Product::create([
            'is_publish' => true,
            'title' => 'Test Product',
            'slug_title' => 'test-product',
            'keywords' => 'test',
            'make_at' => now()->format('Y-m-d'),
            'brand_id' => 1,
            'category_id' => 1,
            'style_id' => 1,
            'material_id' => 1,
            'image_id' => 1,
        ]);

        // 创建商品变体
        $productVariant = ProductVariant::create([
            'product_id' => $product->id,
            'is_publish' => true,
            'price' => 100.00,
            'original_price' => 120.00,
            'stock' => 10,
            'color_attribute_value_id' => 1,
            'size_attribute_value_id' => 1,
        ]);

        // 验证商品当前是发布状态
        $this->assertTrue($productVariant->is_publish_current);

        // 下架商品
        $product->update(['is_publish' => false]);

        // 重新加载关系
        $productVariant->load('product');

        // 验证商品现在显示为未发布状态
        $this->assertFalse($productVariant->is_publish_current);
    }

    public function test_product_variant_shows_false_when_variant_not_published()
    {
        // 创建商品
        $product = Product::create([
            'is_publish' => true,
            'title' => 'Test Product',
            'slug_title' => 'test-product-2',
            'keywords' => 'test',
            'make_at' => now()->format('Y-m-d'),
            'brand_id' => 1,
            'category_id' => 1,
            'style_id' => 1,
            'material_id' => 1,
            'image_id' => 1,
        ]);

        // 创建未发布的商品变体
        $productVariant = ProductVariant::create([
            'product_id' => $product->id,
            'is_publish' => false,
            'price' => 100.00,
            'original_price' => 120.00,
            'stock' => 10,
            'color_attribute_value_id' => 1,
            'size_attribute_value_id' => 1,
        ]);

        // 即使商品是发布状态，但变体未发布，应该返回false
        $this->assertFalse($productVariant->is_publish_current);
    }
}
