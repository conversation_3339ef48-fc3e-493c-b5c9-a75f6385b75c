<?php

namespace App\Filters;

use App\Models\Enums\EquipmentTypeEnum;
use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class EquipmentTypeFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        if ($value !== '') {
            $allEquipment = EquipmentTypeEnum::AllEquipment;

            if ($value === $allEquipment) {
                return $query; // 如果 value 已经是 AllEquipment，直接返回查询
            }

            return $query->whereIn($property, [$value, $allEquipment]);
        }
        return $query;
    }
}
