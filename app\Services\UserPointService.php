<?php

namespace App\Services;

use App\Models\User\User;
use App\Models\User\UserPointChangeRecord;
use App\Models\Enums\User\PointChangeTypeEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Jobs\Order\ProcessDelayedMembershipPoints;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserPointService
{
    /**
     * 增加用户积分
     */
    public function addPoints(
        User $user,
        float $points,
        PointChangeTypeEnum $changeType,
        string $description,
        $source = null,
        bool $immediate = true,
        ?Carbon $scheduledAt = null
    ): UserPointChangeRecord {
        // 确保用户有钱包
        if (!$user->wallet) {
            $user->wallet()->create([
                'point' => 0,
                'invite_amount' => 0,
            ]);
            $user->refresh();
        }

        $balanceBefore = $user->wallet->point;
        $status = $immediate ? 'completed' : 'pending';
        $balanceAfter = $immediate ? $balanceBefore + $points : $balanceBefore;

        DB::beginTransaction();
        try {
            // 如果是立即发放，更新钱包余额
            if ($immediate) {
                $user->wallet->increment('point', $points);
            }

            // 创建积分变化记录
            $record = UserPointChangeRecord::createRecord(
                userId: $user->id,
                changeAmount: $points,
                balanceBefore: $balanceBefore,
                balanceAfter: $balanceAfter,
                changeType: $changeType->value,
                description: $description,
                source: $source,
                status: $status,
                scheduledAt: $scheduledAt
            );

            // 如果是延迟发放，调度 Job 在指定时间处理
            if (!$immediate && $scheduledAt) {
                ProcessDelayedMembershipPoints::dispatch($record)->delay($scheduledAt);
            }

            DB::commit();
            return $record;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 扣除用户积分
     */
    public function deductPoints(
        User $user,
        float $points,
        PointChangeTypeEnum $changeType,
        string $description,
        $source = null,
        ?string $couponCode = null,
        ?string $couponName = null
    ): UserPointChangeRecord {
        // 确保用户有钱包
        if (!$user->wallet) {
            throw new \Exception('用户钱包不存在');
        }

        $balanceBefore = $user->wallet->point;
        
        if ($balanceBefore < $points) {
            throw new \Exception('积分余额不足');
        }

        $balanceAfter = $balanceBefore - $points;

        DB::beginTransaction();
        try {
            // 扣除钱包积分
            $user->wallet->decrement('point', $points);

            // 创建积分变化记录
            $record = UserPointChangeRecord::createRecord(
                userId: $user->id,
                changeAmount: -$points, // 负数表示减少
                balanceBefore: $balanceBefore,
                balanceAfter: $balanceAfter,
                changeType: $changeType->value,
                description: $description,
                source: $source,
                status: 'completed',
                couponCode: $couponCode,
                couponName: $couponName
            );

            DB::commit();
            return $record;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 处理待发放的积分
     */
    public function processPendingPoints(UserPointChangeRecord $record): void
    {
        if (!$record->isPending()) {
            return;
        }

        $user = $record->user;
        if (!$user->wallet) {
            $record->markAsFailed();
            return;
        }

        DB::beginTransaction();
        try {
            // 更新钱包余额
            $user->wallet->increment('point', $record->change_amount);
            
            // 更新记录状态和余额
            $record->update([
                'balance_after' => $user->wallet->fresh()->point,
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $record->markAsFailed();
            throw $e;
        }
    }

    /**
     * 获取用户积分统计
     */
    public function getUserPointStats(User $user): array
    {
        $records = UserPointChangeRecord::byUser($user->id);
        
        return [
            'current_points' => $user->wallet ? $user->wallet->point : 0,
            'total_earned' => $records->increase()->completed()->sum('change_amount'),
            'total_spent' => abs($records->decrease()->completed()->sum('change_amount')),
            'pending_points' => $records->pending()->sum('change_amount'),
            'this_month_earned' => $records->increase()->completed()
                ->whereMonth('completed_at', now()->month)
                ->sum('change_amount'),
            'this_year_earned' => $records->increase()->completed()
                ->whereYear('completed_at', now()->year)
                ->sum('change_amount'),
        ];
    }

    /**
     * 批量处理到期的待发放积分
     */
    public function processDuePoints(): int
    {
        $dueRecords = UserPointChangeRecord::pending()
            ->where('scheduled_at', '<=', now())
            ->get();

        $processedCount = 0;
        foreach ($dueRecords as $record) {
            try {
                $this->processPendingPoints($record);
                $processedCount++;
            } catch (\Exception $e) {
                logger()->error('处理待发放积分失败', [
                    'record_id' => $record->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $processedCount;
    }
}
