<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum CartTypeEnum: int
{
    case Normal = 1;
    case Temp = 2;

    public function label(): string
    {
        return match($this) {
            self::Normal => '正常',
            self::Temp => '临时',
        };
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Normal->value => '正常购物车',
            self::Temp->value => '临时购物车',
        ];
    }
} 