<?php

namespace App\Models\Enums\Order;

use Illuminate\Support\Arr;

enum OrderPaidStatusEnum: int
{
    case  Unpaid = 0;
    case  Paid = 1;

    public function desc(): string
    {
//        return match ($this) {
//            self::StatusEnable => "开启",
//            self::StatusDisabled => "禁用",
//        };
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Unpaid->value => '未付款',
            self::Paid->value => '已付款',
        ];
    }

}
