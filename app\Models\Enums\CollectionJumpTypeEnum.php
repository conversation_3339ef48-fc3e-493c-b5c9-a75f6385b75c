<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum CollectionJumpTypeEnum: int
{
    case None = 0;
    case Collection = 1;
    case Link = 2;


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::None->value => '无跳转',
            self::Collection->value => '集合详情',
            self::Link->value => 'url外链',
        ];
    }
}
