<?php

namespace App\Http\Controllers;

use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Http\Resources\UserAddressResource;
use App\Models\Country;
use App\Models\User\User;
use App\Models\User\UserAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\QueryBuilder;

class UserAddressController extends Controller
{
    //


    public function index(Request $request): JsonResource
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(UserAddress::class)
            ->with(['country:id,name,iso_code'])
            ->where('user_id', $user->id)
            ->defaultSort('-is_both_default')
            ->paginate($this->getPerPage());
        return UserAddressResource::collection($builder);
    }


    public function show(UserAddress $address)
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        if ($user->id !== $address->user_id) {
            throw  new DataException('Access route does not exist.', ErrorCode::HttpNotFound);
        }
        return UserAddressResource::make($address);
    }


    public function store(Request $request): UserAddressResource
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:64',
            'last_name' => 'required|string|max:64',
            'country_id' => ['required', new Exists(Country::class, 'id')],
            'address' => 'required|string|max:512',
            'address1' => 'nullable|max:512',
            'state' => 'string|max:512',
            'city' => 'required|max:512',
            'zip' => 'required|max:512',
            'is_billing_default' => 'required|bool',
            'is_shipping_default' => 'required|bool',
            'phone' => 'required|string|max:32',
        ]);
        /**
         * @var $user User
         */
        $user = Auth::user();
        $is_billing_default = Arr::get($validated, 'is_billing_default');
        $is_shipping_default = Arr::get($validated, 'is_shipping_default');
        
        $address = $user->addresses()->create([
            ...$validated,
            'is_both_default' => $is_billing_default && $is_shipping_default
        ]);
        
        if ($is_billing_default) {
            $user->addresses()
                ->where('id', '!=', $address->id)
                ->update([
                    'is_billing_default' => false,
                    'is_both_default' => false
                ]); 
        }

        if ($is_shipping_default) {
            $user->addresses()
                ->where('id', '!=', $address->id)
                ->update([
                    'is_shipping_default' => false,
                    'is_both_default' => false
                ]); 
        }
        return UserAddressResource::make($address);
    }


    public function update(Request $request, UserAddress $address): UserAddressResource
    {
        $validated = $request->validate([
            'first_name' => 'string|max:64',
            'last_name' => 'string|max:64',
            'country_id' => [new Exists(Country::class, 'id')],
            'address' => 'nullable|string|max:512',
            'address1' => 'nullable|max:512',
            'state' => 'string|max:512',
            'city' => 'max:512',
            'zip' => 'max:512',
            'is_billing_default' => 'bool',
            'is_shipping_default' => 'bool',
            'phone' => 'string|max:32',
        ]);
        $user = Auth::user();
        if ($user->id !== $address->user_id) {
            throw  new DataException('Access route does not exist.', ErrorCode::HttpNotFound);
        }
        
        $is_billing_default = Arr::get($validated, 'is_billing_default');
        $is_shipping_default = Arr::get($validated, 'is_shipping_default');
        
        $address->update([
            ...$validated,
            'is_both_default' => $is_billing_default && $is_shipping_default
        ]);
        
        if ($is_billing_default) {
            $user->addresses()
                ->where('id', '!=', $address->id)
                ->update([
                    'is_billing_default' => false,
                    'is_both_default' => false
                ]); 
        }

        if ($is_shipping_default) {
            $user->addresses()
                ->where('id', '!=', $address->id)
                ->update([
                    'is_shipping_default' => false,
                    'is_both_default' => false
                ]); 
        }
        return UserAddressResource::make($address);

    }


    public function destroy(UserAddress $address): JsonResponse
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        if ($user->id !== $address->user_id) {
            throw  new DataException('Access route does not exist.', ErrorCode::HttpNotFound);
        }
        $address->delete();
        return response()->json([]);
    }

}
