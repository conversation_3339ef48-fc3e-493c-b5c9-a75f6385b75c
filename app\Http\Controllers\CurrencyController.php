<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\Enums\CurrencyEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class CurrencyController extends Controller
{
    /**
     * 列表
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Currency::class, $request)
            ->select('currencies.*', \DB::raw("CONCAT(currency_code, ' ', symbol) AS currency_code_symbol"))
            ->allowedFilters([
                AllowedFilter::partial('currency_name'),
                AllowedFilter::exact('currency_code'),
                AllowedFilter::exact('is_default'),
            ])
            ->where('active',1)
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $currencies = $builder->get();
        return JsonResource::collection($currencies);
    }
}
