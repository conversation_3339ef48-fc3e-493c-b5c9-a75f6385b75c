<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_task_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('task_config_id')->comment('任务配置ID');
            $table->string('task_type', 50)->comment('任务类型');
            $table->string('channel', 50)->nullable()->comment('渠道');
            $table->decimal('earned_points', 10, 2)->comment('获得积分');
            $table->json('task_data')->nullable()->comment('任务相关数据');
            $table->string('external_id')->nullable()->comment('外部ID(如帖子ID、评论ID等)');
            $table->timestamp('completed_at')->comment('完成时间');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('task_config_id')->references('id')->on('task_point_configs')->onDelete('cascade');
            
            $table->index(['user_id', 'task_type', 'completed_at']);
            $table->index(['user_id', 'task_config_id']);
            $table->index('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_task_records');
    }
};
