<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_experience_activities', function (Blueprint $table) {
            $table->id();
            $table->string('activity_name', 255)->comment('活动名称');
            $table->string('description', 1024)->nullable()->comment('活动描述');
            $table->boolean('is_listed')->default(0)->comment('是否上架：1 上架，0 下架');
            $table->boolean('is_coupon')->default(0)->comment('是否使用优惠券：1 使用，0 不使用');
            $table->boolean('is_first')->default(0)->comment('是否首次：1 首次，0 不是首次');
            $table->boolean('is_register')->default(0)->comment('是否注册用户购买：1 注册，0 未注册');
            $table->boolean('is_automatic_listed')->default(0)->comment('是否自动下架过：1 自动下架过，0 未自动下架过');
            $table->bigInteger('admin_user_id')->nullable()->index()->comment('更新人');
            $table->timestamp('start_at')->nullable()->comment('活动开始时间');
            $table->timestamp('end_at')->nullable()->comment('活动结束时间');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('用户体验计划');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_experience_activities');
    }
};
