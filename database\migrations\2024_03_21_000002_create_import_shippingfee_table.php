<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_shippingfee', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('file', 255)->comment('文件');
            $table->tinyInteger('status')->default(0)->comment('状态:0-待处理,1-处理中,2-处理完成,3-处理失败');
            $table->text('error_log')->nullable()->comment('错误日志');
            $table->bigInteger('admin_user_id')->comment('操作人id');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->index('admin_user_id', 'import_shippingFee_admin_user_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_shippingfee');
    }
}; 