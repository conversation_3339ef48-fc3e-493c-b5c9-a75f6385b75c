<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seo_page_settings', function (Blueprint $table) {
            $table->id();
            $table->string('page')->default("");
            $table->string('title', 255)->default("");
            $table->string('keywords', 1024)->default("");
            $table->string('description', 512)->default("");

            $table->engine('InnoDB');
            $table->comment('页面SEO管理');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seo_page_settings');
    }
};
