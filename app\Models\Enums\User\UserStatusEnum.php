<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum UserStatusEnum: int
{
    case  StatusDisabled = 0;
    case  StatusEnable = 1;
    case CreditRisk = 2;        // 积分风险
    case WithdrawalRisk = 3;    // 提现风险
    case DualRisk = 4;          // 积分 + 提现双重风险

    public function desc(): string
    {
//        return match ($this) {
//            self::StatusEnable => "开启",
//            self::StatusDisabled => "禁用",
//        };
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::StatusEnable->value => 'Enable',
            self::StatusDisabled->value => 'Disabled',
            self::CreditRisk->value => 'Credit Risk',//积分风险
            self::WithdrawalRisk->value => 'Withdrawal Risk',//提现风险
            self::DualRisk->value => 'Dual Risk',//双重风险
        ];
    }

}
