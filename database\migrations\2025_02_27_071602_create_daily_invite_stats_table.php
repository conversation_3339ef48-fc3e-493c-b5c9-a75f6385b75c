<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_invite_stats', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('email')->index()->comment('邮箱');
            $table->date('date')->index()->comment('日期');
            $table->integer('share_count')->default(0)->comment('分享次数');
            $table->integer('exclusive_fans_count')->default(0)->comment('专属粉丝数');
            $table->integer('ordinary_fans_count')->default(0)->comment('普通粉丝数');
            $table->integer('total_fans_count')->default(0)->comment('总粉丝数');
            $table->integer('estimated_count')->default(0)->comment('预估次数');
            $table->integer('estimated_amount')->default(0)->comment('预估金额');
            $table->integer('withdraw_count')->default(0)->comment('提现人数');
            $table->integer('withdraw_amount')->default(0)->comment('提现金额');
            $table->comment('每日邀请统计表');
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_invite_stats');
    }
};
