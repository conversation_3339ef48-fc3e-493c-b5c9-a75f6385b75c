<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->id();
            $table->string('title', 32)->nullable()->comment('标题');
            $table->string('description', 128)->nullable()->comment('描述');
            $table->string('button_text', 32)->nullable()->comment('按钮文字');
            $table->string('button_link')->nullable()->comment('按钮链接');
            $table->string('button_background_color', 32)->nullable()->comment('按钮背景颜色');
            $table->string('position')->comment('位置,如：index_top,index_bottom,index_middle...');
            $table->tinyInteger('alignment_method')->default(1)->comment('对齐方式：1-居中,2-居左,3-居右');
            $table->tinyInteger('type')->comment('类型：1-分类,2-商品,3-套盒,4-设计');
            $table->boolean('status')->default(1)->comment('状态：0-禁用,1-启用');
            $table->Integer('sort')->default(0)->comment('排序');
            $table->tinyInteger('equipment_type')->default(1)->comment('设备类型：1-PC, 2-移动端, 3-所有');
            $table->timestamps();
            $table->softDeletes();
            $table->engine('InnoDB');
            $table->comment('广告推荐');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotions');
    }
};
