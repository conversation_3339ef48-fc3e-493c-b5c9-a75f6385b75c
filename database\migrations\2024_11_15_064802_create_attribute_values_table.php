<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attribute_values', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('attribute_id')->index()->comment('属性id');
            $table->string('value', 255)->comment('值');
            $table->integer('sort')->default(100)->comment('排序');
            $table->integer('attachment_id')->index()->default(0)->comment('色卡图片ID');
            $table->json('extra')->nullable()->comment('附加值');
            $table->timestamps();
            $table->comment('变种值(额外的)');
            $table->engine('InnoDB');


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attribute_values');
    }
};
