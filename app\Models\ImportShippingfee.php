<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ImportShippingfee extends Model
{
    use SoftDeletes;
    use HasFactory;
    protected $guarded = [];

    public static array $header = [
        'country',
        'weight_from',
        'weight_to',
        'shipping_fee',
        'delivery_type'
    ];

    public function adminUser()
    {
        return $this->belongsTo(AdminUser::class);
    }
} 