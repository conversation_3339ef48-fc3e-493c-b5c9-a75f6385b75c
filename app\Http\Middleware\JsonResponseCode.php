<?php

namespace App\Http\Middleware;

use App\Constants\ErrorCode;
use App\Exceptions\BaseException;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class JsonResponseCode
{

    public function handle(Request $request, Closure $next)
    {
        /**
         * @var  \Illuminate\Http\JsonResponse $response
         */
        $response = $next($request);
        // API 增加code
        if ($response->headers->get('content-type') == 'application/json') {
            if (!$response->exception instanceof BaseException) {
                if ($response instanceof JsonResponse) {
                    $rawData['data'] = $response->getData(true);
                } else {
                    $rawData['data'] = json_decode($response->getContent(), true);
                }
                $code = $response->getStatusCode();
                // 不需要201等
                $rawData['code'] = match ($code) {
                    201, 202, 203, 204 => 200,
                    default => $code
                };
                if ($response->exception instanceof \Throwable) {
                    $rawData['message'] = $response->exception->getMessage();
                }
                if ($response instanceof JsonResponse) {
                    $response->setData($rawData);
                } else {
                    $response->setContent(json_encode($rawData));
                }
            }
            $response->setStatusCode(200);
        }
        return $response;
    }
}
