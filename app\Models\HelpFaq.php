<?php

namespace App\Models;

use App\Constants\CacheKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

class HelpFaq extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected static function booted(): void
    {
        parent::booted();
        static::created(function () {
            Cache::forget(CacheKey::HelpFaqList->getKey());
        });
        static::saved(function () {
            Cache::forget(CacheKey::HelpFaqList->getKey());
        });
    }

    public function icon(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'icon_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(HelpFaq::class, 'parent_id');
    }
}
